name: staging-nightly

on:
  workflow_dispatch:
    inputs:
      databaseBackupName:
        description: 'The backup name to restore'
        required: false
      databaseBranchName:
        description: 'The name of branch backup to restore (latest)'
        required: false
        default: 'main'
      outputBranchPrefix:
        description: 'The prefix for the output branch'
        required: false
        default: 'nightly'
      databaseName:
        description: 'The name of the database to restore'
        required: false
        default: 'memberup-production'
      communitySlugs:
        description: 'Comma-separated list of community slugs to process'
        required: false
        default: 'automated-testing,manual-testing'
      runRestorePlanetScaleBackup:
        description: 'Whether to Restore PlanetScale Backup'
        required: false
        default: 'true'
      runGetStreamRestore:
        description: 'Whether to run GetStream migration'
        required: false
        default: 'true'
      runKnockConfiguration:
        description: 'Whether to run Knock configuration'
        required: false
        default: 'true'
      runStripeConfig:
        description: 'Whether to run the Stripe Configuration'
        required: false
        default: 'true'
#  schedule:
#    - cron: '0 21 * * *'

jobs:
  build_and_test:
    name: Build and Test Job
    runs-on: ubuntu-latest
    env:
      DOPPLER_PROJECT: memberup
      DOPPLER_CONFIG: staging
    steps:
      - name: Check out code from the repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Setup Node.js environment
        uses: actions/setup-node@v2
        with:
          node-version: 18
          cache: "yarn"

      - name: Cache Node modules
        uses: actions/cache@v2
        with:
          path: |
            node_modules
            packages/*/node_modules
            apps/*/node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install project dependencies
        run: yarn

      - name: Install Doppler CLI
        uses: DopplerHQ/cli-action@v2

      - name: Build the project
        run: doppler run -- yarn build
        env:
          DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN_STAGING }}

      - name: Generate semantic version
        uses: PaulHatch/semantic-version@v5.0.3

  deploy_to_staging:
    name: Deploy to Staging Environment
    needs: build_and_test
    if: github.event_name == 'workflow_dispatch' || github.event_name == 'schedule'
    runs-on: ubuntu-latest
    env:
      DOPPLER_PROJECT: memberup
      DOPPLER_CONFIG: staging
      COMMUNITY_SLUGS: ${{ github.event.inputs.communitySlugs || 'automated-testing,manual-testing' }}
      DATABASE_BRANCH_NAME: ${{ github.event.inputs.databaseBranchName || 'main' }}
      DATABASE_BACKUP_NAME: ${{ github.event.inputs.databaseBackupName }}
      DATABASE_NAME: ${{ github.event.inputs.databaseName || 'memberup-production' }}
      OUTPUT_BRANCH_PREFIX: ${{ github.event.inputs.outputBranchPrefix || 'nightly' }}
      RUN_PLANETSCALE_RESTORE: ${{ github.event.inputs.runRestorePlanetScaleBackup || 'true' }}
      RUN_GETSTREAM_RESTORE: ${{ github.event.inputs.runGetStreamRestore || 'true' }}
      GETSTREAM_ORGANIZATION: ${{ github.event.inputs.organization || '1085403' }}
      GETSTREAM_TEMPLATE_APP_NAME: 'MemberUp Staging'
      GETSTREAM_APP_NAME_TO_CREATE: 'MemberUp Staging Clone'
      RUN_KNOCK_CONFIGURATION: ${{ github.event.inputs.runKnockConfiguration || 'true' }}
      RUN_STRIPE_CONFIGURATION: ${{ github.event.inputs.runStripeConfig || 'true' }}

    steps:
      - name: Check out code from the repository
        uses: actions/checkout@v2

      - name: Setup Node.js environment
        uses: actions/setup-node@v2
        with:
          node-version: 18

      - name: Cache Node modules (restore cache)
        uses: actions/cache@v2
        with:
          path: |
            node_modules
            packages/*/node_modules
            apps/*/node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install project dependencies (if cache miss)
        run: yarn

      - name: Install Doppler CLI
        uses: DopplerHQ/cli-action@v2

      - name: Restore PlanetScale Backup and Update Doppler
        if: env.RUN_PLANETSCALE_RESTORE == 'true'
        run: |
          doppler run -- node apps/memberup/scripts/restore-planetscale-backup.js --outputBranchPrefix="${{ env.OUTPUT_BRANCH_PREFIX }}" --branchName="${{ env.DATABASE_BRANCH_NAME }}" --backupName="${{ env.DATABASE_BACKUP_NAME }}" --databaseName="${{ env.DATABASE_NAME }}"
          CONNECTION_STRING=$(cat ./db_connection_string.txt)
          doppler secrets set DATABASE_URL="$CONNECTION_STRING" --project memberup --config staging
        env:
          DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN_STAGING }}

      - name: Cleanup Database Connection String File
        run: rm -f ./db_connection_string.txt
        if: env.RUN_PLANETSCALE_RESTORE == 'true'

      - name: Create GetStream Environment
        if: env.RUN_GETSTREAM_RESTORE == 'true'
        run: doppler run -- apps/memberup/scripts/create-getstream-environment.sh
        env:
          DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN_STAGING }}

      - name: Update GetStream Environment
        if: env.RUN_GETSTREAM_RESTORE == 'true'
        run: doppler run -- node apps/memberup/scripts/update-getstream-staging.js --community-slugs="${{ env.COMMUNITY_SLUGS }}"
        env:
          DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN_STAGING }}

      - name: Update users emails and memberships table
        if: env.RUN_KNOCK_CONFIGURATION == 'true'
        run: |
          doppler run -- node apps/memberup/scripts/update-users-email-for-staging.js
          doppler run -- node apps/memberup/scripts/update-memberships-table-for-staging.js
        env:
          DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN_STAGING }}

      - name: Update Knock
        if: env.RUN_KNOCK_CONFIGURATION == 'true'
        run: |
          doppler run -- node apps/memberup/scripts/knock-reset-account.js --yes --community-slugs="${{ env.COMMUNITY_SLUGS }}"
          doppler run -- node apps/memberup/scripts/knock-add-tenants.js --yes --community-slugs="${{ env.COMMUNITY_SLUGS }}"
          doppler run -- node apps/memberup/scripts/knock-add-objects.js --yes --community-slugs="${{ env.COMMUNITY_SLUGS }}"
          doppler run -- node apps/memberup/scripts/knock-subscribe-objects.js --yes --community-slugs="${{ env.COMMUNITY_SLUGS }}"
        env:
          DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN_STAGING }}

      - name: Replace Community Stripe Settings
        if: env.RUN_STRIPE_CONFIGURATION == 'true'
        run: doppler run -- node apps/memberup/scripts/replace-stripe-settings.js --yes
        env:
          DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN_STAGING }}

      - name: Trigger Vercel Deployment
        run: |
          curl -X POST ${{ secrets.VERCEL_DEPLOY_HOOK_URL_STAGING }}