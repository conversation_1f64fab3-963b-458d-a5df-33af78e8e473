name: tests-e2e
on: [deployment_status]
jobs:
  e2e:
    if: github.event.deployment_status.state == 'success' && !contains(github.event.deployment_status.log_url, 'signup')
    runs-on: ubuntu-latest
    env:
      DOPPLER_PROJECT: 'memberup'
      SKIP_TESTS: 'true'
    steps:
      - name: Check out code
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Detect branch name from deployment status
        run: |
          BRANCH_NAME=$(git show -s --pretty=%D HEAD | tr -s ',' '\n' | sed 's/^ //' | grep -e 'origin/' | head -1 | sed 's/\origin\///g')
          # NOTE: This is a workaround for the case where the branch name is not detected
          if [ -z "$BRANCH_NAME" ]; then
            BRANCH_NAME="staging"
          fi
          echo "BRANCH_NAME=${BRANCH_NAME}" >> $GITHUB_ENV
          echo "$BRANCH_NAME"

      - name: Check if branch is development or staging
        run: |
          echo "log_url" ${{github.event.deployment_status.log_url}}
          if [ $BRANCH_NAME == "development" ]; then
              echo "SKIP_STEPS=false" >> $GITHUB_ENV
              echo "DOPPLER_CONFIG=dev" >> $GITHUB_ENV
              echo "DOPPLER_TOKEN=${{ secrets.DOPPLER_TOKEN }}" >> $GITHUB_ENV
          elif [ $BRANCH_NAME == "staging" ]; then
              echo "SKIP_STEPS=false" >> $GITHUB_ENV
              echo "DOPPLER_CONFIG=staging" >> $GITHUB_ENV
              echo "DOPPLER_TOKEN=${{ secrets.DOPPLER_TOKEN_STAGING }}" >> $GITHUB_ENV
          fi

      - name: Setup Node.js environment
        uses: actions/setup-node@v2
        with:
          node-version: 18

      - name: Install dependencies
        run: yarn

      - name: Install Doppler CLI
        uses: DopplerHQ/cli-action@v2

      - name: Test doppler
        run: doppler --print-config

      - name: Set environment variables from Doppler
        if: env.SKIP_STEPS != 'true'
        run: |
          doppler secrets download --no-file --format env-no-quotes >> $GITHUB_ENV

      - name: E2E Test via Checkly
        id: run-checks
        if: env.SKIP_STEPS != 'true'
        continue-on-error: true
        run: |
          doppler run yarn test-e2e -- -e PLAYWRIGHT_APP_DOMAIN="${{ env.PLAYWRIGHT_APP_DOMAIN }}" \
            -e PLAYWRIGHT_APP_PROTOCOL="${{ env.PLAYWRIGHT_APP_PROTOCOL }}" \
            -e PLAYWRIGHT_COMMUNITY_1_ADMIN_USER_1_EMAIL="${{ env.PLAYWRIGHT_COMMUNITY_1_ADMIN_USER_1_EMAIL }}" \
            -e PLAYWRIGHT_COMMUNITY_1_ADMIN_USER_1_PASSWORD="${{ env.PLAYWRIGHT_COMMUNITY_1_ADMIN_USER_1_PASSWORD }}" \
            -e PLAYWRIGHT_COMMUNITY_1_DOMAIN="${{ env.PLAYWRIGHT_COMMUNITY_1_DOMAIN }}" \
            -e PLAYWRIGHT_COMMUNITY_1_MEMBER_USER_1_EMAIL="${{ env.PLAYWRIGHT_COMMUNITY_1_MEMBER_USER_1_EMAIL }}" \
            -e PLAYWRIGHT_COMMUNITY_1_MEMBER_USER_1_PASSWORD="${{ env.PLAYWRIGHT_COMMUNITY_1_MEMBER_USER_1_PASSWORD }}" \
            --record --reporter=github

      - name: Create summary # export the markdown report to the job summary.
        if: env.SKIP_STEPS != 'true' && (failure() || cancelled())
        run: cat checkly-github-report.md > $GITHUB_STEP_SUMMARY

      - name: Deploy checks # if the test run was successful and we are on Production, deploy the checks
        id: deploy-checks
        if: steps.run-checks.outcome == 'success' && github.event.deployment_status.environment == 'Production'
        run: npx checkly deploy --force

      - name: Determine Slack Users to Mention
        if: env.SKIP_STEPS != 'true' && (failure() || cancelled())
        run: |
          echo "SLACK_USERS=$(node ./apps/memberup/scripts/getSlackId.js)" >> $GITHUB_ENV
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Notify Slack on Failure or Cancellation
        if: env.SKIP_STEPS != 'true' && (failure() || cancelled())
        uses: ravsamhq/notify-slack-action@v2
        with:
          status: ${{ job.status }}
          notification_title: 'E2E Tests - ${{ job.status }} on ${{ github.ref }} - <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Run>'
          message_format: ':fire: *E2E Tests* ${{ job.status }} in <${{ github.server_url }}/${{ github.repository }}/${{ github.ref }}|${{ github.repository }}>'
          mention_users: ${{ env.SLACK_USERS }}
          footer: 'Linked Repo <${{ github.server_url }}/${{ github.repository }}|${{ github.repository }}> | <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Run>'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
