# MemberUp Monorepo

This is the MemberUp Main monorepo which consists of `apps/memberup` (main app), and `packages/shared` (shared logic and components).

## How to install/run

- We use yarn and yarn workspaces. Run `yarn rebootstrap` to install all dependencies.
- We also use doppler for secret management. Install [Doppler](https://doppler.com/) CLI and run `doppler setup` to login and configure your local environment. By default, the project will use the `dev` environment.
- Run `doppler run yarn dev` to start the development server. The outputs will be available at `http://localhost:3000` (main app) and `http://start.localhost:3001` (signup app).

## How to commit/deploy

- We use [Vercel](https://vercel.com/) configed to deploy from a push to git.
- Create a new branch off of `development` and make your changes.
- Run `yarn commit` to commit your changes. This will run `commitizen` to create a commit message.
- We use husky for pre-commit hooks to run linting, build and tests.
- Push your branch to GitHub and create a pull request to merge into `development`.

## Items to add

- discuss folder structure
- discuss MUI
- discuss testing
- discuss yarn:seed
- discuss archetecture
- discuss prisma structure
- discuss apis
- discuss auth

## Running e2e tests

### Running tests locally

### Unit Tests

```
doppler run yarn test-jest:ci
```

### e2e Tests

```
PLAYWRIGHT_APP_DOMAIN="localhost:3000" \
PLAYWRIGHT_APP_PROTOCOL="HTTP" \
doppler run --preserve-env -- npx playwright test --ui
```

### Running tests on Checkly

```

```
