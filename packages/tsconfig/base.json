{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"target": "es5", "allowJs": false, "composite": false, "declaration": false, "declarationMap": false, "inlineSources": false, "noUnusedLocals": false, "noUnusedParameters": false, "noEmit": true, "noImplicitAny": false, "strictNullChecks": false, "preserveSymlinks": true, "module": "esnext", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "incremental": true, "isolatedModules": true, "jsx": "preserve", "resolveJsonModule": true, "skipLibCheck": true, "strict": false, "lib": ["dom", "dom.iterable", "esnext"]}, "exclude": ["node_modules"]}