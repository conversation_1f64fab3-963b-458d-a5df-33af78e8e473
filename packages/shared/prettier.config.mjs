// Based on: https://github.com/t3-oss/create-t3-app/blob/main/www/prettier.config.mjs

import baseConfig from '../../prettier.config.mjs';

/**
 * @type {import('prettier').Config & import('prettier-plugin-tailwindcss').PluginOptions &
 *       import('@ianvs/prettier-plugin-sort-imports').PluginConfig}
 */
const config = {
  ...baseConfig,
  plugins: [
    '@ianvs/prettier-plugin-sort-imports',
    'prettier-plugin-tailwindcss', // MUST come last
  ],
  tailwindConfig: '../../apps/memberup/tailwind.config.ts',
  importOrder: ['<THIRD_PARTY_MODULES>', '', '^[.][.]/', '^[.]/', '^@memberup/', '^@/', "^(?!.*[.]css$)[./].*$", ".css$"],
  importOrderParserPlugins: ['typescript', 'jsx', 'decorators-legacy'],
  importOrderTypeScriptVersion: '4.4.0',
}

export default config
