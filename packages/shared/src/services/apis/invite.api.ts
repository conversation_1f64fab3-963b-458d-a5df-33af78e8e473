import { baseApi } from './base.api'
import { USER_ROLE_ENUM } from '@/shared-types/enum'

export const inviteApi = (
  data: {
    members: { email: string }[]
    message: string
    role?: USER_ROLE_ENUM
  },
  membershipId: string,
) => {
  return baseApi().post(`/api/invite?membership_id=${membershipId}`, data)
}

export const inviteVerifyTokenApi = (invite_token: string, membership_id: string) => {
  return baseApi().post(`/api/invite/verify-token`, { invite_token, membership_id })
}
