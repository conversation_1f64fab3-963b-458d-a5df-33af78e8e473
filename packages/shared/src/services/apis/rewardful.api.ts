import { baseApi } from './base.api'

export const getRewardfulCommissionsApi = (params: {
  limit?: number // 100 is the max
  page?: number
  state?: string[] | string // ['pending' | 'due' | 'paid']
  expand?: string[] | string // ['sale' | 'campaign']
  all?: boolean
}) => {
  return baseApi().get(`/api/rewardful/commissions`, {
    params,
  })
}

export const getRewardfulAffiliateSSOApi = () => {
  return baseApi().get(`/api/rewardful/affiliate-sso`)
}

export const getRewardfulAffiliateApi = () => {
  return baseApi().get(`/api/rewardful/affiliate`)
}
