import { baseApi } from './base.api'
import { IFeed, IReaction } from '@/shared-types/interfaces'
import { TGetApiParams } from '@/shared-types/types'

export const createFeedApi = (payload: Partial<IFeed>, membershipId: string) => {
  const enhancedPayload: Partial<IFeed> & { membership_id: string } = {
    ...payload,
    membership_id: membershipId,
  }
  return baseApi().post(`/api/feed`, enhancedPayload)
}

export const geFeedApi = (id: string) => {
  return baseApi().get(`/api/feed/${id}`)
}

export const getFeedsCountApi = ({ where }: TGetApiParams) => {
  return baseApi().get(`/api/feed/count`, { params: { where } })
}

export const updateFeedApi = (id: string, payload: Partial<IFeed>) => {
  return baseApi().put(`/api/feed/${id}`, payload)
}

export const reportFeedApi = (id: string, payload: Partial<IFeed>) => {
  return baseApi().put(`/api/feed/${id}/report`, payload)
}

export const deleteFeedApi = (id: string) => {
  return baseApi().delete(`/api/feed/${id}`)
}

export const createReactionApi = (payload: Partial<IReaction>) => {
  return baseApi().post(`/api/reaction`, payload)
}

export const createLikeApi = (id: string, membership_id: string) => {
  return baseApi().put(`/api/feed/${id}/like`, { membership_id })
}

export const deleteLikeApi = (id: string, membership_id: string) => {
  return baseApi().delete(`/api/feed/${id}/like`, { membership_id })
}

export const geReactionApi = (id: string) => {
  return baseApi().get(`/api/reaction/${id}`)
}

export const getReactionsApi = ({ where, take, skip }: TGetApiParams) => {
  return baseApi().get(`/api/reaction`, { params: { where, take, skip } })
}

export const updateReactionApi = (id: string, payload: Partial<IReaction>) => {
  return baseApi().put(`/api/reaction/${id}`, payload)
}

export const deleteReactionApi = (id: string, operationType: string, awardedUserId: string) => {
  return baseApi().delete(`/api/reaction/${id}?operation_type=${operationType}&awarded_user_id=${awardedUserId}`)
}

export const getSimilarSlugs = (slug: string) => {
  return baseApi().get(`/api/slug/${slug}`)
}
