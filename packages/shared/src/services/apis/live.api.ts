import { baseApi } from './base.api'
import { ILive } from '@/shared-types/interfaces'
import { TCreateLiveStreamTokenPayload, TGetApiParams } from '@/shared-types/types'

export const createLiveApi = (payload: Partial<ILive>) => {
  return baseApi().post(`/api/live`, payload)
}

export const getLivesApi = ({ where, take, skip }: TGetApiParams) => {
  return baseApi().get(`/api/live`, { params: { where, take, skip } })
}

export const getLiveApi = (id: string) => {
  return baseApi().get(`/api/live/${id}`)
}

export const getLatestLiveApi = () => {
  return baseApi().get(`/api/live/latest`)
}

export const deleteLiveApi = (id: string) => {
  return baseApi().delete(`/api/live/${id}`)
}

export const updateLiveApi = (id: string, data: ILive) => {
  return baseApi().put(`/api/live/${id}`, data)
}
