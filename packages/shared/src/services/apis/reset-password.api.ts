import { baseApi } from './base.api'

export const resetPasswordSendEmailApi = (email: string) => {
  return baseApi().post(`/api/user/reset-password/send-email`, { email })
}

export const resetPasswordVerifyTokenApi = (token: string) => {
  return baseApi().post(`/api/user/reset-password/verify-token`, { token })
}

export const resetPasswordApi = (token: string, password: string) => {
  return baseApi().post(`/api/user/reset-password`, { password, token })
}

export const changePasswordApi = (current_password: string, new_password: string) => {
  return baseApi().post(`/api/user/change-password`, { current_password, new_password })
}
