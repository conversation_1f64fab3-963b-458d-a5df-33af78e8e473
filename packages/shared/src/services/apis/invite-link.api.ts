import { baseApi } from './base.api'
import { IInviteLink } from '@/shared-types/interfaces'
import { TGetApiParams } from '@/shared-types/types'

export const createInviteLinkApi = (payload: Partial<IInviteLink>) => {
  return baseApi().post(`/api/invite-link`, payload)
}

export const getInviteLinksApi = ({ where, skip, take }: TGetApiParams) => {
  return baseApi().get(`/api/invite-link`, { params: { where, skip, take } })
}

export const getInviteLinkApi = (id: string) => {
  return baseApi().get(`/api/invite-link/${id}`)
}

export const updateInviteLinkApi = (id: string, payload: Partial<IInviteLink>) => {
  return baseApi().put(`/api/invite-link/${id}`, payload)
}

export const deleteInviteLinkApi = (id: string) => {
  return baseApi().delete(`/api/invite-link/${id}`)
}
