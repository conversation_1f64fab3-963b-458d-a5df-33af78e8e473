import axios from 'axios'

export const APIURL = ''

export const baseApi = (cotentType = 'application/json;charset=UTF-8') => {
  const defaultOptions = {
    headers: {
      'Content-Type': cotentType,
    },
  }

  return {
    get: (url, options = {}) => axios.get(url, { ...defaultOptions, ...options }),
    post: (url, data, options = {}) => axios.post(url, data, { ...defaultOptions, ...options }),
    put: (url, data, options = {}) => axios.put(url, data, { ...defaultOptions, ...options }),
    delete: (url, options = {}) => axios.delete(url, { ...defaultOptions, ...options }),
    patch: (url, data, options = {}) => axios.patch(url, data, { ...defaultOptions, ...options }),
  }
}
