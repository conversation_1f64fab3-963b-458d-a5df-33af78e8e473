import { inngest } from '../inngest'
import { deleteRecordsFromAlgoliaIndex } from '@/shared-libs/algolia'
import { deleteStreamMessage } from '@/shared-libs/stream-chat'

export default inngest.createFunction(
  { id: 'feed-deleted', name: 'Feed Deleted' },
  { event: 'memberup/feed.deleted' },
  async ({ event, step }) => {
    const relatedFeedIds = event.data['relatedFeedIds']
    if (relatedFeedIds.length === 0) {
      return { event, body: 'OK' }
    }
    for (let id of relatedFeedIds) {
      await deleteStreamMessage(id)
    }
    await deleteRecordsFromAlgoliaIndex('feed', relatedFeedIds)
    return { event, body: 'OK' }
  },
)
