import {
  Channel,
  Event,
  EventAttendee,
  Feed,
  Feedback,
  InviteLink,
  Live,
  Membership,
  MembershipSetting,
  Notification,
  Payment,
  Prisma,
  Reaction,
  SparkCategory,
  SparkMembershipCategory,
  SparkMembershipQuestion,
  SparkQuestion,
  SparkQuestionSetting,
  SparkResponse,
  Tag,
  User,
  UserMembership,
  UserProfile,
} from '@prisma/client'
import Stripe from 'stripe'

import { BIO_QUESTION_ENUM, MEMBERUP_PLAN_ENUM, USER_ROLE_ENUM, VISIBILITY_ENUM } from './enum'
import {
  TActiveCampaign,
  TAppCropArea,
  TAppTime,
  TAttachment,
  TChannelDetails,
  TEmailNotice,
  TGoogleMapCenter,
  TMembershipSettingLibrary,
  TMembershipSettingSignin,
  TMembershipSettingSignup,
  TMembershipSettingSignupPayment,
  TOnboarding,
  TReportBy,
  TSocial,
  TStripeConnectAccount,
  TTaskStatus,
} from './types'

export interface IBioQuestion {
  type: BIO_QUESTION_ENUM
  question?: string
  description?: string
  answers?: string[]
}

export interface IOnboarding {
  show_onboarding_popup?: boolean
  welcome_title?: string
  welcome_message?: string
  welcome_description?: string
  welcome_asset?: string
  bio_questions?: IBioQuestion[]
}

export interface ILibrarySetting {
  cover_image_type: string
  cover_image: string
  title?: string
}
export interface IStripeAccount {
  id: string
  object: string
  business_profile: {
    mcc: string
    name: string
    product_description: string
    support_address: {
      city: string
      country: string
      line1: string
      line2: string
      postal_code: string
      state: string
    }
    support_email: string
    support_phone: string
    support_url: string
    url: string
  }
  business_type: string
  capabilities: {
    card_payments: string
    transfers: string
  }
  charges_enabled: boolean
  controller: {
    is_controller: boolean
    type: string
  }
  country: string
  created: number
  default_currency: string
  details_submitted: boolean
  email: string
  external_accounts: {
    data: {
      account: string
      account_holder_name: string
      account_holder_type: string
      account_type: string
      bank_name: string
      country: string
      currency: string
      default_for_currency: boolean
      fingerprint: string
      id: string
      last4: string
      metadata: any
      object: string
      routing_number: string
      status: string
    }[]
    has_more: boolean
    object: string
    total_count: number
    url: string
  }
  future_requirements: {
    alternatives: string[]
    current_deadline: number
    currently_due: string[]
    disabled_reason: string
    errors: string[]
    eventually_due: string[]
    past_due: string[]
    pending_verification: string[]
  }
  payouts_enabled: boolean
  requirements: {
    alternatives: string[]
    current_deadline: number
    currently_due: string[]
    disabled_reason: string
    errors: string[]
    eventually_due: string[]
    past_due: string[]
    pending_verification: string[]
  }
  settings: {
    bacs_debit_payments: any
    branding: {
      icon: string
      logo: string
      primary_color: string
      secondary_color: string
    }
    card_issuing: {
      tos_acceptance: {
        date: number
        ip: string
        user_agent: string
      }
    }
    card_payments: {
      decline_on: {
        avs_failure: boolean
        cvc_failure: boolean
      }
      statement_descriptor_prefix: string
    }
    dashboard: {
      display_name: string
      timezone: string
    }
    payments: {
      statement_descriptor: string
      statement_descriptor_kana: string
      statement_descriptor_kanji: string
    }
    payouts: {
      debit_negative_balances: boolean
      schedule: {
        delay_days: number
        interval: string
      }
      statement_descriptor: string
    }
    sepa_debit_payments: any
  }
  tos_acceptance: {
    date: number
    ip: string
    user_agent: string
  }
  type: string
}

export interface IStripePrice {
  id: string
  currency: string
  object: string
  active: true
  billing_scheme: string
  created: string
  custom_unit_amount: null
  livemode: false
  lookup_key: string
  metadata: object
  nickname: string
  product: string
  recurring: {
    aggregate_usage: string
    interval: string
    interval_count: number
    usage_type: string
  }
  tax_behavior: string
  tiers_mode: string
  transform_quantity: number
  type: string
  unit_amount: number
  unit_amount_decimal: string
}

export interface IStripeCoupon {
  id: string
  object: string
  amount_off: number
  created: number
  currency: string
  duration: string
  duration_in_months: number
  livemode: boolean
  max_redemptions: number
  metadata: any
  name: string
  percent_off: number
  redeem_by: number
  times_redeemed: number
  valid: boolean
}

export interface IStripePromotionCode {
  id: string
  object: string
  active: boolean
  code: string
  coupon: IStripeCoupon
  created: number
  customer: string
  expires_at: number
  livemode: boolean
  max_redemptions: number
  metadata: any
  restrictions: {
    first_time_transaction: boolean
    minimum_amount: number
    minimum_amount_currency: number
  }
  times_redeemed: number
}

export interface IStripeWebhook {
  id: string
  api_version: string
  application: string
  created: number
  description: string
  enabled_events: string[]
  livemode: string
  metadata: any
  object: string
  secret: string
  status: 'enabled' | 'disabled'
  url: string
}

export interface IMembership extends Membership {
  channels?: IChannel[]
  feedbacks?: IFeedback[]
  lives?: ILive[]
  membership_setting?: IMembershipSetting
  tags?: ITag[]
  users?: IUser[]
  events?: IEvent[]
  owner?: IUser
}

export interface ILink {
  label: string
  url: string
}

export interface IMembershipSetting extends MembershipSetting {
  active_campaign: TActiveCampaign | null
  emails: TEmailNotice[] | null
  onboarding: TOnboarding | null
  favicon_crop_area: TAppCropArea | null
  logo_crop_area: TAppCropArea | null
  library: TMembershipSettingLibrary | null
  signin: TMembershipSettingSignin | null
  signup: TMembershipSettingSignup | null
  signup_payment: TMembershipSettingSignupPayment | null
  spark_expire_time: TAppTime | null
  stripe_connect_account: TStripeConnectAccount | null
  stripe_prices: any[] | null
  stripe_customer?: Stripe.Customer // gets from stripe
  stripe_payment_method?: Stripe.PaymentMethod // gets from stripe
  stripe_subscription?: Stripe.Subscription
  active_spark_category?: ISparkCategory
  visibility: VISIBILITY_ENUM
  logo_file?: File
  favicon_file?: File
  cover_image_file?: File
}

export interface IUser extends User {
  current_membership_id?: string
  name?: string
  image_crop_area?: TAppCropArea
  deactivated_at?: string
  last_active?: string
  online_status?: boolean
  teams?: string[]
  profile?: IUserProfile
  membership?: IMembership
  created_at?: Date
  online?: boolean
  user_memberships?: IUserMembership[]
  ContentLibraryCourseUserProgress?: any[]
}

export interface IUserProfile extends UserProfile {
  pinned_posts_hidden: any
  stripe_customer?: Stripe.Customer // gets from stripe
  stripe_payment_method?: Stripe.PaymentMethod // gets from stripe
  stripe_subscription?: Stripe.Subscription
  enable_notifications: any | null
  getting_started: TTaskStatus | null
  image_crop_area: TAppCropArea | null
  relationships: string[] | null
  social: TSocial | null
}

export interface IUserMembership extends UserMembership {
  membership?: IMembership
}

export interface IMembershipInput {
  plan: MEMBERUP_PLAN_ENUM
}

export interface IActor {
  id: string
  data: {
    membership: string
    name: string
    image: string
    role: USER_ROLE_ENUM
    status: 'active'
  }
  created_at: string
  updated_at: string
}

export interface IBenefit {
  id: string
  title?: string
  description?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export interface IReport {
  activity_id: string
  _by: {
    date: string // utc date string
    user: IUser | string
  }[]
  report_by: TReportBy[] | null
}

export interface ISpaceDetails {
  title: string
  description?: string
}

export interface ITag extends Tag {}

export interface IGoal {
  id: string
  postsNum: number
  jointNum: number
}

export interface ICreateStripeCustomer {
  stripeToken: string
  email: string
  productPlan: string
}

export interface ILiveStream {
  id: string
  playback: string
  thumbnail: string
}

export interface ILive extends Live {
  membership?: IMembership
  user?: IUser
  live_status?: 'started' | 'loading' | 'ended'
  layoutData?: string // livestream
  layoutMode?: string // livestream
  live?: boolean // livestream
  liveAt?: string // livestream
  live_stream_token?: string // livestream
  overlayData?: string // livestream
  playback: string // livestream
  stream_id: string // livestream
  showNames?: boolean // livestream
  thumbnail: string // livestream
}

export interface IChannel extends Channel {
  is_private: boolean
  visibility: boolean
  banner_image_file?: File
  details: TChannelDetails[] | null
  featured_asset_file?: File
  membership?: IMembership
}

export interface ISpace {
  id: string
  name: string
  slug: string
  description?: string
}

export interface IPayment extends Payment {
  membership?: IMembership
  user?: IUser
}

export interface IFeed extends Feed {
  membership?: IMembership
  user?: IUser
  channel?: IChannel
  // id?: string //	The message ID. This is either created by Stream or set client side when the message is added.
  cid?: string //	The message ID. This is either created by Stream or set client side when the message is added.
  // html?: string //	The safe HTML generated from the raw text message. This field can only be set using server-side APIs or via the import
  type?: string //	The message type. See below for more information.
  attachments: TAttachment[] | null // array	The list of attachments, either provided by the user or generated from a command or as a result of URL scraping.
  latest_reactions?: IReaction[] // The latest reactions to the message created by any user. (Max 10 reactions)
  own_reactions?: IReaction[] // The array of reactions added to the message by the current user. e.g. [ReactionObject1, ReactionObject2].
  pinned?: boolean
  pin_expires?: string | number | Date
  pinned_at?: string | number | Date
  pinned_by?: string | { id: string }
  reaction_counts?: any // The reaction count by type for this message e.g. {"haha": 3, "angry": 2}.
  reports: TReportBy[] | null
  reply_count?: number //	Reserved field indicating the number of replies for this message.
  //parent_id?: string //	The ID of the parent message, if the message is a reply.
  //hierarchy_order?: string
  //reply_parent_id?: string // Custom relation to a feed when this post works as a comment.f
  featured_commenters: any
  created_at?: Date //	Reserved field indicating when the message was created.
  updated_at?: Date //	Reserved field indicating when the message was updated last time.
  deleted_at?: Date //	Reserved field indicating when the message was deleted.
  mentioned_users?: { id: string; name?: string }[] // array of users	The list of users that are mentioned in this message.
  status?: string // Message status: e.g. 'received', 'sending', 'failed'	-	✓
  metadata: any
  permalink: string //	The link to the message.
  parent_permalink?: string | null | unknown //	The link to the parent message.
  links: any
  edited: boolean
  latest_comment_timestamp?: number
  notify_everyone?: boolean
}

export interface IReaction extends Reaction {
  message?: IFeed
  user?: IUser
  // message_id: string //	ID of the message to react to
  // type: string // User could have only 1 reaction of each type per message
  // score: number // Score of the reaction for cumulative reactions
  channel?: string
  mentioned_users?: { id: string; name?: string }[]
  permalink?: string | null | unknown
  notify_everyone?: boolean
  awarded_user_id?: string
}

export interface IEventAttendee extends EventAttendee {
  user?: IUser
}
export interface IEvent extends Event {
  attendees?: IEventAttendee[]
  header_image_file?: File
  location_map_center: TGoogleMapCenter | null
  membership?: IMembership
  location_zoom: any
}

export interface IFeedback extends Feedback {}

export interface IInviteLink extends InviteLink {
  members?: number
}

export interface INotification extends Notification {
  channel?: IChannel
  message?: IFeed
  reaction?: IReaction
  user?: IUser
  // cid?: string
  // type: string // Event type
  // message?: any // Message Object
  // reaction?: any //	Reaction Object
  // channel?: any //	Channel Object
  member?: any // User object for the channel member that was added/removed
  // user?: any //	User object of the current user
  me?: any //	User object of the health check user
  // total_unread_count?: number //	the number of unread messages for current user
  // watcher_count?: number // Number of users watching this channel
}

export interface ISparkCategory extends SparkCategory {
  membership_settings?: IMembershipSetting[]
  spark_m_categories?: ISparkMembershipCategory[]
  spark_questions?: ISparkQuestion[]
}

export interface ISparkMembershipCategory extends SparkMembershipCategory {
  m_active_question?: ISparkQuestion
  active_question?: ISparkQuestion
  category?: ISparkCategory
  question_sequences: string[]
}

export interface ISparkCompletedQuestionListingItem {
  id: string
  content: string
  answer?: string
  responses_count: number
  completed_at: string
}

export interface ISparkQuestion extends SparkQuestion {
  category?: ISparkCategory
  spark_m_categories?: ISparkMembershipCategory[]
  spark_m_questions?: ISparkMembershipQuestion[]
  spark_responses?: ISparkResponse[]
  completed_at?: string
}

export interface ISparkQuestionWithSettings extends SparkQuestion {
  category?: ISparkCategory
  completed_at?: string
  question_settings_id?: string
}

export interface ISparkQuestionSettings extends SparkQuestionSetting {}

export interface ISparkMembershipQuestion extends SparkMembershipQuestion {
  question?: ISparkQuestion
}

export interface ISparkResponse extends SparkResponse {
  question?: ISparkQuestion
  user?: IUser
}

export interface IAsset {
  name: string
  size: number
  type: string
  url: string
}

export interface ICloudinaryImage {
  access_mode: string //"public"
  asset_id: string //"1af0acba29074eaac93308d9ff5d4410"
  batchId: string //"uw-batch2"
  bytes: number
  created_at: string //"2022-04-09T20:33:05Z"
  etag: string //"73c4b3758af64736831438b028ac4524"
  existing: false
  format: string //"png"
  height: number
  id: string //"uw-file3"
  original_filename: string //"barbara"
  path: string //"v1649536385/barbara_pecfqw.png"
  placeholder: false
  public_id: string //"barbara_pecfqw"
  resource_type: string //"image"
  secure_url: string //"https://res.cloudinary.com/memberup-llc/image/upload/v1649536385/barbara_pecfqw.png"
  signature: string //"c2a31358fed0fde699ad12bd165cd57d196ab471"
  tags: string[]
  thumbnail_url: string //"https://res.cloudinary.com/memberup-llc/image/upload/c_limit,h_60,w_90/v1649536385/barbara_pecfqw.png"
  type: string //"upload"
  url: string //"http://res.cloudinary.com/memberup-llc/image/upload/v1649536385/barbara_pecfqw.png"
  version: number
  version_id: string //"1837b56fc0c275b1ade8d89fba519e84"
  width: number
}

const authenticatedUserValidator = Prisma.validator<Prisma.UserDefaultArgs>()({
  select: {
    id: true,
    email: true,
    first_name: true,
    last_name: true,
    username: true,
    image: true,
    verification_code: true,
    role: true,
    status: true,
    banned_reason: true,
    is_primary: true,
    createdAt: true,
    updatedAt: true,
    profile: true,
  },
  include: {
    profile: true,
    user_memberships: {
      select: {
        user_role: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        membership: {
          select: {
            id: true,
            name: true,
            slug: true,
            createdAt: true,
            updatedAt: true,
            membership_setting: {
              select: {
                completed_membership: true,
                community_idea: true,
                cover_image: true,
                cover_image_crop_area: true,
                description: true,
                external_links: true,
                favicon: true,
                favicon_crop_area: true,
                intro_html: false,
                intro_closed_html: false,
                logo: true,
                logo_crop_area: true,
                library: true,
                onboarding: true,
                spark_expire_time: true,
                time_zone: true,
                theme_main_color: true,
                theme_secondary_color: true,
                visibility: true,
                stripe_prices: true,
              },
            },
          },
        },
      },
    },
  },
})

export type IAuthenticatedUser = Prisma.UserGetPayload<typeof authenticatedUserValidator>

export interface IAuthenticatedUserData {
  user: IAuthenticatedUser
  profile: IUserProfile
  knockToken: string
  streamChatUserToken: string
}
