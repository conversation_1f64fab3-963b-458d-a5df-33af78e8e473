import React from 'react'

export const SVGUpload: React.FC<{
  className?: string
  width?: number
  height?: number
  strokeWidth?: number
}> = ({ className, width, height, strokeWidth }) => {
  return (
    <svg
      className={className}
      width={width || 16}
      height={height || 16}
      viewBox="0 0 16 16"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" strokeWidth={strokeWidth || 1} fill="none" fillRule="evenodd">
        <g transform="translate(-2.000000, -2.000000)" fill="currentColor" fillRule="nonzero">
          <g transform="translate(2.000000, 2.000000)">
            <path d="M15,10 C15.5128358,10 15.9355072,10.3860402 15.9932723,10.8833789 L16,11 L16,13 C16,14.5976809 14.75108,15.9036609 13.1762728,15.9949073 L13,16 L3,16 C1.40231912,16 0.09633912,14.75108 0.00509269,13.1762728 L0,13 L0,11 C0,10.4477153 0.44771525,10 1,10 C1.51283584,10 1.93550716,10.3860402 1.99327227,10.8833789 L2,11 L2,13 C2,13.5128358 2.38604019,13.9355072 2.88337887,13.9932723 L3,14 L13,14 C13.5128358,14 13.9355072,13.6139598 13.9932723,13.1166211 L14,13 L14,11 C14,10.4477153 14.4477153,10 15,10 Z M8.7071068,0.292893218 L11.7071068,3.29289322 C12.0976311,3.68341751 12.0976311,4.31658249 11.7071068,4.70710678 C11.3165825,5.09763107 10.6834175,5.09763107 10.2928932,4.70710678 L9,3.414 L9,11 C9,11.5522847 8.5522847,12 8,12 C7.48716416,12 7.06449284,11.6139598 7.00672773,11.1166211 L7,11 L7,3.414 L5.70710678,4.70710678 C5.31658249,5.09763107 4.68341751,5.09763107 4.29289322,4.70710678 C3.90236893,4.31658249 3.90236893,3.68341751 4.29289322,3.29289322 L7.29289322,0.292893218 C7.68341751,-0.0976310725 8.3165825,-0.0976310725 8.7071068,0.292893218 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  )
}
