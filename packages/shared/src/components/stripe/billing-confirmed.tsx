import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import _capitalize from 'lodash/capitalize'
import { useRouter } from 'next/router'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { numberToCurrency } from '@memberup/shared/src/libs/numeric-utils'
import { MEMBERUP_PLANS } from '@memberup/shared/src/settings/plans'

export function BillingConfirmed(props: { planName: string; isAnnual: boolean; stripeSubscriptionStatus: string }) {
  const router = useRouter()
  const plan = MEMBERUP_PLANS.find((p) => p.name === props.planName)

  if (!plan) return null

  const price = numberToCurrency(props.isAnnual ? plan.annualPrice : plan.monthlyPrice)

  return (
    <Box
      sx={{
        padding: 3,
      }}
    >
      <Box
        sx={{
          padding: 3,
          paddingTop: '56px',
          paddingBottom: '48px',
          backgroundImage: 'url(/assets/default/images/thankyou-background.png)',
          backgroundSize: 'cover',
          borderRadius: '12px',
        }}
      >
        <div className="text-center">
          <CheckCircleIcon color="success" style={{ fontSize: 48 }} />
        </div>
        <br />
        <br />
        <Typography className="text-center" variant="h4" gutterBottom style={{ fontSize: 24 }}>
          Thank you for subscribing
        </Typography>
        <br />
        <Typography className="text-center" variant="body1">
          Your subscription has been confirmed. An email confirmation has been sent to your email.
        </Typography>
        <br />
        <br />
        <div
          className="background-color15"
          style={{
            borderRadius: 12,
            padding: 24,
            maxWidth: 454,
            margin: 'auto',
            width: '100%',
            textAlign: 'left',
          }}
        >
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Typography color="text.disabled" variant="body2" gutterBottom>
                Order Summary
              </Typography>
              <Typography variant="h6" gutterBottom>
                {_capitalize(plan?.name)}&nbsp;Plan
              </Typography>
              <Typography variant="body1">
                {price}
                &nbsp;per&nbsp;{props.isAnnual ? 'year' : 'month'}
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Divider />
            </Grid>
            <Grid item xs={12}>
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <Typography color="text.disabled" variant="body2" gutterBottom>
                    Payment Total
                  </Typography>
                </Grid>
                <Grid item xs={4}>
                  <Typography color="text.disabled" variant="body2" gutterBottom>
                    Payment Status
                  </Typography>
                </Grid>
                <Grid item xs={4}></Grid>
              </Grid>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={4}>
                  <Typography variant="h6">{price}</Typography>
                </Grid>
                <Grid item xs={4}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item>
                      <CheckCircleIcon color="success" />
                    </Grid>
                    <Grid item xs>
                      <Typography variant="h6">{props.stripeSubscriptionStatus || 'Unpaid'}</Typography>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid item xs={4}></Grid>
              </Grid>
            </Grid>
          </Grid>
        </div>
        <br />
        <br />
        <div className="text-center">
          <Button className="round-small" variant="outlined" color="inherit" onClick={() => router.push('/')}>
            Go to your Dashboard
          </Button>
        </div>
      </Box>
      <Box style={{ margin: 'auto', width: '100%', maxWidth: 1134 }}>
        <Grid container spacing={4}>
          <Grid item xs={12}></Grid>
          <Grid item xs={12}>
            <Divider />
          </Grid>
          <Grid item xs={12}>
            <Grid container spacing={4} alignItems="center">
              <Grid item>
                <div
                  className="app-image-wrapper"
                  style={{
                    width: 80,
                    height: 80,
                    borderRadius: 40,
                    backgroundColor: '#D8D3F0',
                    padding: 20,
                  }}
                >
                  <AppImg
                    src="/assets/default/svgs/phone-call.svg"
                    alt="phone call icon"
                    width={80}
                    height={80}
                    // style={{ maxWidth: 356, width: '100%' }}
                  />
                </div>
              </Grid>
              <Grid item xs>
                <Typography className="font-family-graphik-medium" variant="h6" gutterBottom>
                  Need Help? We offer 1-on-1 onboarding with one of MemberUp’s co-founders.
                </Typography>
                <Typography className="font-family-graphik-medium" variant="h6">
                  We&apos;ll work with you in setting up your membership platform to your business.
                </Typography>
              </Grid>
              <Grid item>
                <Button className="round-small" variant="contained">
                  Book my onboarding session
                </Button>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <Divider />
          </Grid>
          <Grid item xs={12}></Grid>
          <Grid item xs={12}>
            <Grid container spacing={4}>
              <Grid item xs={12}>
                <Grid container alignItems="center" spacing={1}>
                  <Grid item xs>
                    <Typography variant="h4">Getting Started on MemberUp</Typography>
                  </Grid>
                  <Grid className="color03" item>
                    Memberup University
                  </Grid>
                  <Grid className="color03" item>
                    <ArrowForwardIosIcon fontSize="small" />
                  </Grid>
                </Grid>
              </Grid>
              <Grid className="text-center" item xs={4}>
                <AppImg
                  src="/assets/default/images/thanks01.png"
                  alt="Getting Started on MemberUp"
                  width={356}
                  height={200}
                  style={{ maxWidth: 356, width: '100%' }}
                />
              </Grid>
              <Grid className="text-center" item xs={4}>
                <AppImg
                  src="/assets/default/images/thanks02.png"
                  alt="Getting Started on MemberUp"
                  width={356}
                  height={200}
                  style={{ maxWidth: 356, width: '100%' }}
                />
              </Grid>
              <Grid className="text-center" item xs={4}>
                <AppImg
                  src="/assets/default/images/thanks03.png"
                  alt="Getting Started on MemberUp"
                  width={356}
                  height={200}
                  style={{ maxWidth: 356, width: '100%' }}
                />
              </Grid>
              <Grid item xs={12}></Grid>
              <Grid item xs={12}>
                <Typography variant="h4">Quick Links</Typography>
              </Grid>
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  <Grid item>
                    <Button className="round-small" variant="outlined" color="inherit">
                      Creator Dashboard
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button className="round-small" variant="outlined" color="inherit">
                      Setup your Pricing Plan
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button className="round-small" variant="outlined" color="inherit">
                      Home
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button className="round-small" variant="outlined" color="inherit">
                      Library
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button className="round-small" variant="outlined" color="inherit">
                      Spaces
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button className="round-small" variant="outlined" color="inherit">
                      Live
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button className="round-small" variant="outlined" color="inherit">
                      Members
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </Box>
  )
}

BillingConfirmed.displayName = 'BillingConfirmed'
