import Box from '@mui/material/Box'
import clsx from 'clsx'
import React from 'react'

const styles = {
  templateRightSide: {
    flexGrow: 1,
    height: '100%',
    borderTopRightRadius: '12px',
    borderBottomRightRadius: '12px',
    overflow: 'hidden,',
  },
  templateSideMenuItem: {
    display: 'flex',
    alignItems: 'center',
    padding: '4px',
    borderRadius: '8px',
    '&.active': {
      background: 'linear-gradient(225deg, #DDD6F3 0%, #FAACA8 33.04%, #DB8BE7 100%)',
    },
  },
  templateSideMenuIcon: {
    width: '6px',
    height: '6px',
    borderRadius: '3px',
    marginRight: '4px',
  },
  templateSideMenuText: {
    height: '6px',
    borderRadius: '3px',
    flexGrow: 1,
  },
  templatePageContent: {
    flexGrow: 1,
    height: '100%',
    padding: '8px',
  },
  templateButton: {
    padding: '4px 8px',
    borderRadius: '8px',
    background: 'linear-gradient(225deg, #DDD6F3 0%, #FAACA8 33.04%, #DB8BE7 100%)',
    width: '60px',
    float: 'right',
  },
}

export const ThemeTemplate: React.FC<{
  layoutBackgroundColor: string
  layoutSecondaryColor: string
  pageBackgroundColor: string
  textColor: string
}> = ({ layoutBackgroundColor, layoutSecondaryColor, pageBackgroundColor, textColor }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        maxWidth: '292px',
        height: '175px',
        borderRadius: '12px',
        margin: 'auto',
        overflow: 'hidden',
        backgroundColor: layoutBackgroundColor,
      }}
    >
      <Box
        sx={{
          width: '62px',
          height: '100%',
          padding: '14px 8px',
        }}
      >
        <Box
          className={clsx({ active: true })}
          sx={styles.templateSideMenuItem}
          style={{ background: layoutSecondaryColor }}
        >
          <Box sx={styles.templateSideMenuIcon} style={{ backgroundColor: textColor }}></Box>
          <Box sx={styles.templateSideMenuText} style={{ backgroundColor: textColor }}></Box>
        </Box>
        <Box sx={styles.templateSideMenuItem}>
          <Box sx={styles.templateSideMenuIcon} style={{ backgroundColor: textColor }}></Box>
          <Box sx={styles.templateSideMenuText} style={{ backgroundColor: textColor }}></Box>
        </Box>
        <Box sx={styles.templateSideMenuItem}>
          <Box sx={styles.templateSideMenuIcon} style={{ backgroundColor: textColor }}></Box>
          <Box sx={styles.templateSideMenuText} style={{ backgroundColor: textColor }}></Box>
        </Box>
        <Box sx={styles.templateSideMenuItem}>
          <Box sx={styles.templateSideMenuIcon} style={{ backgroundColor: textColor }}></Box>
          <Box sx={styles.templateSideMenuText} style={{ backgroundColor: textColor }}></Box>
        </Box>
      </Box>
      <Box sx={styles.templateRightSide}>
        <Box sx={styles.templatePageContent} style={{ backgroundColor: pageBackgroundColor }}>
          <Box sx={styles.templateButton} style={{ background: layoutSecondaryColor }}>
            <Box sx={styles.templateSideMenuText} style={{ backgroundColor: textColor }}></Box>
          </Box>
        </Box>
      </Box>
    </Box>
  )
}
