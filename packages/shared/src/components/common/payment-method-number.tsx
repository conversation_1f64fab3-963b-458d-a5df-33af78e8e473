import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import { useTheme } from '@mui/material/styles'

export const AppPaymentMethodNumber = (props: { prefix?: string; number: number | string }) => {
  const theme = useTheme()
  return (
    <span
      style={{ color: theme.palette.mode === 'dark' ? '#fff' : '#000', fontSize: '0.875rem' }}
      className="d-flex align-center"
    >
      {Boolean(props.prefix) && (
        <>
          <span className="capitalize">{props.prefix}</span>&nbsp;
        </>
      )}
      <MoreHorizIcon />
      <MoreHorizIcon />
      <MoreHorizIcon />
      <MoreHorizIcon />
      &nbsp;
      <span>{props.number}</span>
    </span>
  )
}
