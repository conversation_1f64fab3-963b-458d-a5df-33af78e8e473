import Box from '@mui/material/Box'
import CircularProgress from '@mui/material/CircularProgress'
import React from 'react'

const LoadingSpinner: React.FC<{
  size?: number
  textNode?: React.ReactNode
}> = ({ size, textNode }) => {
  return (
    <Box className="app-loading-wrapper">
      <Box
        className="app-loading-backdrop"
        onClick={(e) => {
          e.preventDefault()
          e.stopPropagation()
        }}
      ></Box>
      <CircularProgress size={size || 32} />
      {textNode}
    </Box>
  )
}

export const AppLoadingSpinner = React.memo(LoadingSpinner)
