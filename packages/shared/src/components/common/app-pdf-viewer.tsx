import ChevronLeftRoundedIcon from '@mui/icons-material/ChevronLeftRounded'
import ChevronRightRoundedIcon from '@mui/icons-material/ChevronRightRounded'
import DownloadForOfflineIcon from '@mui/icons-material/DownloadForOffline'
import FullscreenIcon from '@mui/icons-material/Fullscreen'
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Tooltip from '@mui/material/Tooltip'
import Typography from '@mui/material/Typography'
import useMediaQuery from '@mui/material/useMediaQuery'
import { useTheme } from '@mui/styles'
import { SpecialZoomLevel, Viewer, Worker } from '@react-pdf-viewer/core'

import '@react-pdf-viewer/core/lib/styles/index.css'

import { fullScreenPlugin, RenderEnterFullScreenProps } from '@react-pdf-viewer/full-screen'
import { pageNavigationPlugin } from '@react-pdf-viewer/page-navigation'
import { saveAs } from 'file-saver'

const AppPdfViewer: React.FC<{
  name: string
  url?: string
  showHeader?: boolean
  showDownloadButton?: boolean
}> = (props) => {
  const { name, url, showHeader = true, showDownloadButton = true } = props
  const theme = useTheme()
  const isSmDown = useMediaQuery(theme['breakpoints'].down('sm'))
  const pageNavigationPluginInstance = pageNavigationPlugin()
  const fullScreenPluginInstance = fullScreenPlugin()
  const { GoToNextPage, GoToPreviousPage, CurrentPageLabel } = pageNavigationPluginInstance
  const { EnterFullScreen } = fullScreenPluginInstance
  const handleDownload = () => {
    saveAs(url, name)
  }

  return (
    <Box className="h-100 w-100" sx={{ position: 'relative', p: showHeader ? 2 : 0 }}>
      {showHeader && (
        <Box className="w-100" sx={{ p: 2 }}>
          <Grid container alignItems="center" spacing={2}>
            {name && (
              <Grid item xs>
                <Typography className="text-ellipsis" variant="h6">
                  {name}
                </Typography>
              </Grid>
            )}
          </Grid>
        </Box>
      )}
      <Box sx={{ height: 'calc(100% - 68px)', overflow: 'auto' }}>
        <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js">
          <Viewer
            defaultScale={isSmDown ? SpecialZoomLevel.PageFit : 1}
            fileUrl={url}
            plugins={[pageNavigationPluginInstance, fullScreenPluginInstance]}
          />
        </Worker>
      </Box>
      <Box className="w-100" sx={{ pt: '2px' }}>
        <Grid container alignItems="center">
          <Grid item>
            <GoToPreviousPage>
              {(props) => (
                <Tooltip title="Previous Page">
                  <span>
                    <IconButton disabled={props.isDisabled} onClick={props.onClick}>
                      <ChevronLeftRoundedIcon />
                    </IconButton>
                  </span>
                </Tooltip>
              )}
            </GoToPreviousPage>
          </Grid>
          <Grid item>
            <GoToNextPage>
              {(props) => (
                <Tooltip title="Next Page">
                  <span>
                    <IconButton disabled={props.isDisabled} onClick={props.onClick}>
                      <ChevronRightRoundedIcon />
                    </IconButton>
                  </span>
                </Tooltip>
              )}
            </GoToNextPage>
          </Grid>

          <Grid item>
            <CurrentPageLabel>
              {(props) => (
                <Typography variant="subtitle1">{`${props.currentPage + 1} of ${props.numberOfPages}`}</Typography>
              )}
            </CurrentPageLabel>
          </Grid>

          <Grid item xs></Grid>
          {showDownloadButton && (
            <Grid item>
              <Tooltip title="Download">
                <IconButton onClick={handleDownload}>
                  <DownloadForOfflineIcon />
                </IconButton>
              </Tooltip>
            </Grid>
          )}
          <Grid item sx={{ marginRight: '15px' }}>
            <EnterFullScreen>
              {(props: RenderEnterFullScreenProps) => (
                <Tooltip title="Full Screen">
                  <IconButton onClick={props.onClick}>
                    <FullscreenIcon />
                  </IconButton>
                </Tooltip>
              )}
            </EnterFullScreen>
          </Grid>
        </Grid>
      </Box>
    </Box>
  )
}

AppPdfViewer.displayName = 'AppPdfViewer'
export default AppPdfViewer
