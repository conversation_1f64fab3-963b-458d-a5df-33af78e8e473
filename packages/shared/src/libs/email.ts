export const buildEmailContent = (args: {
  color: string
  backgroundColor: string
  contentBackgroundColor: string
  grayColor: string
  logoImage?: string
  logoText?: string
  preHeaderText?: string
  title: string
  content: string[]
  link?: {
    text: string
    url: string
  }
  isResetPassword?: boolean
}) => {
  let emailContent = `<table style="width: 100%;" border="0" cellspacing="0" width="100%">`
  // email body row
  emailContent += `<tr><td style="background-color: ${args.backgroundColor}; color: ${args.color}; padding: 50px 8px 92px;">`
  // email body table
  emailContent += `<table border="0" cellspacing="0" width="100%" style="max-width: 600px; margin: auto;">`
  emailContent += `<tr><td style="background-color: ${args.contentBackgroundColor}; border-radius: 16px; padding-top: 32px; overflow: hidden;">`
  emailContent += `<table border="0" cellspacing="0" width="100%" style="max-width: 600px; margin: auto;">`
  // logo row
  emailContent += `<tr><td style="font-size: 24px; text-align: center;">`
  if (args.logoImage) {
    emailContent += `<img src="${args.logoImage}" style="width: 154px; height: 24px;" />`
  } else if (args.logoText) {
    emailContent += `<b>${args.logoText}</b>`
  }
  emailContent += `</td></tr>`

  if (args.preHeaderText) {
    emailContent += `<tr><td style="padding-top: 24px;"><div style="font-size: 18px; text-align: center;">${args.preHeaderText}</div></td></tr>`
  }

  if (args.isResetPassword) {
    emailContent += `<tr><td style="padding-top: 88px;"><div style="background-color: ${args.grayColor}; border-radius: 16px; font-size: 70px; line-height: 45px; padding: 24px 48px 0 48px; text-align: center; margin: auto; width: 300px;">* * * * *</div></td></tr>`
  }

  // email content row
  emailContent += `<tr style="margin-top: 80px; padding-top: 80px;"><td style="padding: 40px 40px 80px;">`
  // email content table
  emailContent += `<table border="0" cellspacing="0" width="100%">`
  // title row
  if (args.title) {
    emailContent += `<tr><td style="padding-top: 40px; text-align: center;"><h2 style="font-size: 40px; line-height: 48px; letter-spacing: -1px; padding-top: 0px; padding-bottom: 0px; margin-top: 0px; margin-bottom: 0px;">${args.title}</h2></td></tr>`
  }

  // description row
  emailContent += `<tr><td style="padding-top: 16px;"><table border="0" cellspacing="0" width="100%">`
  // description table
  emailContent += `<table border="0" cellspacing="0" width="100%">`
  for (const str of args.content) {
    emailContent += `<tr><td style="font-size: 18px; line-height: 24px; padding-top: 32px;">${str}</td></tr>`
  }
  // end of description table
  emailContent += `</table>`
  // end of description row
  emailContent += `</td></tr>`

  // button link
  if (args.link) {
    emailContent += `<tr><td style="padding-top: 48px;"><a href="${args.link.url}" style="background-color: ${args.backgroundColor}; border-radius: 100px; color: ${args.contentBackgroundColor}; display: block; font-size: 16px; line-height: 24px; padding-top: 16px; padding-bottom: 16px; text-align: center; width: 100%;"><b>${args.link.text}</b></a></td></tr>`
  }
  // description 2
  if (args.isResetPassword) {
    emailContent += `<tr><td style="font-size: 16px; line-height: 24px; padding-top: 24px;"><b>This link can only be used once and will expire in 2 hours.</b><br><span>If you didn’t need to change your password, please ignore this email</span></td></tr>`
  }
  // end of email content table
  emailContent += `</table>`
  // end of email content row
  emailContent += `</td></tr>`
  // bottom
  emailContent += `<tr><td style="background-color: ${args.grayColor}; padding: 40px 100px; text-align: center;"><span style="font-size: 14px; line-height: 24px;">Have some questions or concerns? Come and ask our friendly community experts&nbsp;<a style="text-decoration: underline;">here</a>.</span></td></tr>`
  emailContent += `</table>`
  // end of email body table
  emailContent += `</td></tr></table>`
  // end of email body
  emailContent += `</td></tr>`
  emailContent += `</table>`

  return emailContent
}
