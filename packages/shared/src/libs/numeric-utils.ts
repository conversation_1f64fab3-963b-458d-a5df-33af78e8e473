const removeCommas = (value: string): string => {
  return `${value}`.replace(/,/g, '')
}

export const stringToNumber = (value: string, targetType = 'int') => {
  if (!value) return null

  let regexNumberMatch: any = `${removeCommas(value)}`.match(/[-+]?(\d+)(\.\d+)?/g)
  if (!regexNumberMatch) return null

  const parser = targetType === 'float' ? parseFloat : parseInt
  const num = parser(`${regexNumberMatch}`)

  return isNaN(num) ? null : num
}

export const numberToPercent = (value: number) => {
  return isNaN(value) ? null : `${value.toFixed(2)}%`
}

export const numberFormat = (value: number, minimumFractionDigits: number = 0, maximumFractionDigits: number = 0) => {
  if (!value) return ''
  return value.toLocaleString('en-US', {
    minimumFractionDigits,
    maximumFractionDigits,
  })
}

export const numberToCurrency = (
  value: number,
  minimumFractionDigits: number = 2,
  maximumFractionDigits: number = 2,
) => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    // These options are needed to round to whole numbers if that's what you want.
    //minimumFractionDigits: 0, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
    //maximumFractionDigits: 0, // (causes 2500.99 to be printed as $2,501)
  })
  return value.toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
    // These options are needed to round to whole numbers if that's what you want.
    minimumFractionDigits, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
    maximumFractionDigits, // (causes 2500.99 to be printed as $2,501)
  })
}
