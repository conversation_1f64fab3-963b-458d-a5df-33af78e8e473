import { unfurl } from 'unfurl.js'

// we want to render our default icons in these cases of lowres favicons

export const getUrlData = async (url: string) => {
  try {
    const result = await unfurl(url)
    // Truncate the description to 200 characters
    let description = result.description ?? result.open_graph?.description
    if (description && description.length > 200) {
      description = description.substring(0, 200) + '...'
    }
    return {
      title: result.title,
      description: description,
      //favicon: result.favicon && !ignoreDomainFavicon ? result.favicon : null,
      favicon: null, // NOTE: Favicon is not an option for rendering as there are some sites returning favicons with incorrect image format.
      imageSrc: result.oEmbed?.thumbnails?.[0] ?? result.open_graph?.images?.[0]?.url,
      url,
    }
  } catch (e) {
    let data = {
      title: null,
      description: null,
      favicon: null,
      imageSrc: null,
      url,
    }
    if (url.includes('linkedin.com')) {
      data.imageSrc = 'https://www.linkedin.com/favicon.ico'
      data.title = 'Linkedin'
      data.description =
        '1 billion members | Manage your professional identity. Build and engage with your professional network. Access knowledge, insights and opportunities.'
    }
    return data
  }
}
