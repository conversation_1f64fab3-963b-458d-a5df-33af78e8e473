import { Prisma } from '@prisma/client'

import prisma, { mapNullToJsonNull } from './prisma'
import {
  createStreamChannel,
  createStreamMessage,
  deleteStreamChannel,
  deleteStreamChannels,
  getStreamChannels,
  getStreamMessage,
  updateStreamChannel,
  updateStreamMessage,
} from '@/shared-libs/stream-chat'
import { CHANNEL_TYPE_ENUM, USER_ROLE_ENUM } from '@/shared-types/enum'
import { ISparkMembershipCategory } from '@/shared-types/interfaces'

const jsonFields = ['question_sequences']

const upsertStreamMessage = async (mCategory: ISparkMembershipCategory, userId: string) => {
  const messageId = `${mCategory.id}-${mCategory.active_question_id}`
  const messageData = {
    text: '',
  }
  const temp = await getStreamMessage(messageId)
  let result

  if (temp) {
    result = await updateStreamMessage(messageId, userId, messageData)
  } else {
    result = await createStreamMessage(CHANNEL_TYPE_ENUM.team, mCategory.id, {
      ...messageData,
      id: messageId,
      user_id: userId,
    })
  }
}

export async function createSparkMembershipCategory(payload: Prisma.SparkMembershipCategoryCreateArgs, userId: string) {
  const result = (await prisma.sparkMembershipCategory.create(payload)) as ISparkMembershipCategory
  await createStreamChannel(CHANNEL_TYPE_ENUM.team, result.id, {
    created_by_id: userId,
    name: '',
    team: result.membership_id,
    custom_type: 'spark',
    custom_started: result.started,
  })
  if (result.active_question_id) {
    await upsertStreamMessage(result, userId)
  }
  return result
}

export async function findSparkMembershipCategoryById(payload: Prisma.SparkMembershipCategoryFindUniqueArgs) {
  const result = await prisma.sparkMembershipCategory.findUnique(payload)
  return result as ISparkMembershipCategory
}

export async function findSparkMembershipCategory(payload: Prisma.SparkMembershipCategoryFindFirstArgs) {
  const result = await prisma.sparkMembershipCategory.findFirst(payload)
  return result
}

export async function findSparkMembershipCategories(payload: Prisma.SparkMembershipCategoryFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.sparkMembershipCategory.findMany(args)
  const total = await prisma.sparkMembershipCategory.count({ where })
  return { docs: result, total }
}

export async function updateSparkMembershipCategory(payload: Prisma.SparkMembershipCategoryUpdateArgs) {
  mapNullToJsonNull(jsonFields, payload.data)
  const result = (await prisma.sparkMembershipCategory.update(payload)) as ISparkMembershipCategory

  if (result?.id && payload.data.active_question?.connect?.id) {
    const membership = await prisma.membership.findUnique({
      where: { id: result.membership_id },
      include: {
        users: {
          where: {
            role: USER_ROLE_ENUM.owner,
          },
        },
      },
    })

    if (membership.users[0]) {
      const existingStreamChannels = await getStreamChannels({
        type: CHANNEL_TYPE_ENUM.team,
        id: { $eq: result.id },
      })
      if (existingStreamChannels?.length) {
        await updateStreamChannel(
          CHANNEL_TYPE_ENUM.team,
          result.id,
          {
            active_question_id: result.active_question_id,
            custom_started: result.started,
          },
          [],
        )
      } else {
        await createStreamChannel(CHANNEL_TYPE_ENUM.team, result.id, {
          created_by_id: membership.users[0].id,
          name: '',
          team: result.membership_id,
          custom_type: 'spark',
          custom_started: result.started,
        })
      }
    }
    if (result.active_question_id) {
      await upsertStreamMessage(result, membership.users[0].id)
    }
  }
  // const membership = await prisma.membership.findUnique({
  //   where: { id: result.membership_id },
  //   include: {
  //     users: {
  //       where: {
  //         role: USER_ROLE_ENUM.owner
  //       }
  //     }
  //   }
  // })
  // if (membership.users[0]) {
  //   createStreamChannel(CHANNEL_TYPE_ENUM.team, result.id, {
  //     created_by_id: membership.users[0].id,
  //     name: '',
  //     team: result.membership_id,
  //   })
  // }
  // if (result.active_question_id) {
  //   upsertStreamMessage(result, membership.users[0].id, true)
  // }
  return result
}

export async function updateSparkQuestionCategories(payload: Prisma.SparkMembershipCategoryUpdateManyArgs) {
  mapNullToJsonNull(jsonFields, payload.data)
  const result = await prisma.sparkMembershipCategory.updateMany(payload)
  return result
}

export async function deleteSparkMembershipCategories(args: Prisma.SparkMembershipCategoryFindManyArgs) {
  const categories = await findSparkMembershipCategories(args)
  if (categories.docs.length) {
    const streamChannelIds = categories.docs.map((c) => `${CHANNEL_TYPE_ENUM.team}:${c.id}`)
    deleteStreamChannels(streamChannelIds)

    const result = await prisma.sparkMembershipCategory.deleteMany({
      where: {
        id: { in: categories.docs.map((d) => d.id) },
      },
    })
    return result
  }
  return { count: 0 }
}

export async function deleteSparkMembershipCategoryById(id: string) {
  deleteStreamChannel(`${CHANNEL_TYPE_ENUM.team}:${id}`)
  const result = await prisma.sparkMembershipCategory.delete({
    where: {
      id,
    },
  })
  return result as ISparkMembershipCategory
}
