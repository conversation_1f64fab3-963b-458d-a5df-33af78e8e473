import { PAYMENT_STATUS_ENUM, PAYMENT_TYPE_ENUM } from '@prisma/client'

import prisma from '@/shared-libs/prisma/prisma'
import { IPayment } from '@/shared-types/interfaces'

export async function createPayment(payload) {
  const result = await prisma.payment.create(payload)
  return result as IPayment
}

// Helper function to map Stripe status to Payment status
export function mapStripeStatusToPaymentStatus(status: string): PAYMENT_STATUS_ENUM {
  switch (status) {
    case 'paid':
    case 'succeeded':
      return PAYMENT_STATUS_ENUM.paid
    case 'refunded':
      return PAYMENT_STATUS_ENUM.refunded
    case 'failed':
      return PAYMENT_STATUS_ENUM.failed
    default:
      return PAYMENT_STATUS_ENUM.pending
  }
}

// Helper function to format payment description with bold text for membership names and "MemberUp"
export function formatPaymentDescription(paymentType: PAYMENT_TYPE_ENUM, membershipName?: string): string {
  if (paymentType === PAYMENT_TYPE_ENUM.membership && membershipName) {
    return `Payment for membership **${membershipName}**`
  } else if (paymentType === PAYMENT_TYPE_ENUM.memberup) {
    return 'Payment for **MemberUp**'
  } else {
    return 'Payment'
  }
}
