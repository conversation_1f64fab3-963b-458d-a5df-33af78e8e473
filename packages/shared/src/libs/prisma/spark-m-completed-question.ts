import prisma from './prisma'

export async function findSparkCompletedQuestions(categoryId: string, membershipId: string) {
  const result = await prisma.$queryRaw`
  SELECT
    smq.id,
        CAST(COALESCE(COUNT(sr.id), 0) AS CHAR) AS responses_count,
        COALESCE(sqs.content, sq.content) as content,
        COALESCE(sqs.answer, sq.answer) as answer,
        max(smq.updatedAt) as 'completed_at'
    FROM
    spark_m_questions smq
    LEFT JOIN spark_responses sr
    ON
    smq.id = sr.m_question_id
    INNER JOIN spark_questions sq
    ON
    sq.id = smq.question_id
    LEFT JOIN spark_questions_settings sqs
    ON
    sqs.question_id = sq.id AND sqs.membership_id = ${membershipId}
    WHERE
    smq.status = 'completed'
    AND smq.membership_id = ${membershipId}
    AND sq.category_id = ${categoryId}
    AND (sqs.membership_id = ${membershipId}
    or sqs.membership_id is null)
    GROUP BY
    smq.id,
        COALESCE(sqs.content, sq.content),
        COALESCE(sqs.answer, sq.answer)
    ORDER BY
    completed_at desc`

  return { docs: result as any[], total: 0 }
}
