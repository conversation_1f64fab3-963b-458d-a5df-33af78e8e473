import { Prisma } from '@prisma/client'

import { getFullName } from '../profile'
import { generateNextSlug, slugify } from '../string-utils'
import { deleteActionHistory } from './actions'
import prisma, { boostedPrisma, mapNullToJsonNull } from './prisma'
import { inngest } from '@memberup/shared/src/services/inngest'
import { deleteRecordsFromAlgoliaIndex, uploadDataToAlgoliaIndex } from '@/shared-libs/algolia'
import {
  createStreamMessage,
  deleteStreamMessage,
  getStreamMessage,
  updateStreamMessage,
} from '@/shared-libs/stream-chat'
import { CHANNEL_TYPE_ENUM, FEED_TYPE_ENUM } from '@/shared-types/enum'
import { IFeed } from '@/shared-types/interfaces'

const jsonFields = ['attachments', 'reports', 'metadata']

const MU_ID = process.env.NEXT_PUBLIC_MU_ID

export const prepareAndUpdateStreamMessage = async (data: IFeed) => {
  // TODO: Why do we need to copy each field manually?
  const messageData = {
    // space_type: feed.channel.space_type, //custom field
    feed_type: data.feed_type, //custom field
    feed_status: data.feed_status, //custom field
    reports: data.reports, //custom field
    text: data.text,
    title: data.title,
    attachments: data.attachments,
    mentioned_users: data.metadata?.mentioned_users
      ? data.metadata?.mentioned_users.map((item) => item.id).filter((item) => item !== '-1')
      : [],
    permalink: data.permalink,
    links: data.metadata?.links,
    reply_parent_id: data.reply_parent_id,
    hierarchy_order: data.hierarchy_order,
    edited: data.edited,
    last_activity_at: data.last_activity_at,
  }

  if (data.html) {
    messageData['html'] = data.html
  }

  if (data.attachments?.length) {
    messageData['attachments'] = data.attachments.map((item) => {
      if (item.mimetype) {
        return {
          id: item.id,
          filename: item.filename,
          is_gif: item.is_gif,
          mimetype: item.mimetype,
          url: item.url,
          embed_url: item.embed_url,
          mux_asset: item.mux_asset,
          mux_upload_id: item.mux_upload_id,
          mux_upload_url: item.mux_upload_url,
          passthrough: item.passthrough,
          size_in_bytes: item.size_in_bytes ?? null,
          thumbnail: item.thumbnail ?? null,
          show_preview: item.show_preview,
        }
      }
      return item
    }) //custom field
  }

  let temp = await getStreamMessage(data.id)
  if (temp) {
    return await updateStreamMessage(data.id, data.user_id, messageData)
  }
}

export const upsertStreamMessage = async (channelType, data: IFeed) => {
  // TODO: Why do we need to copy each field manually?
  const messageData = {
    // space_type: feed.channel.space_type, //custom field
    feed_type: data.feed_type, //custom field
    feed_status: data.feed_status, //custom field
    reports: data.reports, //custom field
    text: data.text,
    title: data.title,
    attachments: data.attachments,
    mentioned_users: data.metadata?.mentioned_users
      ? data.metadata?.mentioned_users.map((item) => item.id).filter((item) => item !== '-1')
      : [],
    permalink: data.permalink,
    links: data.metadata?.links,
    reply_parent_id: data.reply_parent_id,
    hierarchy_order: data.hierarchy_order,
    edited: data.edited,
    last_activity_at: data.last_activity_at,
  }

  if (data.html) {
    messageData['html'] = data.html
  }

  if (data.attachments?.length) {
    messageData['attachments'] = data.attachments.map((item) => {
      if (item.mimetype) {
        return {
          id: item.id,
          filename: item.filename,
          is_gif: item.is_gif,
          mimetype: item.mimetype,
          url: item.url,
          embed_url: item.embed_url,
          mux_asset: item.mux_asset,
          mux_upload_id: item.mux_upload_id,
          mux_upload_url: item.mux_upload_url,
          passthrough: item.passthrough,
          size_in_bytes: item.size_in_bytes ?? null,
          thumbnail: item.thumbnail ?? null,
          show_preview: item.show_preview,
        }
      }
      return item
    }) //custom field
  }

  let temp = await getStreamMessage(data.id)
  if (temp) {
    return await updateStreamMessage(data.id, data.user_id, messageData)
  } else {
    messageData['id'] = data.id
    messageData['user_id'] = data.user_id
    if (data.parent_id) {
      messageData['parent_id'] = data.parent_id
    }

    return await createStreamMessage(channelType, data.channel_id, messageData as any)
  }
}

// TODO: Rename to createPost
export async function createFeed(payload: Prisma.FeedCreateArgs) {
  const { title, feed_type } = payload.data
  if (feed_type === FEED_TYPE_ENUM.default) {
    // Generate the slugified title
    let feedSlug = slugify(title)
    let latestFeedSlug = await getLatestFeedSlug(feedSlug)
    feedSlug = generateNextSlug(latestFeedSlug, feedSlug)
    payload.data.permalink = feedSlug
  }

  // Compute the hierarchy order
  if (payload.data.feed_type === FEED_TYPE_ENUM.comment) {
    const queryResult = await prisma.feed.findUnique({
      where: {
        id: payload.data.reply_parent_id,
      },
      select: {
        hierarchy_order: true,
        replies: {
          select: {
            hierarchy_order: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
      },
    })
    if (payload.data.parent_id !== payload.data.reply_parent_id) {
      // Second level comment, the parent has a hierarchy and we also look into the latest child for next sequence if exists.
      const parentHierarchyOrder = queryResult.hierarchy_order
      // This is an old comment, so also don't produce any hierarchy order.
      if (!parentHierarchyOrder) {
        payload.data.hierarchy_order = null
      } else {
        const childHierarchyOrder = queryResult.replies.length > 0 ? queryResult.replies[0].hierarchy_order : null

        let hierarchyOrder = `${parentHierarchyOrder}.0000`
        if (childHierarchyOrder) {
          const parts = childHierarchyOrder.split('.')
          if (parts.length == 2) {
            let currentSeq = parseInt(parts[1])
            if (isNaN(currentSeq)) {
              currentSeq = 0
            }
            const nextSeq = currentSeq + 1
            hierarchyOrder = `${parts[0]}.${nextSeq.toString().padStart(4, '0')}`
          }
        }
        payload.data.hierarchy_order = hierarchyOrder
      }
    } else {
      // Top level comment, the parent does not have any hierarchy, so we look for the latest child for next sequence if exists.
      let hierarchyOrder = 0
      if (queryResult?.replies?.length > 0) {
        const childHierachyOrder = queryResult.replies[0].hierarchy_order
        let currentSeq = parseInt(childHierachyOrder)
        if (isNaN(currentSeq)) {
          currentSeq = 0
        }
        hierarchyOrder = currentSeq + 1
      }
      payload.data.hierarchy_order = hierarchyOrder.toString().padStart(4, '0')
    }
  }

  const replyParentId = payload.data.reply_parent_id
  delete payload.data.reply_parent_id // NOTE: We do this to not fail the prisma create.
  if (replyParentId) {
    payload.data.replyParent = {
      connect: {
        id: replyParentId,
      },
    }
  }

  const channelType = payload.data['channel_type']
  delete payload.data['channel_type']

  const data = payload.data

  const result = (await prisma.feed.create({
    data: data,
    include: {
      user: {
        include: { profile: true },
      },
      channel: true,
      ...(payload.include || {}),
    },
  })) as unknown as IFeed

  let upserResult
  if (result?.id) {
    upserResult = await upsertStreamMessage(channelType, result)
    if (result.feed_type === FEED_TYPE_ENUM.default) {
      uploadDataToAlgoliaIndex('feed', result)
    }
  }
  return { result, getStreamMessage: upserResult?.message }
}

export async function findFeedById(id: string, options?: any) {
  const result = await prisma.feed.findUnique({
    where: {
      id,
    },
    ...options,
  })
  return result as IFeed
}

export async function findFeeds(payload: Prisma.FeedFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.feed.findMany(args)
  const total = await prisma.feed.count({ where })
  return { docs: result as IFeed[], total }
}

export async function findFeedsCount(payload: Prisma.FeedFindManyArgs) {
  const { where } = payload
  const total = await prisma.feed.count({ where })
  return total
}

export async function updateFeed(payload: Prisma.FeedUpdateArgs) {
  mapNullToJsonNull(jsonFields, payload.data)
  delete (payload.data as any).membership_id
  const result = (await prisma.feed.update({
    ...payload,
    include: {
      user: { include: { profile: true } },
      channel: true,
      ...(payload.include || {}),
    },
  })) as unknown as IFeed
  let updatedStreamMessage = null
  if (result?.id) {
    updatedStreamMessage = await prepareAndUpdateStreamMessage(result)
    if (!result.parent_id) {
      uploadDataToAlgoliaIndex('feed', result)
    }
  }
  return { result, updatedStreamMessage }
}

export async function updateFeeds(payload: Prisma.FeedUpdateManyArgs) {
  mapNullToJsonNull(jsonFields, payload.data)
  const result = await prisma.feed.updateMany(payload)
  return result
}

export async function deleteFeedByIdAndType(id: string, type: string, userId: string) {
  if (!id) {
    return
  }

  const referenceIds = []

  if (type === 'comment') {
    console.log('deleting comment')
    // For top level comments we want to delete all the related replies.
    const replies = await prisma.feed.findMany({
      select: { id: true, user_id: true },
      where: { reply_parent_id: id },
    })

    const replyIds = replies.map((c) => {
      referenceIds.push({ id: c.id, userId: c.user_id, type: 'comment' })
      return c.id
    })

    if (replyIds.length > 0) {
      await prisma.feed.deleteMany({ where: { reply_parent_id: id } })
    }

    referenceIds.push({ id: id, userId, type: 'comment' })
    await prisma.feed.delete({
      where: {
        id,
      },
    })
    await deleteStreamMessage(id)
    const postsToDelete = [id, ...replyIds]
    console.log('deleting posts from Algolia ', JSON.stringify(postsToDelete))
    deleteRecordsFromAlgoliaIndex('feed', postsToDelete)

    // Getstream does not delete replies so we must use inngest to back our endpoint back
    try {
      await inngest.send({ name: 'memberup/feed.deleted', data: { relatedFeedIds: replyIds } })
    } catch (e) {
      console.log(e.message)
    }
  } else if (type === 'default') {
    console.log('deleting post')
    // Handle second level comments deletion.
    const secondLevelComments = await prisma.feed.findMany({
      select: { id: true, user_id: true },
      where: { parent_id: id, reply_parent_id: { not: id } },
    })
    const secondLevelCommentsIds = secondLevelComments.map((c) => {
      referenceIds.push({ id: c.id, userId: c.user_id, type: 'comment' })
      return c.id
    })

    if (secondLevelCommentsIds.length > 0) {
      await prisma.feed.deleteMany({
        where: { parent_id: id, reply_parent_id: { not: id } },
      })
    }

    // Handle top level comments deletion
    const topLevelComments = await prisma.feed.findMany({
      select: { id: true, user_id: true },
      where: { reply_parent_id: id, parent_id: id },
    })

    const topLevelCommentsIds = topLevelComments.map((c) => {
      referenceIds.push({ id: c.id, userId: c.user_id, type: 'comment' })
      return c.id
    })

    if (topLevelCommentsIds.length > 0) {
      await prisma.feed.deleteMany({
        where: { reply_parent_id: id, parent_id: id },
      })
    }

    let result = null
    referenceIds.push({ id: id, userId, type: 'default' })

    try {
      result = await prisma.feed.delete({
        where: {
          id,
        },
      })
    } catch (e) {
      console.log(e)
    }

    await deleteStreamMessage(id)
    const postsToDelete = [id, ...secondLevelCommentsIds, ...topLevelCommentsIds]
    console.log('deleting posts from Algolia: ', JSON.stringify(postsToDelete))
    deleteRecordsFromAlgoliaIndex('feed', postsToDelete)
  }

  const postCreated = await prisma.action.findFirst({
    where: { action_name: 'POST_CREATED' },
  })
  const commentCreated = await prisma.action.findFirst({
    where: { action_name: 'COMMENT_CREATED' },
  })

  for (const referenceId of referenceIds) {
    await deleteActionHistory(
      referenceId.userId,
      referenceId.type === 'comment' ? commentCreated : postCreated,
      referenceId.id,
    )
  }
}

export async function deleteFeedById(id: string) {
  // Handle second level comments deletion.
  const secondLevelComments = await prisma.feed.findMany({
    where: { parent_id: id, reply_parent_id: { not: id } },
  })

  const secondLevelCommentsIds = secondLevelComments.map((c) => c.id)
  if (secondLevelCommentsIds.length > 0) {
    await prisma.feed.deleteMany({
      where: { parent_id: id, reply_parent_id: { not: id } },
    })
  }

  // Handle top level comments deletion
  const topLevelComments = await prisma.feed.findMany({
    where: { reply_parent_id: id, parent_id: id },
  })
  const topLevelCommentsIds = topLevelComments.map((c) => c.id)
  if (topLevelCommentsIds.length > 0) {
    await prisma.feed.deleteMany({
      where: { reply_parent_id: id, parent_id: id },
    })
  }

  const relatedIdsToDelete = [...secondLevelCommentsIds, ...topLevelComments]
  if (relatedIdsToDelete.length > 0) {
    try {
      await inngest.send({
        name: 'memberup/feed.deleted',
        data: { relatedFeedIds: topLevelCommentsIds },
      })
    } catch (e) {
      console.log(e.message)
    }
  }

  // NOTE: We want to try to delete both records regardless of an out of sync between db and getstream.
  let result = null
  try {
    result = await prisma.feed.delete({
      where: {
        id,
      },
    })
  } catch (e) {
    console.log(e)
  }

  deleteStreamMessage(id)
  // TODO: Check algolia records cleanup
  // if (!result.parent_id) {
  //   deleteRecordsFromAlgoliaIndex('feed', [id as string])
  // }
  deleteRecordsFromAlgoliaIndex('feed', [id as string])
  return result
}

export async function findFeedByPermalink(permalink: string, membershipId: string) {
  if (!permalink) {
    throw new Error('Permalink is required')
  }

  const where = {
    permalink,
  }

  // TODO: review code and remove this check
  // NOTE: In case of MemberUp university we don't want to look for posts created by a user with the same membership_id as the requesting user.
  // The ideal fix should be to add the membership_id to the feeds table and this could be done with the unified account functionality.
  if (membershipId !== MU_ID) {
    where['user'] = {
      membership_id: membershipId,
    }
  }
  const result = await prisma.feed.findFirst({
    where,
  })

  return result as IFeed
}

export async function upsertFeedTrack(userId: string, feedId: string, updatedAt: number) {
  try {
    const result = await prisma.feedTrack.upsert({
      where: {
        user_id_feed_id: {
          user_id: userId,
          feed_id: feedId,
        },
      },
      create: {
        user_id: userId,
        feed_id: feedId,
        updatedAt: new Date(updatedAt * 1000),
      },
      update: {
        updatedAt: new Date(updatedAt * 1000),
      },
    })
    return result
  } catch (err) {
    return
  }
}

export async function getFeedTracksByUserId(userId: string) {
  // using cache boost instance
  const result = await boostedPrisma.feedTrack.findMany({
    where: {
      user_id: userId,
    },
    take: 1000,
    orderBy: {
      updatedAt: 'desc',
    },
  })
  return result
}

export async function getLatestFeedSlug(slug: string): Promise<string> {
  const sanitizedKeyword = `^${slug}-[0-9]+$`
  const result: any =
    await prisma.$queryRaw`SELECT permalink FROM feeds WHERE permalink REGEXP ${sanitizedKeyword} ORDER BY createdAt DESC LIMIT 1;`
  return result?.[0]?.permalink
}

export async function getComments(parentId: string) {
  return await prisma.feed.findMany({
    where: {
      parent_id: parentId,
      feed_type: 'comment',
    },
    orderBy: {
      createdAt: 'desc',
    },
    take: 100,
    include: {
      user: {
        include: {
          profile: true,
        },
      },
    },
  })
}

// TODO: We don't want to get the user image as we are going to resolve this in the UI, with the user name and id is enough.
// TODO: Instead of all of this we could do a single database query to return the latest distinct commenters, we don't actually need to get the comments.
export async function getFeaturedCommenters(parentId) {
  const comments = await getComments(parentId)
  const uniqueCommenters = []
  const commenterSet = new Set()

  for (const comment of comments) {
    const userId = comment.user_id

    if (!commenterSet.has(userId)) {
      const { first_name: firstName, last_name: lastName } = comment.user
      const commenter = {
        id: userId,
        name: getFullName(firstName, lastName, ''),
        image: comment.user.profile.image,
        image_crop_area: comment.user.profile.image_crop_area,
      }
      uniqueCommenters.push(commenter)
      commenterSet.add(userId)

      // If we've already found 5 unique commenters, break out of the loop
      if (uniqueCommenters.length === 5) {
        break
      }
    }
  }

  return uniqueCommenters
}
