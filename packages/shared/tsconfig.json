{"extends": "tsconfig/react-library.json", "include": ["."], "exclude": ["dist", "build", "node_modules"], "compilerOptions": {"baseUrl": ".", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "preserveWatchOutput": true, "skipLibCheck": true, "noEmit": true, "allowImportingTsExtensions": true, "paths": {"@/shared-components/*": ["./src/components/*"], "@/shared-config/*": ["./src/config/*"], "@/shared-libs/*": ["./src/libs/*"], "@/shared-models/*": ["./src/models/*"], "@/shared-services/*": ["./src/services/*"], "@/shared-settings/*": ["./src/settings/*"], "@/shared-styles/*": ["./src/styles/*"], "@/shared-types/*": ["./src/types/*"], "@/*": ["../../apps/memberup/*"]}, "lib": ["dom"]}}