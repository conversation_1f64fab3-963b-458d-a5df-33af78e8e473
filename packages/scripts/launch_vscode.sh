#!/bin/sh

# ------------------------------------------------------------------------------
# Launches VSCode while loading environment variables from a .env file
# located at the repository root directory, as well as
# all Doppler environment variables except for those already defined.
#
# Usage:
#
# $ sh packages/scripts/load_vscode.sh
# ------------------------------------------------------------------------------

ENV_FILE_DIRECTORY=$(dirname "$(dirname "$(dirname "$_")")")
ENV_FILE="$ENV_FILE_DIRECTORY/.env"

if test -f $ENV_FILE; then
  export $(xargs < $ENV_FILE)

  echo "Launching VSCode with environment variables loaded from $(realpath $ENV_FILE) and Doppler"
  doppler run --preserve-env code
else
  echo "No .env file found at $(realpath $ENV_FILE_DIRECTORY). Launching VSCode with environment variables loaded from Doppler."
  doppler run code
fi

