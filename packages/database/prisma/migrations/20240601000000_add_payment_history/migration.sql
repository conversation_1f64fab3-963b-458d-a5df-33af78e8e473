-- CreateEnum
CREATE TYPE "PAYMENT_HISTORY_TYPE_ENUM" AS ENUM ('membership', 'memberup');

-- CreateEnum
CREATE TYPE "PAYMENT_HISTORY_STATUS_ENUM" AS ENUM ('paid', 'refunded', 'failed', 'pending');

-- CreateTable
CREATE TABLE "payment_history" (
    "id" TEXT NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'usd',
    "description" TEXT,
    "payment_date" TIMESTAMP(3),
    "status" "PAYMENT_HISTORY_STATUS_ENUM" NOT NULL,
    "payment_type" "PAYMENT_HISTORY_TYPE_ENUM" NOT NULL,
    "stripe_invoice_id" TEXT,
    "stripe_payment_intent_id" TEXT,
    "stripe_charge_id" TEXT,
    "stripe_receipt_url" TEXT,
    "stripe_customer_id" TEXT,
    "user_id" TEXT NOT NULL,
    "membership_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "payment_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "payment_history_user_id_idx" ON "payment_history"("user_id");

-- CreateIndex
CREATE INDEX "payment_history_membership_id_idx" ON "payment_history"("membership_id");

-- CreateIndex
CREATE INDEX "payment_history_stripe_invoice_id_idx" ON "payment_history"("stripe_invoice_id");

-- CreateIndex
CREATE INDEX "payment_history_stripe_customer_id_idx" ON "payment_history"("stripe_customer_id");

-- AddForeignKey
ALTER TABLE "payment_history" ADD CONSTRAINT "payment_history_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment_history" ADD CONSTRAINT "payment_history_membership_id_fkey" FOREIGN KEY ("membership_id") REFERENCES "memberships"("id") ON DELETE SET NULL ON UPDATE CASCADE;
