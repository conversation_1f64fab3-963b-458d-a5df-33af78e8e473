import { test as setup } from '@playwright/test'

import { UserRole, executeUserLogIn, getAuthFile } from './utils.ts'

setup('Authenticate as Admin user 1 (community 1)', async ({ page }, testInfo) => {
  testInfo.setTimeout(60_000)

  // Log in Admin user
  await executeUserLogIn(1, page, UserRole.Admin, 1)

  // Store the login state
  await page.context().storageState({ path: getAuthFile(1, UserRole.Admin, 1) })
})

setup('Authenticate as Member user 1 (community 1)', async ({ page }, testInfo) => {
  testInfo.setTimeout(60_000)

  // Log in Member user
  await executeUserLogIn(1, page, UserRole.Member, 1)

  // Store the login state
  await page.context().storageState({ path: getAuthFile(1, UserRole.Member, 1) })
})
