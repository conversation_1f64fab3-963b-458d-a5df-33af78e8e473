import { createSlice, current, PayloadAction } from '@reduxjs/toolkit'

import { RootState } from '../store'
import { IFeed } from '@memberup/shared/src/types/interfaces'
import { TAttachment, TError } from '@memberup/shared/src/types/types'

export interface FeedState {
  libraryFeed: any
  liveFeed: any
  feeds: IFeed[]
  hasMore: boolean
  next: string | null
  feedToDelete: IFeed | null
  attachmentToDeleteAction: { feed: IFeed; attachment: TAttachment; membershipId: string }
  previewToRemove: { feed: IFeed; attachment: TAttachment }
  requestUpsertFeed: boolean
  requestGetFeeds: boolean
  requestAddFeeds: IFeed[]
  requestDeleteFeeds: IFeed[]
  error: TError | null
  currentPostDraft: { [key: string]: any } | null
  editedCommentTracks: { [key: string]: boolean } | null
  hideSparkWarning: boolean
}

const initialState: FeedState = {
  libraryFeed: null,
  liveFeed: null,
  feeds: [],
  hasMore: true,
  next: null,
  feedToDelete: null,
  attachmentToDeleteAction: null,
  previewToRemove: null,
  requestUpsertFeed: false,
  requestGetFeeds: false,
  requestAddFeeds: [],
  requestDeleteFeeds: [],
  error: null,
  currentPostDraft: null,
  editedCommentTracks: {},
  hideSparkWarning: false,
}

export const feedSlice = createSlice({
  name: 'feedSlice',
  initialState,
  // The `reducers` field lets us define reducers and generate associated actions
  reducers: {
    upsertFeed: (
      state,
      action: PayloadAction<{
        data: Partial<IFeed>
        membershipId: string
        messages?: { success?: string; fail?: string }
      }>,
    ) => {
      state.error = null
      state.requestUpsertFeed = true
    },
    reportFeed: (
      state,
      action: PayloadAction<{
        data: Partial<IFeed>
        messages?: { success?: string; fail?: string }
      }>,
    ) => {
      state.error = null
      state.requestUpsertFeed = true
    },
    upsertFeedSuccess: (
      state,
      action: PayloadAction<{
        data: IFeed
      }>,
    ) => {
      state.requestUpsertFeed = false
    },
    upsertFeedFailure: (state, action: PayloadAction<TError>) => {
      state.error = action.payload
      state.requestUpsertFeed = false
    },
    deleteFeed: (
      state,
      action: PayloadAction<{
        id: string
        data: IFeed
      }>,
    ) => {
      const before = current(state)
      state.requestDeleteFeeds = [...before.requestDeleteFeeds, action.payload.data]
    },
    deleteFeedSuccess: (
      state,
      action: PayloadAction<{
        data: IFeed
      }>,
    ) => {
      const before = current(state)
      state.requestDeleteFeeds = before.requestDeleteFeeds.filter((a) => a !== action.payload.data)
      state.feedToDelete = null
    },
    deleteFeedFailure: (
      state,
      action: PayloadAction<{
        data: IFeed
      }>,
    ) => {
      const before = current(state)
      state.requestDeleteFeeds = before.requestDeleteFeeds.filter((a) => a !== action.payload.data)
    },
    setFeedToDelete: (state, action: PayloadAction<IFeed>) => {
      state.feedToDelete = action.payload
    },
    setHideSparkWarning: (state, action: PayloadAction<boolean>) => {
      state.hideSparkWarning = action.payload
    },
    setAttachmentToDeleteAction: (
      state,
      action: PayloadAction<{ feed: IFeed; attachment: TAttachment; membershipId: string }>,
    ) => {
      state.attachmentToDeleteAction = action.payload
    },
    setPreviewToRemove: (state, action: PayloadAction<{ feed: IFeed; attachment: TAttachment }>) => {
      state.previewToRemove = action.payload
    },
    setFeedError: (state, action: PayloadAction<TError>) => {
      state.error = action.payload
      state.requestUpsertFeed = false
      state.requestGetFeeds = false
    },
    setRequestUpsertFeed: (state, action: PayloadAction<{ value: boolean }>) => {
      state.requestUpsertFeed = action.payload.value
    },
    setCurrentPostDraft: (state, action: PayloadAction<{ [key: string]: any } | null>) => {
      state.currentPostDraft = action.payload
    },
    cleanCurrentPostDraft: (state) => {
      state.currentPostDraft = null
    },
    cleanEditedCommentTracks: (state) => {
      state.editedCommentTracks = {}
    },
    setEditedCommentTracks: (state, action: PayloadAction<{ id: string; operation: 'add' | 'remove' }>) => {
      const { id, operation } = action.payload
      if (operation === 'add') {
        state.editedCommentTracks = { ...state.editedCommentTracks, [id]: true }
      } else if (operation === 'remove') {
        const { [id]: _, ...rest } = state.editedCommentTracks
        state.editedCommentTracks = rest
      }
    },
  },
})

export const {
  upsertFeed,
  reportFeed,
  upsertFeedSuccess,
  upsertFeedFailure,
  deleteFeed,
  deleteFeedSuccess,
  deleteFeedFailure,
  setFeedToDelete,
  setAttachmentToDeleteAction,
  setHideSparkWarning,
  setPreviewToRemove,
  setFeedError,
  setCurrentPostDraft,
  cleanCurrentPostDraft,
  setEditedCommentTracks,
  cleanEditedCommentTracks,
  setRequestUpsertFeed,
} = feedSlice.actions
export const selectFeeds = (state: RootState) => state.feed.feeds
export const selectHasMore = (state: RootState) => state.feed.hasMore
export const selectRequestUpsertFeed = (state: RootState) => state.feed.requestUpsertFeed
export const selectRequestGetFeeds = (state: RootState) => state.feed.requestGetFeeds
export const selectFeedToDelete = (state: RootState) => state.feed.feedToDelete
export const selectHideSparkWarning = (state: RootState) => state.feed.hideSparkWarning

export const selectAttachmentToDeletAction = (state: RootState) => state.feed.attachmentToDeleteAction
export const selectPreviewToRemove = (state: RootState) => state.feed.previewToRemove
export const selectFeedError = (state: RootState) => state.feed.error
export const selectCurrentPostDraft = (state: RootState) => state.feed.currentPostDraft
export const selectEditedCommentTracks = (state: RootState) => state.feed.editedCommentTracks

export default feedSlice.reducer
