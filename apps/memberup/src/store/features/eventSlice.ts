import { createSlice, current, PayloadAction } from '@reduxjs/toolkit'
import _groupBy from 'lodash/groupBy'

import { RootState } from '../store'
import { EVENT_STATUS_ENUM, EVENT_VIEW_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { IEvent } from '@memberup/shared/src/types/interfaces'
import { TError, TGetApiParams } from '@memberup/shared/src/types/types'

export interface EventState {
  membershipId: string
  eventViewType: EVENT_VIEW_TYPE_ENUM
  events: {
    total: number
    docs: IEvent[]
  }
  requestDeleteEvents: string[]
  requestGetEvents: boolean
  requestUpsertEvent: boolean
  error: TError
}

const initialState: EventState = {
  membershipId: null,
  eventViewType: EVENT_VIEW_TYPE_ENUM.upcoming,
  events: {
    total: 1,
    docs: [],
  },
  requestDeleteEvents: [],
  requestGetEvents: false,
  requestUpsertEvent: false,
  error: null,
}

export const eventSlice = createSlice({
  name: 'eventSlice',
  initialState,
  // The `reducers` field lets us define reducers and generate associated actions
  reducers: {
    // Use the PayloadAction type to declare the contents of `action.payload`
    getEvents: (
      state,
      action: PayloadAction<{
        membershipId: string
        eventViewType: EVENT_VIEW_TYPE_ENUM
        params: TGetApiParams
      }>,
    ) => {
      state.error = null
      state.eventViewType = action.payload.eventViewType
      state.events = {
        total: 1,
        docs: [],
      }
      state.requestGetEvents = true
    },
    getEventsSuccess: (state, action: PayloadAction<{ init: boolean; total: number; docs: IEvent[] }>) => {
      const before = current(state)
      state.events = {
        total: action.payload.total,
        docs: action.payload.init ? action.payload.docs || [] : [...before.events.docs, ...(action.payload.docs || [])],
      }
      state.requestGetEvents = false
    },
    getEventsFailure: (state, action: PayloadAction<TError>) => {
      const before = current(state)
      state.error = action.payload
      state.requestGetEvents = false
    },
    upsertEvent: (state, action: PayloadAction<{ membershipId: string; data: Partial<IEvent> }>) => {
      state.error = null
      state.requestUpsertEvent = true
    },
    upsertEventSuccess: (state, action: PayloadAction<IEvent>) => {
      const before = current(state)
      let flag = '' // insert/update/delete
      const index = before.events.docs.findIndex((d) => d.id === action.payload.id)
      if (
        (before.eventViewType === EVENT_VIEW_TYPE_ENUM.draft && action.payload.status === EVENT_STATUS_ENUM.drafts) ||
        (before.eventViewType !== EVENT_VIEW_TYPE_ENUM.draft && action.payload.status === EVENT_STATUS_ENUM.published)
      ) {
        if (index < 0) {
          flag = 'insert'
        } else {
          const now = Math.floor(new Date().getTime() / 1000)
          if (
            before.eventViewType === EVENT_VIEW_TYPE_ENUM.draft ||
            (before.eventViewType === EVENT_VIEW_TYPE_ENUM.upcoming && action.payload.end_time > now) ||
            (before.eventViewType === EVENT_VIEW_TYPE_ENUM.past && action.payload.end_time < now)
          ) {
            flag = 'update'
          } else {
            flag = 'delete'
          }
        }
      } else {
        flag = 'delete'
      }

      if (flag === 'insert') {
        state.events.total = state.events.total + 1
        state.events.docs = [...state.events.docs, action.payload].sort((a, b) => a.start_time - b.start_time)
      } else if (flag === 'update') {
        state.events.docs = [
          ...before.events.docs.slice(0, index),
          action.payload,
          ...before.events.docs.slice(index + 1),
        ].sort((a, b) => a.start_time - b.start_time)
      } else if (flag === 'delete') {
        state.events.docs = before.events.docs.filter((item) => item.id !== action.payload.id)
        state.events.total = state.events.total - 1
      }
      state.requestUpsertEvent = false
    },
    upsertEventFailure: (state, action: PayloadAction<TError>) => {
      state.error = action.payload
      state.requestUpsertEvent = false
    },
    deleteEvent: (state, action: PayloadAction<{ id: string; membershipId: string }>) => {
      const before = current(state)
      state.error = null
      state.requestDeleteEvents = [...before.requestDeleteEvents, action.payload.id]
    },
    deleteEventSuccess: (state, action: PayloadAction<string>) => {
      const before = current(state)
      state.requestUpsertEvent = false
      state.requestDeleteEvents = before.requestDeleteEvents.filter((e) => e !== action.payload)
      state.events.total = state.events.total - 1
      state.events.docs = state.events.docs.filter((d) => d.id !== action.payload)
    },
    deleteEventFailure: (state, action: PayloadAction<{ error: TError; id: string }>) => {
      const before = current(state)
      state.error = action.payload.error
      state.requestDeleteEvents = before.requestDeleteEvents.filter((e) => e !== action.payload.id)
    },
  },
})

export const {
  getEvents,
  getEventsSuccess,
  getEventsFailure,
  upsertEvent,
  upsertEventSuccess,
  upsertEventFailure,
  deleteEvent,
  deleteEventSuccess,
  deleteEventFailure,
} = eventSlice.actions
export const selectEvents = (state: RootState, type: string, nowDate: Date) => {
  const now = Math.floor(nowDate.getTime() / 1000)
  if (type === 'now') {
    const docs = state.event.events.docs.filter((item) => {
      if (now < item.start_time || now > item.end_time) return false
      const eventDate = new Date(item.start_time * 1000)
      return (
        nowDate.getFullYear() === eventDate.getFullYear() &&
        nowDate.getMonth() === eventDate.getMonth() &&
        nowDate.getDate() === eventDate.getDate()
      )
    })

    return {
      total: docs.length,
      docs,
    }
  }

  let docs = state.event.events.docs.map((item) => {
    const eventDate = new Date(item.start_time * 1000)
    return {
      ...item,
      year: eventDate.getFullYear(),
      month: eventDate.getMonth() + 1,
    }
  })

  docs = type === EVENT_VIEW_TYPE_ENUM.upcoming ? docs.filter((item) => item.start_time > now) : docs
  const groupedDocs = _groupBy(docs, (item) => `${item.year}+${item.month < 10 ? '0' : ''}${item.month}`)

  return {
    total: docs.length,
    docs: groupedDocs,
  }
}
export const selectRequestDeleteEvent = (state: RootState, id: string) =>
  state.event.requestDeleteEvents.indexOf(id) >= 0
export const selectRequestGetEvents = (state: RootState) => state.event.requestGetEvents
export const selectRequestUpsertEvent = (state: RootState) => state.event.requestUpsertEvent
export default eventSlice.reducer
