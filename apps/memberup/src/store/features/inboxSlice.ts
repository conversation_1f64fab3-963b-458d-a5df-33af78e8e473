import { createSlice } from '@reduxjs/toolkit'

import { RootState } from '../store'

export interface InboxState {
  refreshChannels: number
  channels: any[]
  scrollBottomObservers: any
}

const initialState: InboxState = {
  refreshChannels: 0,
  channels: [],
  scrollBottomObservers: {},
}

export const inboxSlice = createSlice({
  name: 'inboxSlice',
  initialState,
  reducers: {
    requestRefreshChannels: (state) => {
      state.refreshChannels = state.refreshChannels + 1
    },
    setChannels: (state, action) => {
      // Add this action
      state.channels = action.payload
    },
  },
})

export const { requestRefreshChannels, setChannels /* , setScrollBottomObserver */ } = inboxSlice.actions
export const selectRefreshChannels = (state: any) => state.refreshChannels
export const selectTotalUnreadCount = (state: RootState) =>
  state.inbox.channels.reduce((total, channel) => total + channel.state.unreadCount, 0)
export default inboxSlice.reducer
