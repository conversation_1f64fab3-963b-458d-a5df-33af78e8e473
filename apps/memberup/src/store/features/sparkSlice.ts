import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import { RootState } from '../store'

export interface SparkState {
  responses: any[]
}

const initialState: SparkState = {
  responses: [],
}

export const sparkSlice = createSlice({
  name: 'sparkSlice',
  initialState,
  reducers: {
    setSparkResponses: (state, action: PayloadAction<any>) => {
      state.responses = action.payload
    },
  },
})

export const { setSparkResponses } = sparkSlice.actions
export const selectSparkResponses = (state: RootState) => state.spark.responses
export default sparkSlice.reducer
