import PeopleAltOutlinedIcon from '@mui/icons-material/PeopleAltOutlined'
import Typography from '@mui/material/Typography'
import Link from 'next/link'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { openDialog } from '@/memberup/store/features/uiSlice'
import { useAppDispatch } from '@/memberup/store/hooks'

export const GETTING_STARTED_TASKS = [
  {
    id: '1',
    title: 'Create Account',
    icon: '',
  },
  {
    id: '2',
    title: 'Help Us Help You',
    icon: <AppImg src="/assets/default/svgs/fi-rr-resources.svg" width={20} height={18} alt="help us help you" />,
    questions: [
      {
        id: '2-1',
        question: 'Where are you in your journey?',
        answers: [
          'I have a small audience or customer base I’d like to invite to a community.',
          'I have a large audience or customer base I’d like to invite to a community.',
          'I want to migrate my community from another platform.',
          'I don’t have an audience or community yet, I’m here to build one.',
        ],
      },
      {
        id: '2-2',
        question: 'What do you want to achieve with MemberUp?',
        answers: [
          'I want to learn how to generate recurring revenue.',
          'I want a platform to help me monetize my audience.',
          'I want a better platform than the one I’m currently using.',
        ],
      },
      {
        id: '2-3',
        question: 'How would you describe yourself?',
        answers: [
          'I/ my company offer(s) educational content/services/coaching to members.',
          'I/ my company offer(s) entertainment content to members.',
          `I’m building a social community for people to connect over shared interests.`,
          'I work at an established brand and want to build a community for our members.',
          'Other',
        ],
      },
      {
        id: '2-4',
        question: 'How much do you know about MemberUp?',
        answers: [
          'Not much. I’m just starting to look at platforms like MemberUp.',
          `A little bit. I’d like to see if this is the right fit for what I want to accomplish.`,
          `A lot. I’m ready to start building my community out right away.`,
        ],
      },
    ],
  },
  {
    id: '3',
    title: 'Basic Community Setup',
    icon: <AppImg src="/assets/default/svgs/combined-shape.svg" width={20} height={20} alt="help us help you" />,
    subtitle: 'Start strong by setting up the basics of your new MemberUp community.',
    accordions: [
      {
        id: '3-1',
        title: '1. Complete Your Profile',
        content: ({ handleOpenVideo }) => {
          // eslint-disable-next-line react-hooks/rules-of-hooks
          const dispatch = useAppDispatch()
          return (
            <Typography variant="body1">
              Tell your members about yourself by&nbsp;
              <a onClick={() => dispatch(openDialog({ dialog: 'UserProfile', open: true, props: {} }))}>
                setting your profile picture and bio.
              </a>
            </Typography>
          )
        },
      },
      {
        id: '3-2',
        title: '2. Set your community name and domain',
        content: ({ handleOpenVideo }) => {
          return (
            <Typography variant="body1">
              Let’s name your community!&nbsp;
              <a href="/settings/community-settings" target="_blank" rel="noopener noreferrer">
                Click here
              </a>
              <br />
              <br />
              Need help? Watch these: <br />
              🍿
              <a onClick={(e) => handleOpenVideo(e, '4')}>How to find and validate your community idea.</a>
              <br />
              🍿
              <a onClick={(e) => handleOpenVideo(e, '5')}>How to create a magnetic community name.</a>
            </Typography>
          )
        },
      },
      {
        id: '3-3',
        title: '3. Make your community unique',
        content: ({ handleOpenVideo }) => {
          return (
            <Typography variant="body1">
              It&rsquo;s time to make your community your own! Pick your brand color, upload your logo, and customize
              your sign-up and login pages.
              <br /> <br />
              Need help? Watch this: <br />
              🍿<a onClick={(e) => handleOpenVideo(e, '6')}>How to brand your community.</a>
            </Typography>
          )
        },
      },
      {
        id: '3-4',
        title: '4. Set Up Community Access',
        content: ({ handleOpenVideo }) => {
          return (
            <>
              <Typography variant="body1">Decide who can join your community and how.</Typography>
              <br />
              <Typography variant="subtitle1" gutterBottom>
                <b>1. Select your community privacy level</b>
              </Typography>
              <Typography variant="body1">
                <a href="/settings/community-settings" target="_blank" rel="noopener noreferrer">
                  Click here
                </a>{' '}
                to select open, private, or closed.
              </Typography>
              <br />
              <Typography variant="subtitle1" gutterBottom>
                <b>2. Connect your Stripe account</b>
              </Typography>
              <Typography variant="body1">
                Optional: charge for access to your community.
                <br />
                <a href="/settings/community-pricing" target="_blank" rel="noopener noreferrer">
                  Connect your Stripe account and set up a pricing plan.
                </a>
                <br />
                <br />
                Need help? Watch these:
                <br />
                🍿
                <a
                  href="https://university.memberup.com/library?id=521d58e8-9b9e-4dbd-a8e3-6ff3a1ed9635"
                  onClick={(e) =>
                    handleOpenVideo(
                      e,
                      null,
                      'https://university.memberup.com/library?id=521d58e8-9b9e-4dbd-a8e3-6ff3a1ed9635',
                    )
                  }
                >
                  How to setup your Stripe account
                </a>
                <br />
                🍿
                <a
                  href="https://university.memberup.com/library?id=777c2246-67ff-4b21-961c-d4aa4662ce76"
                  onClick={(e) =>
                    handleOpenVideo(
                      e,
                      null,
                      'https://university.memberup.com/library?id=777c2246-67ff-4b21-961c-d4aa4662ce76',
                    )
                  }
                >
                  How to decide a community access model
                </a>
                <br />
                🍿
                <a
                  href="https://university.memberup.com/library?id=30b19d3a-0509-4394-8367-2ccb8f5d1902"
                  onClick={(e) =>
                    handleOpenVideo(
                      e,
                      null,
                      'https://university.memberup.com/library?id=30b19d3a-0509-4394-8367-2ccb8f5d1902',
                    )
                  }
                >
                  How to price your community
                </a>
                <br />
                🍿
                <a
                  href="https://university.memberup.com/library?id=7a7a842a-f087-4696-ba66-fbf8b718d3e0"
                  onClick={(e) =>
                    handleOpenVideo(
                      e,
                      null,
                      'https://university.memberup.com/library?id=7a7a842a-f087-4696-ba66-fbf8b718d3e0',
                    )
                  }
                >
                  How to pick launch price and set revenue goals
                </a>
              </Typography>
            </>
          )
        },
      },
    ],
  },
  {
    id: '4',
    title: 'Design Your Community',
    icon: <AppImg src="/assets/default/svgs/object-subtract.svg" width={20} height={20} alt="help us help you" />,
    subtitle: 'It’s time to set up your community and content library!',
    content: ({ handleOpenVideo }) => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const dispatch = useAppDispatch()
      return (
        <>
          <Typography variant="subtitle1" gutterBottom>
            <b>1. Upload Content</b>
          </Typography>
          <Typography variant="body1">
            Visit the{' '}
            <a href="/[slug]/library" target="_blank" rel="noopener noreferrer">
              content library
            </a>{' '}
            to upload a course, content, or resources.
            <br />
            <br />
            Need help? Watch these:
            <br />
            🍿
            <a
              href="https://university.memberup.com/course/90598ac7-aa91-4eb9-b4f4-9742ee99af79?lesson_id=7f4dea3d-2c21-4ba1-a7e6-673071447278"
              onClick={(e) =>
                handleOpenVideo(
                  e,
                  null,
                  'https://university.memberup.com/course/90598ac7-aa91-4eb9-b4f4-9742ee99af79?lesson_id=7f4dea3d-2c21-4ba1-a7e6-673071447278',
                )
              }
            >
              How to make sales before you create any content
            </a>
            <br />
            🍿
            <a
              href="https://university.memberup.com/course/90598ac7-aa91-4eb9-b4f4-9742ee99af79?lesson_id=9acdf78b-c459-4014-95e7-48b0a4da3b95"
              onClick={(e) =>
                handleOpenVideo(
                  e,
                  null,
                  'https://university.memberup.com/course/90598ac7-aa91-4eb9-b4f4-9742ee99af79?lesson_id=9acdf78b-c459-4014-95e7-48b0a4da3b95',
                )
              }
            >
              How to decide what to offer inside your community
            </a>
            <br />
            🍿
            <a
              href="https://university.memberup.com/course/90598ac7-aa91-4eb9-b4f4-9742ee99af79?lesson_id=c4b119bc-68c8-4702-86ee-f53bee2b7200"
              onClick={(e) =>
                handleOpenVideo(
                  e,
                  null,
                  'https://university.memberup.com/course/90598ac7-aa91-4eb9-b4f4-9742ee99af79?lesson_id=c4b119bc-68c8-4702-86ee-f53bee2b7200',
                )
              }
            >
              How to quickly & easily create community content
            </a>
          </Typography>
          <br />
          <Typography variant="subtitle1" gutterBottom>
            <b>2. Set Up Spaces</b>
          </Typography>
          <Typography variant="body1">
            Create new feed spaces by clicking the button in the sidebar. Feed spaces engage members as you would in a
            Facebook Group, without the clutter, algorithms, or spam. You don’t need dozens of spaces to get started. In
            fact, starting your community with a few spaces will make it easier for your members to get a lay of the
            land. It’s easy to create new spaces and reorganize your community at any time.
          </Typography>
          <br />
          <Typography variant="subtitle1" gutterBottom>
            <b>3. Customize Your Sign Up & Login Pages</b>
          </Typography>
          <Typography variant="body1">
            With MemberUp you can customize the way your checkout, sign up and payment pages look too!{' '}
            <Link href="/settings/signup-pages">Click here</Link> to add your own text, background images, and member
            testimonials. Don’t have any yet? No worries, replace the default testimonials with an image that represents
            your community.
          </Typography>
          <br />
          <br />
          <Typography className="background-color06" variant="body2" sx={{ p: 2, borderRadius: 1 }}>
            💡Pro Tip: If your community is centered around members achieving a certain goal, set up a #MemberWins space
            and encourage your members to share and celebrate their progress with one another.
          </Typography>
        </>
      )
    },
  },
  {
    id: '5',
    title: 'Invite Your First Members',
    icon: <PeopleAltOutlinedIcon style={{ color: '#8D94A3' }} fontSize="small" />,
    content: ({ handleOpenVideo }) => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const dispatch = useAppDispatch()
      return (
        <>
          <Typography variant="subtitle1" gutterBottom>
            <b>1. Customize your onboarding experience</b>
          </Typography>
          <Typography variant="body1">
            To give your members a great first impression, you can turn on their welcome email&nbsp;
            <a href="/settings/emails" target="_blank" rel="noopener noreferrer">
              here.
            </a>
          </Typography>
          <br />
          <Typography variant="subtitle1" gutterBottom>
            <b>
              2. Inviting people to join your community is an exciting moment. Whether you decide to invite a few
              members or hundreds, here are some ways to do this.
            </b>
          </Typography>
          <Typography variant="body1">
            1) Manual invitations
            <br />
            Customize and send manual email invitations&nbsp;
            <Link href="/settings/invite-members">here.</Link>
            <br />
            <br />
            2) Import a CSV
            <br />
            You can bulk import a CSV file with emails of your members&nbsp;
            <a href="/settings/invite-members" target="_blank" rel="noopener noreferrer">
              here.
            </a>
            <br />
            <br />
            3) Invitation links
            <br />
            You can create a unique invitation link for your members to join&nbsp;
            <a href="/settings/invite-links" target="_blank" rel="noopener noreferrer">
              here.
            </a>
            <br />
            <br />
            4) Share your checkout link
            <br />
            You can link future members to your signup page directly, or from your marketing site.
            <br />
            <br />
            Need help launching your community and attracting members? Steal our ridiculously simple marketing strategy
            here 🚀.
          </Typography>
        </>
      )
    },
  },
  {
    id: '6',
    title: 'Spark Engagement',
    icon: <AppImg src="/assets/default/svgs/ic20-spark.svg" width={14} height={20} alt="help us help you" />,
    accordions: [
      {
        id: '6-1',
        title: '1. Turn on Spark and let us do the work for you',
        content: ({ handleOpenVideo }) => {
          // eslint-disable-next-line react-hooks/rules-of-hooks
          const dispatch = useAppDispatch()
          return (
            <Typography variant="body1">
              Bring your community together in a fun and meaningful way. Spark automatically posts questions within your
              community every 24 hours. Answers are only visible to members who respond to the question.
              <p style={{ display: 'inline' }}>
                {' '}
                <a href="/settings/spark" target="_blank" rel="noopener noreferrer">
                  Click here
                </a>{' '}
              </p>
              to select a category and let us take care of engagement for you! to select a category and let us take care
              of engagement for you!
            </Typography>
          )
        },
      },
      {
        id: '6-2',
        title: '2. Set Up Events',
        content: ({ handleOpenVideo }) => {
          return (
            <>
              <Typography variant="body1">
                It&rsquo;s easy to schedule an event within your community.&nbsp;
                <a href="/[slug]/events" target="_blank" rel="noopener noreferrer">
                  Head to the events page and add your first event
                </a>
              </Typography>
              <br />
              <Typography className="background-color06" variant="body2" sx={{ p: 2, borderRadius: 1 }}>
                💡Pro Tip: Schedule events into the future so members have something to look forward to. These don’t
                just have to be live calls or in person events, you can also use the events calendar to let members know
                when they can expect new content releases or announcements. Add all key dates you want your members to
                look forward to to your calendar.
              </Typography>
              <br />
              <Typography variant="body1">
                Need help? Watch this: <br />
                🍿
                <a onClick={(e) => handleOpenVideo(e, '2')}>How to decide what events to offer in your community.</a>
              </Typography>
            </>
          )
        },
      },
      {
        id: '6-3',
        title: '3. Start 1 on 1 conversations',
        content: ({ handleOpenVideo }) => {
          return (
            <>
              <Typography variant="body1">
                The more that you can get to know your members one-on-one, the stronger the foundation of your community
                will be. Don’t be shy- your members will love hearing from you personally!
              </Typography>
              <br />
              <Typography className="background-color06" variant="body2" sx={{ p: 2, borderRadius: 1 }}>
                💡Pro Tip: Go the extra mile and send your members a DM when they join to get to know them and what
                they’re hoping to get from joining your community.
              </Typography>
            </>
          )
        },
      },
      {
        id: '6-4',
        title: '4. Post on your Community Home Feed',
        content: ({ handleOpenVideo }) => {
          return (
            <>
              <Typography variant="body1">
                This is the first thing your members see when they log in to your community.
                <br />
                <br />
                <a href="/home" target="_blank" rel="noopener noreferrer">
                  Set up a welcome post
                </a>{' '}
                and guide your members through your community!
              </Typography>
              <br />
              <Typography className="background-color06" variant="body2" sx={{ p: 2, borderRadius: 1 }}>
                💡Pro Tip: Pin your welcome post to the top of your home feed.
              </Typography>
            </>
          )
        },
      },
    ],
  },
  {
    id: '7',
    title: 'Grow',
    icon: <AppImg src="/assets/default/svgs/chat-arrow-grow.svg" width={20} height={18} alt="help us help you" />,
    content: ({ handleOpenVideo }) => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const dispatch = useAppDispatch()
      return (
        <>
          <Typography variant="body1">
            Ready to take your community and business to the next level? Get the step by step strategy you need from
            industry leaders alongside creators just like you inside{' '}
            <a
              href="https://university.memberup.com/"
              onClick={(e) => handleOpenVideo(e, null, 'https://university.memberup.com/')}
            >
              MemberUp University
            </a>
            .
            <br />
            <br />
            Inside{' '}
            <a
              href="https://university.memberup.com/"
              onClick={(e) => handleOpenVideo(e, null, 'https://university.memberup.com/')}
            >
              MemberUp University
            </a>
            , you can network with and connect to other community leaders, ask questions, attend live events, have your
            community reviewed, see our product roadmap, request features and learn what’s working in other communities.
            Plus access to a full course on community building from industry leaders.
            <br />
            <br />
            You can access{' '}
            <a
              href="https://university.memberup.com/"
              onClick={(e) => handleOpenVideo(e, null, 'https://university.memberup.com/')}
            >
              MemberUp University
            </a>
            &nbsp;by clicking on the sidebar icon. Come say hi in the Introductions Space 👋
          </Typography>
        </>
      )
    },
  },
]

export const GETTING_STARTED_VIDEOS = [
  {
    id: '1',
    mux_asset: {
      id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_ID_01,
      playback_ids: [
        {
          id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_PLAYBACK_ID_01,
        },
      ],
    },
    title: 'Your Community Success Path - Step by Step',
    download_title: 'Level 1 Workbook',
    description:
      'Welcome! We’re so glad to be supporting you on this journey to becoming the leader of a thriving community and building a growing income every month. This video is a quick overview of exactly what that journey looks like, so you always know what step you’re on, what’s next, and what milestones (and pay days) you’ll hit along the way.',
    pdf: '/assets/default/MUWorkbook-Level-1.pdf',
    thumbnails: '/assets/default/images/getting-started-01.jpg',
  },
  {
    id: '2',
    mux_asset: {
      id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_ID_02,
      playback_ids: [
        {
          id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_PLAYBACK_ID_02,
        },
      ],
    },
    title: 'Is a community the right choice for you?',
    download_title: 'Level 1 Workbook',
    description:
      'What you’ll learn in this video will change how you thing about money and freedom forever. This very knowledge is what allowed Amy to make multiple 7 figures in her businesses, and reach 110 million people with her message. Jump in!',
    pdf: '/assets/default/MUWorkbook-Level-1.pdf',
    thumbnails: '/assets/default/images/getting-started-02.jpg',
  },
  {
    id: '3',
    mux_asset: {
      id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_ID_03,
      playback_ids: [
        {
          id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_PLAYBACK_ID_03,
        },
      ],
    },
    title: 'Uncover the 5 things you need to build a thriving community',
    download_title: 'Level 1 Workbook',
    description:
      'Want a community that beats all the industry averages, that sells effortlessly, that members fall in love with and tell all their friends about? Our 5C framework will show you how.',
    pdf: '/assets/default/MUWorkbook-Level-1.pdf',
    thumbnails: '/assets/default/images/getting-started-03.jpg',
  },
  {
    id: '4',
    mux_asset: {
      id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_ID_04,
      playback_ids: [
        {
          id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_PLAYBACK_ID_04,
        },
      ],
    },
    title: 'How to discover your infinite community idea (even if you have no idea what to offer)',
    download_title: 'Level 1 Workbook',
    description:
      'According to the recent Marketing Benchmark Report, over 60% of respondents who had not yet launched their subscription, 53% said that they are not clear on their idea, 36% did not know how to start, and 16% said they don’t know who to serve. In this video, we’re going to get you 100% clear on your idea, even if right now you have no idea what to offer, so you can be on the positive side of statistics. The side where every month you’re generating a growing predictable income just for bringing together a community around a topic you love.',
    pdf: '/assets/default/MUWorkbook-Level-1.pdf',
    thumbnails: '/assets/default/images/getting-started-04.jpg',
  },
  {
    id: '5',
    mux_asset: {
      id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_ID_05,
      playback_ids: [
        {
          id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_PLAYBACK_ID_05,
        },
      ],
    },
    title: 'How to quickly + easily create a magnetic community name',
    download_title: 'Level 1 Workbook',
    description:
      'Now that you have your infinite community idea- it’s time for the fun part. Let’s create a magnetic community name. In this video you’ll learn our quick and easy naming formula for doing just that!',
    pdf: '/assets/default/MUWorkbook-Level-1.pdf',
    thumbnails: '/assets/default/images/getting-started-05.jpg',
  },
  {
    id: '6',
    mux_asset: {
      id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_ID_06,
      playback_ids: [
        {
          id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_PLAYBACK_ID_06,
        },
      ],
    },
    title: 'How to build a professional-looking community step by step in 20 minutes or less',
    download_title: 'Level 1 Workbook',
    description:
      'In the next 20 minutes we’re going to customize your community together so it’s all set up and ready to start accepting members. No designers or developers needed. Let’s go!',
    pdf: '/assets/default/MUWorkbook-Level-1.pdf',
    thumbnails: '/assets/default/images/getting-started-06.jpg',
  },
  // {
  //   id: '7',
  //   mux_asset: {
  //     id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_ID_07,
  //     playback_ids: [
  //       {
  //         id: process.env.NEXT_PUBLIC_GETTING_STARTED_MUX_ASSET_PLAYBACK_ID_07,
  //       },
  //     ],
  //   },
  //   title: 'Free Gift',
  //   download_title: 'Level 1 Workbook',
  //   description: 'Free Gift!',
  //   pdf: '/assets/default/sample06.pdf',
  //   thumbnails: '/assets/default/images/getting-started-07.jpg',
  // },
]
