// TODO: We arent going to do this any more so you can remove this and things related to goals
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React from 'react'

import { IGoal } from '@memberup/shared/src/types/interfaces'
import PrettoSlider from '@/memberup/components/silders/pretto-silder'

const useStyles = makeStyles((theme) => ({
  root: {
    borderColor: theme.palette.divider,
    borderStyle: 'solid',
    borderWidth: 1,
    fontSize: 8,
    padding: 32,
    width: '100%',
  },
  numbers: {
    color: theme.palette.text.disabled,
    float: 'right',
  },
}))

const GoalCard: React.FC<{ goal: IGoal }> = ({ goal }) => {
  const classes = useStyles()

  return (
    <Card className={classes.root}>
      <CardContent>
        <Typography variant="h4">This goal is on track</Typography>
        <br />
        <br />
        <Typography variant="body1" gutterBottom>
          Complete 3 master classes
        </Typography>
        <Typography variant="body1">
          <span className="color03">Get to know members, share thoughts or discuss ideas.</span>
          <span className={classes.numbers}>2/5</span>
        </Typography>
        <br />
        <PrettoSlider value={2} total={5} />
      </CardContent>
    </Card>
  )
}

export default GoalCard
