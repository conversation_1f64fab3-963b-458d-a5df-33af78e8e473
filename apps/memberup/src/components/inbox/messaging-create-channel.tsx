import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew'
import Autocomplete from '@mui/material/Autocomplete'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Chip from '@mui/material/Chip'
import CircularProgress from '@mui/material/CircularProgress'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import Popper from '@mui/material/Popper'
import useTheme from '@mui/material/styles/useTheme'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import useMediaQuery from '@mui/material/useMediaQuery'
import debounce from 'lodash/debounce'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import type { ChannelData, UserResponse } from 'stream-chat'
import { useChatContext } from 'stream-chat-react'
import { v4 as uuidv4 } from 'uuid'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { CHANNEL_TYPE_ENUM, THEME_MODE_ENUM, USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { useAppMessagingContext } from '@/memberup/components/inbox/messaging-context'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { selectUser } from '@/src/store/features/userSlice'

const AppPopper = (props) => {
  return (
    <Popper
      {...props}
      sx={{
        width: 312,
        maxWidth: 312,
        '& .MuiAutocomplete-listbox': {
          maxHeight: '300px!important',
        },
      }}
      placement="bottom-start"
    />
  )
}

const AppMessagingCreateChannel: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const mountedRef = useMounted(true)
  const theme = useTheme()
  const isSmDown = useMediaQuery(theme.breakpoints.down('sm'))
  const inputRef = useRef<HTMLInputElement>(null)
  const { setOpenCreateMessagingChannel } = useAppMessagingContext()
  const { client: streamChatClient, setActiveChannel } = useChatContext()
  const [inputChannelName, setInputChannelName] = useState('')
  const [loading, setLoading] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<UserResponse[]>([])
  const [users, setUsers] = useState<UserResponse[]>([])
  const [defaultUsers, setDefaultUsers] = useState<UserResponse[]>([])
  const user = useAppSelector((state) => selectUser(state))

  const [requestCreateChannel, setRequestCreateChannel] = useState(false)
  const isLightTheme = theme.palette.mode === THEME_MODE_ENUM.light
  const [userQuery, setUserQuery] = useState('')

  const membershipIds = user?.user_memberships.map((um) => um.membership.id) || []

  useEffect(() => {
    if (!streamChatClient?.userID) return
    if (!mountedRef.current) return

    let allUsers = []
    const MAX_PAGES = 2
    let offset = 0
    console.log('mmmmm', membershipIds)
    const loadAllChatUsers = async () => {
      let res = null
      while (res === null || res?.users.length > 0) {
        if (offset >= MAX_PAGES * 100) {
          break
        }
        res = await streamChatClient.queryUsers(
          {
            $and: [
              { id: { $ne: streamChatClient.userID as string } },
              {
                status: {
                  $nin: [
                    USER_STATUS_ENUM.banned,
                    USER_STATUS_ENUM.deleted,
                    USER_STATUS_ENUM.inactive,
                    USER_STATUS_ENUM.invited,
                  ],
                },
              },
              { teams: { $in: membershipIds } as any },
            ],
          },
          [{ name: 1 }],
          { limit: 100, offset },
        )
        if (res.users.length === 0) {
          break
        }
        allUsers = allUsers.concat(res.users)
        offset += 100
      }
      setUsers(allUsers as UserResponse[])
      setDefaultUsers(allUsers)
      setLoading(false)
    }

    loadAllChatUsers()

    return () => {}
  }, [streamChatClient?.userID])

  const handleCreateChannel = async () => {
    try {
      // TODO UA: Find a way to make a conversation between users on different teams?

      if (!selectedUsers.length || !streamChatClient.userID) return

      // NOTE: For now we try to get a team that is common between the users
      const selectedUser = selectedUsers[0]
      const selectedMembershipId = selectedUser.teams.find((t) => membershipIds.includes(t))
      console.log('selectedMembershipId', selectedMembershipId)

      const existingChannel = Object.values(streamChatClient.activeChannels || {})
        .filter((item) => item.type === 'messaging')
        .find((item) => {
          if (item.data.member_count !== selectedUsers.length + 1) return false
          for (const selectedUser of selectedUsers) {
            if (!item.state.members[selectedUser.id]) return false
          }
          // Check if the current user is part of the existing channel
          if (!item.state.members[streamChatClient.userID]) return false
          return true
        })
      if (existingChannel) {
        setActiveChannel?.(existingChannel)
        setOpenCreateMessagingChannel(false)
        return
      }

      setRequestCreateChannel(true)
      const selectedUsersIds = selectedUsers.map((u) => u.id)
      const payload: ChannelData = {
        // members: [...selectedUsersIds, streamChatClient.userID],
        team: selectedMembershipId,
      }

      if (inputChannelName) {
        payload.name = inputChannelName
      }

      const newChannel = streamChatClient.channel(CHANNEL_TYPE_ENUM.messaging, uuidv4(), payload)
      await newChannel.create()
      await newChannel.addMembers([...selectedUsersIds, streamChatClient.userID])
      await newChannel.watch()

      onClose()
      if (mountedRef.current) {
        setActiveChannel?.(newChannel)
        setRequestCreateChannel(false)
        setInputChannelName('')
        setSelectedUsers([])
      }
    } catch (err: any) {
      setRequestCreateChannel(false)
    }
  }

  const addUser = (addedUser: UserResponse) => {
    const isAlreadyAdded = selectedUsers.find((user) => user.id === addedUser.id)
    if (isAlreadyAdded) return
    setSelectedUsers([...selectedUsers, addedUser])
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }

  const fetchUsers = async (value: string) => {
    if (!value || value.length < 3) {
      setUsers(defaultUsers)
      return
    }
    const res = await streamChatClient.queryUsers(
      {
        $and: [
          { name: { $autocomplete: value } },
          { id: { $ne: streamChatClient.userID as string } },
          {
            status: {
              $nin: [
                USER_STATUS_ENUM.banned,
                USER_STATUS_ENUM.deleted,
                USER_STATUS_ENUM.inactive,
                USER_STATUS_ENUM.invited,
              ],
            },
          },
          { teams: { $in: membershipIds } as any },
        ],
      },
      [{ name: 1 }],
      { limit: 100 },
    )
    return setUsers(res.users)
  }

  const debouncedFetchOptions = useCallback(debounce(fetchUsers, 300), [defaultUsers])
  const handleMemberAutocomplete = async (event: any, value: string) => {
    value = value.trim()
    await debouncedFetchOptions(value)
  }

  useEffect(() => {
    return () => {
      debouncedFetchOptions.cancel()
    }
  }, [debouncedFetchOptions])

  return (
    <Box
      data-cy="app-messaging-create-channel"
      sx={{
        backgroundColor: isLightTheme ? 'white' : 'rgba(255, 255, 255, 0.09)',
        borderBottom: isLightTheme && '1px solid rgba(111, 118, 133, 0.12)',
        py: '12px',
        pl: {
          xs: '12px',
          sm: '32px',
        },
        pr: {
          xs: '12px',
          sm: '16px',
        },
        '& .MuiOutlinedInput-notchedOutline': {
          border: 'none',
        },
        '& .MuiAutocomplete-tag': {
          backgroundColor: 'rgba(141,148,163,0.4)',
          height: 24,
        },
        '& .MuiAutocomplete-popper': {
          ...theme.components.MuiCssBaseline.styleOverrides['body']['& .border01'],
          borderRadius: '12px',
          overflow: 'hidden',
          mt: '30px!important',
          '& ul': {
            backgroundColor: !isLightTheme && 'rgb(23, 23, 26)!important',
            p: '4px',
            m: 0,
          },
        },
        '& .MuiInputBase-root': {
          pl: '0!important',
          py: '0!important',
        },
        '& .MuiChip-root': {
          p: '4px',
        },
        '& .MuiChip-label': {
          color: isLightTheme ? 'rgba(0, 0, 0, 0.87)' : 'rgba(255, 255, 255, 0.87)',
          fontFamily: 'Graphik Medium',
          fontSize: 13,
        },
        '& .MuiChip-deleteIcon': {
          color: isLightTheme ? 'rgb(111, 118, 133)' : 'rgb(141, 148, 163)',
          mr: 0,
        },
      }}
    >
      <Grid container spacing={2} alignItems="center">
        <Grid item sx={{ display: { xs: 'block', sm: 'none' }, pt: '6px' }}>
          <IconButton size="small" onClick={onClose}>
            <ArrowBackIosNewIcon />
          </IconButton>
          &nbsp;
        </Grid>
        <Grid item>
          <Typography
            className="font-family-graphik-bold"
            variant="body1"
            sx={{ color: !isLightTheme && 'rgb(141, 148, 163)', fontSize: 14 }}
          >
            To:{' '}
          </Typography>
        </Grid>
        <Grid item xs sx={{ position: 'relative' }}>
          <Autocomplete
            id="chat-members"
            disableClearable
            disablePortal
            fullWidth
            multiple
            popupIcon={null}
            size="small"
            options={users}
            PopperComponent={AppPopper}
            value={selectedUsers || []}
            getOptionLabel={(option: UserResponse) => option.name as string}
            onInputChange={handleMemberAutocomplete}
            onChange={(event, newValue) => {
              setSelectedUsers(newValue as any)
            }}
            renderInput={(params) => (
              <TextField {...params} placeholder={selectedUsers.length ? '' : 'Start typing for suggestions'} />
            )}
            renderTags={(tagValue, getTagProps) =>
              tagValue.map((option, index) => {
                const tagProps = getTagProps({ index })

                const { key, ...otherTagProps } = tagProps

                return <Chip key={`${index}`} label={option.name} size="small" {...otherTagProps} />
              })
            }
            renderOption={(props, option: any, state) => (
              <ListItemButton
                key={option.id}
                disabled={selectedUsers.indexOf(option) !== -1}
                sx={{
                  borderRadius: '12px',
                  p: '8px',
                  '&:hover': {
                    backgroundColor: !isLightTheme && 'rgba(141, 148, 163, 0.04)',
                  },
                }}
                onClick={() => addUser(option)}
                data-cy="app-messaging-user"
              >
                <ListItemIcon>
                  <AppProfileImage
                    imageUrl={option.profile?.image || option?.image}
                    cropArea={option.profile?.image_crop_area || option?.image_crop_area}
                    name={option.name}
                    size={40}
                    fontSize={18}
                  />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography
                      className="font-family-graphik-medium text-ellipsis"
                      variant="body1"
                      sx={{ fontWeight: '500' }}
                    >
                      {option.name}
                    </Typography>
                  }
                  sx={{ pl: '12px' }}
                />
                {/* {user.online && <div className="messaging-create-channel__user-result-online" />} */}
              </ListItemButton>
            )}
            data-cy="app-messaging-find-users"
          />
        </Grid>
        <Grid item className="text-right" xs={12} sm="auto">
          <Button
            className="app-button round-small"
            variant="contained"
            disabled={!selectedUsers.length || requestCreateChannel}
            onClick={() => handleCreateChannel()}
            sx={{ color: 'white', backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
          >
            {requestCreateChannel ? <CircularProgress size={16} /> : 'Start Chat'}
          </Button>
        </Grid>
      </Grid>
    </Box>
  )
}

export default AppMessagingCreateChannel
