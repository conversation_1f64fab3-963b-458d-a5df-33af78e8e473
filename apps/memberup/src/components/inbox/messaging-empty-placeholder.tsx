import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import React from 'react'

import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { useAppMessagingContext } from '@/memberup/components/inbox/messaging-context'

const AppMessagingEmptyPlaceholder = (props: { isMessageEmpty?: boolean }) => {
  const { isMessageEmpty } = props
  const theme = useTheme()
  const isLightTheme = theme.palette.mode === THEME_MODE_ENUM.light
  const { setOpenCreateMessagingChannel } = useAppMessagingContext()

  return (
    <Box className="d-flex direction-column align-center w-100 h-100 background-color19 justify-center text-center">
      <Box>
        <Box
          sx={{
            width: 72,
            height: 72,
            display: 'flex',
            alignItems: 'center',
            color: 'white',
            justifyContent: 'center',
            m: 'auto',
            fontSize: 40,
            backgroundColor: isLightTheme ? 'rgba(111, 118, 133, 0.12)' : 'rgba(141, 148, 163, 0.12)',
            borderRadius: '36px',
          }}
        >
          💬
        </Box>
        {!isMessageEmpty && (
          <>
            <Typography variant="h6" sx={{ fontFamily: 'Graphik SemiBold!important', mt: '24px' }}>
              No Chat Selected
            </Typography>
            <Button
              sx={{
                boxShadow: 'none',
                color: '#FFFFFF',
                fontFamily: 'Graphik SemiBold!important',
                mt: '24px',
                backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
              }}
              className="round-small"
              variant="contained"
              onClick={() => setOpenCreateMessagingChannel(true)}
              aria-label="Start a Conversation"
            >
              Start a Conversation
            </Button>
          </>
        )}
      </Box>
    </Box>
  )
}

export default AppMessagingEmptyPlaceholder
