import List from '@mui/material/List'

import SearchEmptyState from './search-empty-state'
import PostSearchResultItem from '@/memberup/components/algolia/post-search-result-item'
import { IUser } from '@/shared-types/interfaces'

interface PostSearchResultListProps {
  searchResults: any
  onClickSearchResultHandler: (feedID: string) => void
  members: IUser[]
}

const postSearchResultList = ({ searchResults, members, onClickSearchResultHandler }: PostSearchResultListProps) => {
  return (
    <List
      disablePadding
      sx={{
        '& .MuiListItemButton-root:hover': {
          backgroundColor: 'transparent',
          '& .MuiTypography-root': {
            fontWeight: 'bold',
          },
        },
      }}
    >
      {(searchResults?.hits || []).map((item: any) => {
        let feedID = item.objectID
        if (members?.[item.author_id]?.name) {
          item.author_full_name = members[item.author_id].name
        }
        return (
          <div key={feedID} style={{ marginBottom: '20px' }}>
            {' '}
            {/* Modify this line to adjust the space */}
            <PostSearchResultItem key={feedID} onClickSearchResultHandler={onClickSearchResultHandler} item={item} />
          </div>
        )
      })}
      {!searchResults?.hits?.length && (
        <div style={{ marginTop: '20px' }}>
          <SearchEmptyState />
        </div>
      )}
    </List>
  )
}

export default postSearchResultList
