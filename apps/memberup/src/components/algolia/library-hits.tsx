import { truncate } from 'lodash'
import Image from 'next/image'
import Link from 'next/link'
import { useEffect } from 'react'
import { useInstantSearch } from 'react-instantsearch'

import SearchEmptyState from '@/memberup/components/algolia/search-empty-state'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { setSearchStatus } from '@/memberup/store/features/uiSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const LibraryHits = ({ searchResults, displayEmptyResult, onClickSearchResultHandler }) => {
  const dispatch = useAppDispatch()

  const membership = useAppSelector((state) => selectMembership(state))

  useEffect(() => {
    if (searchResults?.hits?.length) {
      dispatch(setSearchStatus({ searchIndex: 'content-library', hasResults: true }))
    } else {
      dispatch(setSearchStatus({ searchIndex: 'content-library', hasResults: false }))
    }
  }, [searchResults])

  return (
    <>
      {searchResults?.hits?.length ? (
        <div className="flex flex-col space-y-4">
          <div>
            <div className="flex items-center">
              <h6 className="text-black text-sm font-semibold">Content</h6>
            </div>
          </div>
          <div>
            <ul className="space-y-4">
              {searchResults.hits.map((item: any) => (
                <li key={item.objectID} className="hover:bg-transparent">
                  <Link
                    href={`/${membership.slug}/content/course/${item.course_id}?lesson_id=${item.objectID}`}
                    className="block cursor-pointer"
                    onClick={onClickSearchResultHandler}
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex-1">
                        <h6 className="text-black mb-1 text-sm font-semibold">{item.title}</h6>
                        <p className="break-all text-xs text-[#2d2d2d]">{truncate(item.text, { length: 130 })}</p>
                      </div>
                      {item.thumbnail_url ? (
                        <div className="flex h-[72px] w-[72px] items-center justify-center overflow-hidden rounded-xl">
                          <Image
                            src={item.thumbnail_url}
                            alt={item.title}
                            width={250}
                            height={250}
                            className="h-full w-full object-cover"
                          />
                        </div>
                      ) : null}
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      ) : (
        displayEmptyResult && (
          <div className="mt-5">
            <SearchEmptyState />
          </div>
        )
      )}
    </>
  )
}

function CustomSearchBox(props: any) {
  const { results } = useInstantSearch({
    catchError: true,
  })

  return (
    <LibraryHits
      displayEmptyResult={props.displayEmptyResult}
      onClickSearchResultHandler={props.onClickSearchResultHandler}
      searchResults={results}
    />
  )
}

export default CustomSearchBox
