import React, { useState } from 'react'
import { SearchBox, useSearchBox } from 'react-instantsearch'

/* const SearchBox: React.FC<{ handleChange: (e: string) => void; refine: (e: string) => void }> = ({
 */
const SearchBoxWrapper = (props) => {
  const { setIsSearchBoxEmpty, refine, query } = props

  /* useEffect(() => {
    console.log('querying', query)
    refine(query)
  }, [refine, query]) */
  /*   const [query, setSearchQuery] = useState('')
   */
  /* useEffect(() => {
    // Cleanup function to clear the timer when the component unmounts or searchQuery changes
    return () => {
      if (timerId !== null) {
        clearTimeout(timerId)
      }
    }
  }, [timerId]) */

  /*  useEffect(() => {
     // Trigger the refine function after the debounce time (500ms)
     if (query) {
       const newTimerId = setTimeout(() => {
         console.log('triggering refine')
         refine(query)
       }, 300)
       setTimerId(newTimerId)
     } else {
       // If the searchQuery is empty, clear the timer
       setTimerId(null)
       // You can also decide whether to clear the search results when the input is empty.
       // For now, I'm not calling refine when the input is empty.
     }
   }, [query, refine]) */

  /*  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
     const value = e.currentTarget.value
     setSearchQuery(value)
     handleChange(value)
   } */

  return (
    <SearchBox
      className="text-black text-xs"
      autoFocus
      placeholder="Search content or members..."
      onInput={() => {
        setIsSearchBoxEmpty((prevVal) => {
          if (query.length === 0) {
            return false
          }

          if (prevVal) {
            return true
          }
        })
        refine(query)
      }}
    />
  )
}

function CustomSearchBox(props) {
  const searchBoxApi = useSearchBox(props)

  return <SearchBoxWrapper {...searchBoxApi} setIsSearchBoxEmpty={props.setIsSearchBoxEmpty} />
}

export default CustomSearchBox
