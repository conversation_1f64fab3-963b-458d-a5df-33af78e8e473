import React from 'react'
import { useFormContext } from 'react-hook-form'

import { Input } from '@/components/ui'

export const DynamicControl = ({
  inputType,
  name,
  label,
  defaultValue,
  value,
  options = [],
  config = {},
  readOnly,
}) => {
  const { register } = useFormContext()

  switch (inputType) {
    case 'short_text':
      return (
        <Input
          className="w-full"
          placeholder={label}
          type="text"
          readOnly={readOnly}
          {...register(name, config)}
          defaultValue={defaultValue}
          value={value}
        />
      )
    case 'textarea':
      return (
        <textarea
          className="text-white w-full rounded-xl bg-gray-900 p-4 font-['Graphik'] text-gray-400"
          {...register(name, config)}
          defaultValue={defaultValue}
          value={value}
        />
      )
    case 'select': {
      return (
        <select {...register(name, config)} defaultValue={defaultValue} name={name} id={name} value={value}>
          {options.map((o, index) => (
            <option key={index} value={o.value}>
              {o.label}
            </option>
          ))}
        </select>
      )
    }
    case 'number':
      return (
        <input
          className="text-white w-full rounded-xl bg-gray-900 p-4 font-['Graphik'] text-gray-400"
          type="number"
          {...register(name, config)}
          defaultValue={defaultValue}
          value={value}
        />
      )
    default:
      return <input type="text" />
  }
}
