import { ErrorMessage } from '@hookform/error-message'
import React from 'react'
import { FormProvider, useForm } from 'react-hook-form'

import { Button, Input } from '@/components/ui'
import { Form, FormControl, FormCounter, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { DynamicControl } from '@/src/components/community/dynamic-control'

function createFormSchema(fields) {
  const schema = {
    fields: [],
  }

  fields.forEach((field) => {
    const { name, label, config, inputType, defaultValue } = field

    const fieldSchema = {
      name,
      label,
      required: config?.required || false,
      inputType,
      defaultValue,
    }

    schema.fields.push(fieldSchema)
  })

  return schema
}

export default function DynamicForm({ fields, onSubmit, isSubmitting }) {
  const formSchema = createFormSchema(fields)
  const form = useForm({ mode: 'onChange', schema: formSchema })

  const {
    handleSubmit,
    control,
    register,
    formState: { errors, isValid },
  } = form

  return (
    <div className={'mt-8'}>
      <Form {...form}>
        {fields.map((f) => (
          <FormField
            control={form.control}
            name={f.name}
            rules={{ required: f.required ? f.required : false }}
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <Input
                    className="w-full"
                    placeholder={f.label}
                    type="text"
                    name={'label'}
                    disabled={isSubmitting}
                    error={Boolean(error)}
                    {...field}
                  />
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
              </FormItem>
            )}
          />
        ))}
        <Button
          className={'mt-4 w-full'}
          type="submit"
          variant="default"
          loading={isSubmitting}
          disabled={!isValid || isSubmitting}
          data-cy="signin-button"
          onClick={handleSubmit(onSubmit)}
        >
          {isSubmitting ? 'Submitting' : 'Submit'}
        </Button>
      </Form>
    </div>
  )
}
