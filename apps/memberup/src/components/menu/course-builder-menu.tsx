'use client'

import { Drag<PERSON><PERSON><PERSON>ontext, Draggable, Droppable } from '@hello-pangea/dnd'
import { Add, Circle, Error, MoreHoriz } from '@mui/icons-material'
import {
  Box,
  Button,
  Chip,
  Divider,
  FormControl,
  Grid,
  IconButton,
  ListItem,
  Menu,
  MenuItem,
  Select,
  Tab,
  Tabs,
  TextField,
  Typography,
} from '@mui/material'
import Skeleton from '@mui/material/Skeleton'
import { makeStyles } from '@mui/styles'
import { rgbToHex } from '@mui/system'
import clsx from 'clsx'
import Image from 'next/image'
import { useRouter, useSearchParams } from 'next/navigation'
import { tint } from 'polished'
import React, { useEffect, useRef, useState } from 'react'
import { Controller } from 'react-hook-form'

import AppTooltip from '../common/app-tooltip'
import VisuallyHiddenInput from '../common/hidden-input'
import ImageCropDialog from '../dialogs/image-crop-dialog'
import EditSectionModal from '../dialogs/library/edit-section'
import useAppTheme from '../hooks/use-app-theme'
import useUploadFiles from '../hooks/use-upload-files'
import SVGDown from '../svgs/down'
import SVGDragIcon from '../svgs/drag-icon'
import SVGGear from '../svgs/gear'
import SVGPhoto from '../svgs/photo'
import SVGPhotoAdd from '../svgs/photo-add'
import SVGTrash from '../svgs/trash'
import { AppDropzone } from '@memberup/shared/src/components/common/app-dropzone'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { stripHtml } from '@memberup/shared/src/libs/string-utils'
import { ChevronLeft24Icon } from '@/components/icons'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import AppWarning from '@/memberup/components/dialogs/warning'
import StatusDot from '@/memberup/components/ui/status-dot'
import { getCommunityBaseURL } from '@/memberup/libs/utils'
import { cropString } from '@/shared-libs/string-utils'
import { updateLessonsOrderApi, updateSectionsOrderApi } from '@/shared-services/apis/content-library.api'

const useStyles = makeStyles((theme) => ({
  tabContainer: {
    padding: '0px 16px 0px 0px',
    height: '100%',
    overflowY: 'scroll',
    scrollbarWidth: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    '-ms-overflow-style': 'none',
    '& .draggable-item:hover .drag-handle': {
      visibility: 'visible',
    },
    '& .drag-handle': {
      /* Estilos para el ícono, inicialmente oculto */
      visibility: 'hidden',
      /* ...otros estilos... */
    },
  },
  settingsContainer: {
    '& .MuiDialog-container': {
      alignItems: 'unset',
    },
    '& .MuiDialog-paper': {
      backgroundColor: theme.palette.background.paper,
    },
    '& .MuiDialog-paperFullWidth': {
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      maxWidth: '100%',
      margin: 0,
    },
    '& .MuiDivider-root': {
      borderColor: theme.palette.action.disabledBackground,
    },
    '& .MuiOutlinedInput-root': {
      backgroundColor: theme.palette.action.disabledBackground,
      borderRadius: 12,
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
      },
    },
  },
  inputFields: {
    height: 48,
    '& .MuiInputBase-input': {
      boxSizing: 'border-box',
      height: '100%',
      fontFamily: 'Graphik Regular',
      fontSize: '14px',
    },
  },
  sectionSidebarText: {
    fontSize: '14px',
    fontFamily: 'Graphik Semibold',
    color: theme.palette.mode == 'dark' ? 'rgb(141, 148, 163)' : '#585D66',
  },
  inputLabel: {
    fontSize: '14px',
    fontFamily: 'Graphik Medium',
    lineHeight: '16px',
    color: theme.palette.mode == 'dark' ? 'rgba(255, 255, 255, 0.87)' : 'rgba(0, 0, 0, 0.87)',
  },
  subLabel: {
    color: '#8D94A3',
    fontSize: '12px',
    fontFamily: 'Graphik Regular',
    fontWeight: '500',
  },
  inputSection: {
    height: 48,
    border: 'none',
    '& .MuiInputBase-input': {
      height: '100%',
      fontSize: '14px',
      fontFamily: 'Graphik Semibold',
    },
  },
}))

const ContentStatusOptions = (theme) => [
  {
    value: 'draft',
    label: (
      <Box sx={{ fontFamily: 'Graphik Medium', fontWeight: 500 }}>
        <Circle sx={{ fontSize: '10px', color: '#e7ad8b', marginRight: '8px' }} />
        Draft
      </Box>
    ),
  },
  {
    value: 'published',
    label: (
      <Box
        sx={{
          fontFamily: 'Graphik Medium',
          fontWeight: 500,
          color: theme.palette.mode === 'dark' ? '#AEE78B' : '#2E3D25',
        }}
      >
        <Circle
          sx={{
            fontSize: '10px',
            color: theme.palette.mode === 'dark' ? '#AEE78B' : '#48b705',
            marginRight: '8px',
          }}
        />
        Published
      </Box>
    ),
  },
]

const CourseBuilderSection = ({
  provided,
  section,
  classes,
  sections,
  anchorEl,
  setAnchorEl,
  setOpenEditSectionModal,
  isCreatingLesson,
  addLesson,
  disableAction,
  collapsedSections,
  setCollapsedSections,
  isCollapsed,
  handleClick,
  setOpenWarningSectionDelete,
  onLessonClick,
  selectedLessonId,
  setOpenWarningLessonDelete,
  setLessonIdToDelete,
  setEditedSection,
  setAnchorEls,
  setSectionIdToDelete,
  hasLessonBeenClicked,
  isDragging,
}) => {
  const { isDarkTheme, theme } = useAppTheme()
  const hexPrimary = rgbToHex(theme.palette.primary.main).slice(0, -2)

  const [menuAnchorEl, setMenuAnchorEl] = useState({})
  const lessonsContainerRef = useRef(null)
  const height = isCollapsed ? '0px' : `${lessonsContainerRef?.current?.scrollHeight}px`
  const lessonRefs = useRef({})
  const searchParams = useSearchParams()
  const lessonIdFromUrl = searchParams.get('lesson_id')
  // Ensure a ref exists for each item
  section?.ContentLibraryCourseLesson?.forEach((lesson) => {
    if (!lessonRefs.current[lesson.id]) {
      lessonRefs.current[lesson.id] = React.createRef()
    }
  })

  useEffect(() => {
    /* this effect is for when the user adds a new section/lesson or when he loads a url with the query param lesson_id so it can scroll to that lesson id item on the side menu */
    const targetLessonId = lessonIdFromUrl || selectedLessonId
    if (targetLessonId && !hasLessonBeenClicked) {
      const doScroll = () => {
        if (lessonRefs.current[targetLessonId]) {
          if (lessonRefs?.current?.[targetLessonId]?.current) {
            lessonRefs.current[targetLessonId].current.scrollIntoView({ behavior: 'smooth' })
          }
        }
      }

      const timeoutId = setTimeout(doScroll, 1300)

      // Clear the timeout when the effect cleans up
      return () => clearTimeout(timeoutId)
    }
  }, [selectedLessonId, sections, lessonIdFromUrl])

  return (
    <Box
      ref={provided.innerRef}
      className="draggable-item"
      sx={{ margin: '16px 4px 16px 16px' }}
      {...provided.draggableProps}
    >
      <Box key={`${section.id}`}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            backgroundColor: isDarkTheme ? '#1c1c1f' : '#ecefef',
            height: '48px',
            borderRadius: '12px',
          }}
        >
          <Box
            {...provided.dragHandleProps}
            className="drag-handle"
            sx={{
              color: theme.palette.text.disabled,
              maxWidth: '20px',
              width: '20px',
              paddingLeft: '8px',
              marginRight: '8px',
              height: '90%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <SVGDragIcon width={6} height={12} />
          </Box>
          <Box
            sx={{
              width: '100%',
              marginLeft: '4px',
            }}
          >
            <Typography
              sx={{
                lineHeight: '24px',
                display: 'flex',
                alignItems: 'center',
                fontSize: '14px',
              }}
              className={classes.sectionSidebarText}
            >
              <StatusDot
                styles={{
                  backgroundColor: section.visibility === 'published' ? '#50C878' : '#e7ad8b',
                  marginRight: '10px',
                  width: '10px',
                  height: '10px',
                }}
              />
              {cropString(section.name, 22)}
            </Typography>
          </Box>

          <IconButton
            sx={{
              color: theme.palette.text.disabled,
              backgroundColor: isDarkTheme ? '#17171a' : '#ecefef',
              height: '32px',
              width: '32px',
            }}
            onClick={async () => {
              if (collapsedSections.includes(section.id)) {
                setCollapsedSections(collapsedSections.filter((id) => id !== section.id))
              } else {
                setCollapsedSections([...collapsedSections, section.id])
              }
            }}
            disabled={isCreatingLesson || disableAction}
          >
            <SVGDown
              width={20}
              height={20}
              style={{
                transform: !isCollapsed && 'rotate(180deg)',
                transition: 'transform 0.5s ease-in-out',
              }}
            />
          </IconButton>
          <IconButton
            sx={{
              color: theme.palette.text.disabled,
              backgroundColor: isDarkTheme ? '#17171a' : '#ecefef',
              height: '32px',
              width: '32px',
              marginRight: '8px',
              '& svg': {
                maxWidth: '21px',
                position: 'relative',
                top: '1px',
              },
            }}
            onClick={(e) => handleClick(e, section.id)}
            disabled={isCreatingLesson || disableAction}
          >
            <MoreHoriz />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            keepMounted
            id={`menu-${section.id}`}
            open={Boolean(anchorEl)}
            onClose={() =>
              setAnchorEls((prevState) => ({
                ...prevState,
                [section.id]: null,
              }))
            }
            sx={{
              '& .MuiPaper-root': {
                borderRadius: '12px',
                boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                backgroundImage: 'unset',
                padding: '6px',
              },
              '& .MuiList-root': {
                padding: '0px',
              },
            }}
          >
            <ListItem
              sx={{
                justifyContent: 'left',
                fontFamily: 'unset',
                height: '40px',
                margin: '0px',
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                  borderRadius: '12px',
                },
              }}
              onClick={(e) => {
                /* set open edit section modal */
                setOpenEditSectionModal(true)
                setAnchorEl(null)

                const sectionId = e.currentTarget.dataset.section

                // Find the clicked section from the sections
                const clickedSection = sections.filter((section) => section.id === sectionId)[0]
                // Set the edited section
                setEditedSection(clickedSection, sectionId)
              }}
              disabled={isCreatingLesson || disableAction}
              data-section={`${section.id}`}
            >
              Edit Section
            </ListItem>
            {((sections && Object.keys(sections).length >= 2) || isCreatingLesson || disableAction) && (
              <ListItem
                sx={{
                  justifyContent: 'left',
                  fontFamily: 'unset',
                  height: '40px',
                  margin: '0px',
                  cursor: 'pointer',
                  color: '#F34646',
                  '&:hover': {
                    backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                    borderRadius: '12px',
                  },
                }}
                onClick={async () => {
                  if (sections && Object.keys(sections).length >= 2) {
                    setOpenWarningSectionDelete(true)
                    setSectionIdToDelete(section.id)
                    setAnchorEl(null)
                  }
                }}
                disabled={(sections && Object.keys(sections).length < 2) || isCreatingLesson || disableAction}
              >
                Delete Section
              </ListItem>
            )}
          </Menu>
        </Box>
        <Droppable
          droppableId={`section_lessons_${section.id}`}
          type="lesson"
          isCombineEnabled={false}
          isDropDisabled={false}
          ignoreContainerClipping={false}
        >
          {(providedLesson) => {
            return (
              <Box
                ref={lessonsContainerRef}
                sx={{
                  overflow: 'hidden',
                  height: height,
                  transition: 'height 0.5s ease-in-out',
                  marginLeft: '-4px',
                }}
              >
                <Box
                  {...providedLesson.droppableProps}
                  ref={providedLesson.innerRef}
                  sx={{
                    '& .draggable-item-lesson:hover .drag-handle-lesson': {
                      visibility: 'visible',
                    },
                    '& .drag-handle-lesson': {
                      visibility: 'hidden',
                    },
                  }}
                >
                  {section?.ContentLibraryCourseLesson?.map((lesson: any, lessonIndex: number) => {
                    const lessonQty = section?.ContentLibraryCourseLesson?.length
                    const hasOnlyOneLesson = lessonQty === 1
                    let isDragDisabled = disableAction || isDragging || hasOnlyOneLesson || isCreatingLesson

                    return (
                      <Draggable
                        key={lesson.id}
                        draggableId={`lesson_${lesson.id}`}
                        index={lessonIndex}
                        isDragDisabled={isDragDisabled}
                      >
                        {(providedLesson) => (
                          <div
                            /* ref={providedLesson.innerRef} */
                            ref={(el) => {
                              providedLesson.innerRef(el)
                              lessonRefs.current[lesson.id].current = el
                            }}
                            {...providedLesson.draggableProps}
                            className="draggable-item-lesson"
                          >
                            <Box
                              key={lesson?.id}
                              className="course-builder-lesson-item"
                              onClick={() => {
                                if (disableAction) return
                                onLessonClick(section.id, lesson?.id)
                              }}
                              sx={{
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                position: 'relative',
                                height: '48px',
                                borderRadius: '12px',
                                backgroundColor:
                                  lesson?.id === selectedLessonId
                                    ? isDarkTheme
                                      ? '#29292c'
                                      : `${hexPrimary}10`
                                    : 'transparent',
                                marginLeft: '8px',
                                '&:hover': {
                                  backgroundColor: isDarkTheme ? '#29292c50' : `${hexPrimary}05`,
                                },
                              }}
                            >
                              <Box
                                className="drag-handle-lesson"
                                {...providedLesson.dragHandleProps}
                                sx={{
                                  color: theme.palette.text.disabled,
                                  maxWidth: '20px',
                                  width: '20px',
                                  paddingLeft: '8px',
                                  marginRight: '8px',
                                  height: '90%',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}
                              >
                                <SVGDragIcon
                                  width={6}
                                  height={12}
                                  styles={{ display: isDragDisabled ? 'none' : 'flex' }}
                                />
                              </Box>

                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'space-between',
                                  '&:hover button': {
                                    visibility: 'visible',
                                  },
                                  cursor: 'pointer',
                                  color:
                                    lesson?.id === selectedLessonId
                                      ? theme.palette.text.primary
                                      : theme.palette.text.disabled,
                                }}
                              >
                                <StatusDot
                                  styles={{
                                    backgroundColor: lesson?.visibility == 'draft' ? '#e7ad8b' : '#50C878',
                                    width: '10px',
                                    height: '10px',
                                    marginRight: '10px',
                                  }}
                                />

                                <Typography
                                  variant="body1"
                                  sx={{
                                    color:
                                      lesson?.id === selectedLessonId
                                        ? theme.palette.mode == 'dark'
                                          ? 'rgba(255, 255, 255, 0.87)'
                                          : '#000000'
                                        : theme.palette.mode == 'dark'
                                          ? 'rgb(141, 148, 163)'
                                          : '#585D66',
                                    fontFamily: 'Graphik Semibold',
                                    fontSize: '14px',
                                  }}
                                >
                                  {cropString(lesson?.title, 28) || 'New Lesson'}
                                </Typography>
                              </Box>
                              {!(
                                section.ContentLibraryCourseLesson.length < 2 ||
                                isCreatingLesson ||
                                disableAction
                              ) && (
                                <>
                                  <IconButton
                                    sx={{
                                      color: theme.palette.text.disabled,
                                      backgroundColor: isDarkTheme ? '#17171a' : '#ecefef',
                                      height: '32px',
                                      width: '32px',
                                      marginRight: '8px',
                                      position: 'absolute',
                                      right: '0',
                                      visibility: menuAnchorEl?.[lesson.id] && 'visible !important',
                                      '& svg': {
                                        maxWidth: '21px',
                                        position: 'relative',
                                        top: '1px',
                                      },
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      setMenuAnchorEl((prev) => ({
                                        ...prev,
                                        [lesson.id]: e.currentTarget,
                                      }))
                                    }}
                                    className="course-builder-delete-lesson-btn"
                                  >
                                    <MoreHoriz />
                                  </IconButton>
                                  <Menu
                                    anchorEl={menuAnchorEl?.[lesson.id]}
                                    keepMounted
                                    open={Boolean(menuAnchorEl?.[lesson.id])}
                                    onClose={() => {
                                      setMenuAnchorEl((prev) => ({ ...prev, [lesson.id]: null }))
                                    }}
                                    sx={{
                                      '& .MuiPaper-root': {
                                        borderRadius: '12px',
                                        boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                                        border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                                        backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                                        backgroundImage: 'unset',
                                        padding: '6px',
                                      },
                                      '& .MuiList-root': {
                                        padding: '0px',
                                      },
                                    }}
                                  >
                                    <ListItem
                                      sx={{
                                        justifyContent: 'left',
                                        fontFamily: 'unset',
                                        height: '40px',
                                        margin: '0px',
                                        cursor: 'pointer',
                                        color: '#F34646',
                                        '&:hover': {
                                          backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                                          borderRadius: '12px',
                                        },
                                      }}
                                      onClick={() => {
                                        setOpenWarningLessonDelete(true)
                                        setLessonIdToDelete({
                                          sectionId: section.id,
                                          lessonId: lesson?.id,
                                        })
                                        setMenuAnchorEl(null)
                                      }}
                                    >
                                      Delete Lesson
                                    </ListItem>
                                  </Menu>
                                </>
                              )}
                            </Box>
                          </div>
                        )}
                      </Draggable>
                    )
                  })}
                  <Button
                    onClick={async () => {
                      if (disableAction) return
                      /* save current lesson*/
                      await addLesson(section.id)
                    }}
                    variant="outlined"
                    sx={{
                      borderRadius: '10px',
                      marginTop: '8px',
                      display: isDragging ? 'none' : 'flex',
                      width: '76px',
                      height: '32px',
                      minHeight: 'unset',
                      borderColor: '#25262a',
                      color: theme.palette.mode == 'dark' ? 'rgb(141, 148, 163)' : 'rgb(88, 93, 102)',
                      fontFamily: 'Graphik Semibold',
                      fontSize: '14px',
                      '&:hover': {
                        borderColor: theme.palette.text.disabled,
                        color: theme.palette.mode == 'dark' ? 'rgb(141, 148, 163)' : 'rgb(88, 93, 102)',
                        backgroundColor: isDarkTheme ? '#29292c50' : `${hexPrimary}05`,
                      },
                      '& svg': {
                        maxWidth: '21px',
                      },
                    }}
                    disabled={isCreatingLesson || disableAction || isDragging}
                  >
                    <Add />
                    Add
                  </Button>
                  {providedLesson.placeholder}
                </Box>
              </Box>
            )
          }}
        </Droppable>
      </Box>
    </Box>
  )
}

const CourseBuilderMenu = ({
  sections,
  setSections,
  isLoadingSections,
  addSection,
  addLesson,
  selectLesson,
  deleteLesson,
  editSection,
  deleteSection,
  selectedLessonId,
  courseSettings,
  onLessonClick,
  hasUnsavedChanges,
  isCourseSettingsLoading,
  setHasUnsavedChanges,
  isCreatingLesson,
  disableAction,
  hasLessonBeenClicked,
  courseThumbnail,
  setCourseThumbnail,
  reset,
  setValue,
  descriptionValue,
  titleValue,
  control,
  hideCourseUpdatedToast,
  getLessonValues,
  saveLesson,
  isDragging,
  setIsDragging,
  setSelectedSectionId,
}) => {
  const [tabValue, setTabValue] = useState(0)
  const [anchorEls, setAnchorEls] = useState({})
  const [menuAnchorThumbnail, setMenuAnchorThumbnail] = useState(null)
  const router = useRouter()
  const searchParams = useSearchParams()
  const lessonId = searchParams.get('lesson_id')
  const { theme, isDarkTheme } = useAppTheme()
  const contentStatusOptions = ContentStatusOptions(theme)
  const [openWarningSectionDelete, setOpenWarningSectionDelete] = useState(false)
  const [openWarningLessonDelete, setOpenWarningLessonDelete] = useState(false)
  const [openWarningUnsavedChanges, setOpenWarningUnsavedChanges] = useState(false)
  const [sectionIdToDelete, setSectionIdToDelete] = useState(null)
  const [lessonIdToDelete, setLessonIdToDelete] = useState(null)
  const membership = useStore((state) => state.community.membership)
  const { uploadProgress, uploadedFiles, initUploadFiles, handleUploadFiles } = useUploadFiles('library', membership.id)
  const classes = useStyles()

  const [openImageCropper, setOpenImageCropper] = useState(false)
  const [previousCourseThumbnail, setPreviousCourseThumbnail] = useState(null)
  const [hasSetInitialQueryParam, setHasSetInitialQueryParam] = useState(false)
  const [openEditSectionModal, setOpenEditSectionModal] = useState(false)
  const [collapsedSections, setCollapsedSections] = useState([])
  const [editedSection, setEditedSection] = useState(null)
  const [orderedSections, setOrderedSections] = useState([])
  const [hasInitiallyLoadedCourseSettings, setHasInitiallyLoadedCourseSettings] = useState(false)
  const thumbnailPlaceholderDimensions = {
    width: '560px',
    height: '285px',
  }
  // to rerender height of sections after dragging
  const [lastDragOperation, setLastDragOperation] = useState(Date.now())

  useEffect(() => {
    const sectionsArray = Object.values(sections)
    sectionsArray.sort((a, b) => a.sequence - b.sequence)

    const orderedSections = sectionsArray.map((section) => {
      if (section?.ContentLibraryCourseLesson) {
        const lessonsMap = section.ContentLibraryCourseLesson
        const lessonsArray = Object.values(lessonsMap)
        lessonsArray.sort((a, b) => a.sequence - b.sequence)
        return {
          ...section,
          ContentLibraryCourseLesson: lessonsArray,
        }
      }
      return section
    })
    setOrderedSections(orderedSections)
  }, [sections])

  useEffect(() => {
    if (uploadProgress === 100) {
      setCourseThumbnail({
        ...uploadedFiles[0],
        originalImg: uploadedFiles[0],
        croppedImg: uploadedFiles[0],
      })
    }
  }, [uploadProgress, uploadedFiles])

  useEffect(() => {
    if (courseSettings && !hasInitiallyLoadedCourseSettings) {
      setHasInitiallyLoadedCourseSettings(true)
      setCourseThumbnail(courseSettings?.thumbnail)
      reset({
        title: courseSettings.title,
        description: stripHtml(courseSettings.description),
        thumbnail: courseSettings?.thumbnail,
        visibility: courseSettings.visibility,
        /* access: 'all_members', */
      })
    }
  }, [courseSettings, reset])

  useEffect(() => {
    if (!selectedLessonId && Object.keys(orderedSections).length > 0 && !hasSetInitialQueryParam) {
      setHasSetInitialQueryParam(true)
      let section = orderedSections[0]
      /* find the lesson_id within any of the  sections */
      if (lessonId) {
        section = orderedSections.find((section) => {
          return section.ContentLibraryCourseLesson.find((lesson) => {
            return lesson.id === lessonId
          })
        })
      }

      if (section?.ContentLibraryCourseLesson?.length > 0) {
        let lesson = section.ContentLibraryCourseLesson[0]
        if (lessonId) {
          lesson = section.ContentLibraryCourseLesson.find((lesson) => lesson.id === lessonId)
        }
        selectLesson(section.id, lesson.id)
      }
    }
  }, [orderedSections, selectedLessonId])

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      const uploadedFile = uploadedFiles[0]
      setValue('thumbnail', uploadedFile)
    }
  }, [uploadedFiles])

  useEffect(() => {
    const handleUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault()
        e.returnValue = ''
      }
    }

    window.addEventListener('beforeunload', handleUnload)

    return () => {
      window.removeEventListener('beforeunload', handleUnload)
    }
  }, [hasUnsavedChanges])

  const handleChange = (event, newValue) => {
    setTabValue(newValue)
  }

  const handleClick = (event, section_id) => {
    setAnchorEls((prevState) => ({
      ...prevState,
      [section_id]: prevState[section_id] ? null : event.currentTarget,
    }))
  }

  const handleRemoveThumbnail = () => {
    setValue('thumbnail', null)
    setCourseThumbnail(null)
    setMenuAnchorThumbnail(null)
  }

  const handleDropThumbnail = (f) => {
    setPreviousCourseThumbnail(courseThumbnail)
    initUploadFiles()
    handleUploadFiles([f], 'Cloudinary')
    handleOpenImageCropper()
  }

  const handleOpenImageCropper = () => {
    setOpenImageCropper(true)
  }

  const handleDragStart = async (result) => {
    setIsDragging(true)
  }

  const handleDragEnd = async (result) => {
    const { destination, source, draggableId } = result

    if (!destination) {
      setIsDragging(false)
      return
    }

    if (source.droppableId === 'all-sections' && destination.droppableId === 'all-sections') {
      // Section reordering logic remains unchanged
      const newSections = Array.from(orderedSections)
      const [reorderedSection] = newSections.splice(source.index, 1)
      newSections.splice(destination.index, 0, reorderedSection)

      const updatedSectionsWithNewOrder = newSections.map((section, index) => ({
        ...section,
        sequence: index + 1, // Update sequence to match the new order
      }))

      const sortedSections = updatedSectionsWithNewOrder.sort((a, b) => a.sequence - b.sequence)

      setOrderedSections(sortedSections)

      // Prepare data for API call
      const newOrders = sortedSections.map((section) => ({
        sectionId: section.id,
        sequence: section.sequence,
      }))

      await updateSectionsOrderApi(newOrders)

      toast.success('Order Updated')

      // set sections state with the content of orderedSections, ContentLibraryCourseLesson property needs to be converted to a map as well using the lesson id as key, right now is an array of objects
      const newSectionsMap = {}

      sortedSections.forEach((section) => {
        newSectionsMap[section.id] = {
          ...section,
          ContentLibraryCourseLesson: section.ContentLibraryCourseLesson.reduce((acc, lesson) => {
            acc[lesson.id] = lesson
            return acc
          }, {}),
        }
      })
      setIsDragging(false)
      setSections(newSectionsMap)
    } else if (
      source.droppableId.startsWith('section_lessons_') &&
      destination.droppableId.startsWith('section_lessons_')
    ) {
      const sourceSectionId = source.droppableId.split('_').pop()
      const destinationSectionId = destination.droppableId.split('_').pop()
      const sourceSectionIndex = orderedSections.findIndex((section) => section.id === sourceSectionId)
      const destinationSectionIndex = orderedSections.findIndex((section) => section.id === destinationSectionId)
      if (sourceSectionIndex === -1 || destinationSectionIndex === -1) return

      const movingLessonId = draggableId.split('_').pop()
      const movingLesson = orderedSections[sourceSectionIndex].ContentLibraryCourseLesson.find(
        (lesson) => lesson.id === movingLessonId,
      )
      if (!movingLesson) return

      // Remove the lesson from the source section
      let newSourceLessons = []

      orderedSections[sourceSectionIndex].ContentLibraryCourseLesson.map((lesson) => {
        let a = 1
        let b = 1
        let sum = a + b
        if (lesson.id !== movingLessonId) {
          newSourceLessons.push(lesson)
        }
      })

      // Insert the lesson into the destination section at the correct position
      let newDestinationLessons = [...orderedSections[destinationSectionIndex].ContentLibraryCourseLesson]
      if (sourceSectionId !== destinationSectionId) {
        newDestinationLessons.splice(destination.index, 0, movingLesson)
      } else {
        // This line is problematic when source and destination are the same; it's unnecessary and causes issues.
        // newDestinationLessons = newSourceLessons.splice(destination.index, 0, movingLesson);
        newSourceLessons.splice(destination.index, 0, movingLesson)
        newDestinationLessons = newSourceLessons // Correctly reference the updated list if they are the same
      }

      // Recalculate sequence numbers for both source and destination sections
      const updateSequenceNumbers = (lessons) =>
        lessons.map((lesson, index) => ({
          ...lesson,
          sequence: index,
        }))

      newSourceLessons = updateSequenceNumbers(newSourceLessons)
      if (sourceSectionId !== destinationSectionId) {
        newDestinationLessons = updateSequenceNumbers(newDestinationLessons)
      }

      // Update the orderedSections with the new lessons array for both sections
      const updatedOrderedSections = orderedSections.map((section, index) => {
        if (index === sourceSectionIndex) {
          return {
            ...section,
            ContentLibraryCourseLesson: newSourceLessons,
          }
        } else if (index === destinationSectionIndex) {
          return {
            ...section,
            ContentLibraryCourseLesson: newDestinationLessons,
          }
        }
        return section
      })

      setOrderedSections(updatedOrderedSections)

      // Convert the updated lessons array to a map for the API call
      const updatedLessons = updatedOrderedSections.flatMap((section) =>
        section.ContentLibraryCourseLesson.map((lesson) => ({
          lessonId: lesson.id,
          sequence: lesson.sequence,
          sectionId: section.id,
        })),
      )

      await updateLessonsOrderApi(updatedLessons)

      toast.success('Order Updated')

      // set sections state with the content of orderedSections, ContentLibraryCourseLesson property needs to be converted to a map as well using the lesson id as key, right now is an array of objects
      const newSections = {}
      updatedOrderedSections.forEach((section) => {
        newSections[section.id] = {
          ...section,
          ContentLibraryCourseLesson: section.ContentLibraryCourseLesson.reduce((acc, lesson) => {
            acc[lesson.id] = lesson
            return acc
          }, {}),
        }
      })
      setIsDragging(false)
      setSections(newSections)
      setSelectedSectionId(destinationSectionId)
    }

    setLastDragOperation(Date.now())
  }

  return (
    <>
      <Box
        sx={{
          width: '100%',
          height: '161px',
          minHeight: '161px',
          position: 'relative',
          backgroundColor: tint(-0.22, theme.palette.primary.main),
          /*background: `linear-gradient(
            135deg, 
            #FF32EB, 
            #CC31FB, 
            #3830F0, 
            #30ABE5, 
            #30DFDD
            )`,*/
        }}
      >
        <IconButton
          color="info"
          onClick={() => {
            hasUnsavedChanges ? setOpenWarningUnsavedChanges(true) : router.push(`/${membership.slug}/content`)
          }}
          aria-label="back to library"
          data-cy="back-to-library"
          size="large"
          sx={{
            position: 'absolute',
            top: '10px',
            left: '10px',
            backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
            color: '#8d94a2',
            zIndex: 3,
            padding: '0px',
            width: '40px',
            height: '40px',
          }}
        >
          <ChevronLeft24Icon />
        </IconButton>
        <Chip
          label={
            <Box
              sx={{
                height: '14px',
                display: 'flex',
                flexDirection: 'row',
                alignContent: 'center',
                alignItems: 'center',
              }}
            >
              <Circle
                sx={{
                  fontSize: '10px',
                  color:
                    courseSettings?.visibility == 'draft'
                      ? '#e7ad8b'
                      : theme.palette.mode === 'dark'
                        ? '#AEE78B'
                        : '#48b705',
                }}
              />
              <Typography
                sx={{
                  fontSize: '10px',
                  fontFamily: 'Graphik Semibold',
                  lineHeight: '10px',
                  marginTop: '1px',
                  marginLeft: '4px',
                  color:
                    courseSettings?.visibility == 'draft'
                      ? 'inherit'
                      : theme.palette.mode === 'dark'
                        ? '#AEE78B'
                        : '#2E3D25',
                }}
              >
                {courseSettings?.visibility == 'draft' ? 'DRAFT' : 'PUBLISHED'}
              </Typography>
              <Divider
                orientation="vertical"
                flexItem
                sx={{
                  borderColor: 'rgba(141, 148, 163, 0.16)',
                  marginLeft: '6px',
                  borderRadius: 1,
                  borderStyle: 'solid',
                  borderWidth: '1px',
                  marginRight: '6px',
                  color: theme.palette.text.disabled,
                }}
              />
              <SVGGear styles={{ color: '#8d94a2' }} />
            </Box>
          }
          clickable
          onClick={() => setTabValue(1)}
          sx={{
            zIndex: 3,
            position: 'absolute',
            bottom: '16px',
            left: '20px',
            height: '24px',
            backgroundColor: isDarkTheme ? '#FFFFFF08' : '#F3F5F5',
            '&:hover': {
              backgroundColor: isDarkTheme ? '#FFFFFF08' : '#ffffff',
            },
          }}
        />
        <AppTooltip
          leaveDelay={200}
          title={
            <Typography sx={{ fontFamily: 'Graphik Regular', wordBreak: 'break-all' }}>
              {courseSettings?.title}
            </Typography>
          }
          placement="top"
          sx={{
            display: courseSettings?.title?.length < 27 ? 'none' : 'block',
            wordBreak: 'break-all',
          }}
        >
          <Typography
            sx={{
              position: 'absolute',
              fontFamily: 'Graphik Semibold',
              fontSize: '18px',
              lineHeight: '24px',
              zIndex: 3,
              bottom: '52px',
              left: '20px',
              color: '#ffffff',
            }}
            variant="h6"
          >
            {cropString(courseSettings?.title, 27)}
          </Typography>
        </AppTooltip>
        <Image
          src={
            courseThumbnail?.croppedImg?.url
              ? courseThumbnail?.croppedImg?.url
              : isDarkTheme
                ? '/assets/default/images/dark-card.png'
                : '/assets/default/images/light-card.png'
          }
          alt={courseSettings?.name || 'Thumbnail image'}
          layout="fill"
          objectFit="cover"
          priority
        />
        <Box
          sx={{
            width: '100%',
            height: '161px',
            minHeight: '161px',
            position: 'relative',
            background: 'linear-gradient(#00000000, #00000030 50%, #000000FF 100%)',
            backgroundImage: isCourseSettingsLoading
              ? ''
              : courseThumbnail?.croppedImg?.url
                ? courseThumbnail?.croppedImg?.url
                : isDarkTheme
                  ? '/assets/default/images/dark-card.png'
                  : '/assets/default/images/light-card.png',
          }}
        />
      </Box>
      <Box sx={{ position: 'relative' }}>
        <Divider
          sx={{
            position: 'absolute',
            borderBottomWidth: 2,
            top: '48px',
            left: '0px',
            width: '100%',
            borderColor: theme.palette.mode == 'dark' ? '#25262b' : 'rgba(88,93,102,0.12)',
          }}
        />
        <Tabs
          sx={{
            marginTop: '8px',
            minHeight: '42px',
          }}
          variant="fullWidth"
          value={tabValue}
          indicatorColor="primary"
          onChange={handleChange}
          centered
        >
          <Tab sx={{ marginRight: '0px !important', fontSize: '0.875rem' }} label="Outline" />
          <Tab sx={{ marginRight: '0px !important', fontSize: '0.875rem' }} label="Settings" />
        </Tabs>
      </Box>
      <Box></Box>
      <Box className={classes.tabContainer}>
        {isLoadingSections && (
          <Box sx={{ padding: '16px' }}>
            <Skeleton variant="rectangular" height={40} sx={{ marginBottom: '12px', borderRadius: '8px' }} />

            {/* Skeletons for lessons */}
            {[...Array(3)].map((_, index) => (
              <Skeleton
                key={index}
                variant="rectangular"
                height={32}
                sx={{ marginBottom: '8px', borderRadius: '8px', marginLeft: '16px' }}
              />
            ))}

            <Skeleton variant="rectangular" height={40} sx={{ marginBottom: '12px', borderRadius: '8px' }} />

            {/* Skeletons for lessons */}
            {[...Array(2)].map((_, index) => (
              <Skeleton
                key={index}
                variant="rectangular"
                height={32}
                sx={{ marginBottom: '8px', borderRadius: '8px', marginLeft: '16px' }}
              />
            ))}

            {/* Divider */}
            <Divider sx={{ marginBottom: '16px' }} />

            {/* Skeleton for Add Section button */}
            <Skeleton
              variant="rectangular"
              height={36}
              sx={{ marginBottom: '16px', borderRadius: '20px', width: '100%' }}
            />
          </Box>
        )}
        {!isLoadingSections && tabValue === 0 && (orderedSections as any)?.length >= 0 && (
          <>
            <DragDropContext onDragEnd={handleDragEnd} onBeforeDragStart={handleDragStart}>
              <Droppable
                droppableId="all-sections"
                type="section"
                isCombineEnabled={false}
                isDropDisabled={false}
                ignoreContainerClipping={false}
              >
                {(provided) => (
                  <Box {...provided.droppableProps} ref={provided.innerRef} sx={{ marginBottom: '28%' }}>
                    {(orderedSections as any).map((section, index) => {
                      const isCollapsed = collapsedSections.includes(section?.id)
                      return section?.ContentLibraryCourseLesson?.length ? (
                        <Draggable
                          key={section.id}
                          draggableId={`section_${section.id}`}
                          index={index}
                          isDragDisabled={disableAction || isDragging}
                        >
                          {(provided) => (
                            <div
                              key={`${section.id}-${lastDragOperation}`}
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              <CourseBuilderSection
                                provided={provided}
                                section={section}
                                classes={classes}
                                sections={orderedSections}
                                anchorEl={anchorEls[section.id]}
                                setAnchorEl={(el) => {
                                  setAnchorEls((prev) => ({ ...prev, [section.id]: el }))
                                }}
                                setAnchorEls={setAnchorEls}
                                setOpenEditSectionModal={setOpenEditSectionModal}
                                isCreatingLesson={isCreatingLesson}
                                addLesson={addLesson}
                                disableAction={disableAction}
                                collapsedSections={collapsedSections}
                                setCollapsedSections={setCollapsedSections}
                                isCollapsed={isCollapsed}
                                handleClick={handleClick}
                                setOpenWarningSectionDelete={setOpenWarningSectionDelete}
                                onLessonClick={onLessonClick}
                                selectedLessonId={selectedLessonId}
                                setOpenWarningLessonDelete={setOpenWarningLessonDelete}
                                setLessonIdToDelete={setLessonIdToDelete}
                                setEditedSection={setEditedSection}
                                setSectionIdToDelete={setSectionIdToDelete}
                                hasLessonBeenClicked={hasLessonBeenClicked}
                                isDragging={disableAction || isDragging}
                              />

                              <Divider />
                            </div>
                          )}
                        </Draggable>
                      ) : null
                    })}
                  </Box>
                )}
              </Droppable>
            </DragDropContext>
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                width: '100%',
              }}
            >
              <Divider />
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '24px 20px',
                  bottom: '0px',
                }}
              >
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  sx={{
                    width: '100%',
                    borderRadius: '10px !important',
                    height: '32px',
                    backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
                    boxShadow: 'none',
                    color: '#fff',
                  }}
                  onClick={addSection}
                  disabled={isCreatingLesson || disableAction || isDragging}
                >
                  Add Section
                </Button>
              </Box>
              <Divider />
            </Box>
          </>
        )}
        {tabValue === 1 && (
          <>
            <Box
              className={classes.settingsContainer}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                height: '100%',
                gap: 3,
                padding: '16px 0 0 0',
                margin: '0px 4px 0px 20px',
              }}
            >
              <Box>
                <Typography className={classes.inputLabel} gutterBottom>
                  Course Status
                </Typography>
                <Controller
                  name="visibility"
                  control={control}
                  defaultValue="draft"
                  render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                    <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                      <Select
                        placeholder=""
                        variant="outlined"
                        error={Boolean(error)}
                        className={classes.inputFields}
                        sx={{
                          '& .MuiOutlinedInput-input': {
                            top: '0px',
                            position: 'relative',
                          },
                          '& .MuiListItem-root': {
                            '&:hover': {
                              backgroundColor: 'transparent !important',
                            },
                          },
                        }}
                        value={value}
                        onChange={(e) => {
                          onChange(e.target.value)
                          setHasUnsavedChanges(true)
                          hideCourseUpdatedToast.current = false
                        }}
                        onBlur={onBlur}
                        data-cy="profile-personality-type-select-field"
                        MenuProps={{
                          PaperProps: {
                            sx: {
                              borderRadius: '12px',
                              boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                              border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                              backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                              backgroundImage: 'unset',
                              '& .MuiOutlinedInput-input': {
                                top: '-3px',
                                position: 'relative',
                              },
                              padding: '6px',
                              '& li.Mui-selected': {
                                backgroundColor: isDarkTheme ? '#1b1b1f !important' : '#f8f8f8 !important',
                                backgroundImage: 'unset',
                                borderRadius: '12px',
                              },
                              '& .MuiList-root': {
                                padding: '0px',
                              },
                            },
                          },
                        }}
                      >
                        {contentStatusOptions.map((option) => (
                          <MenuItem
                            key={option.value}
                            value={option.value}
                            sx={{
                              '&:hover': {
                                backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                                borderRadius: '12px',
                              },
                            }}
                          >
                            <ListItem
                              sx={{
                                justifyContent: 'left',
                                paddingLeft: '5px',
                                paddingTop: '0px',
                                fontFamily: 'Graphik Medium',
                                fontSize: '14px',
                                color: option.value == 'published' && 'rgb(80, 200, 120)',
                                '& .MuiListItem-root': {
                                  lineHeight: '14px !important',
                                },
                                '&:hover': {
                                  backgroundColor: 'transparent',
                                },
                              }}
                              style={{
                                justifyContent: 'left',
                                paddingLeft: '5px',
                                fontFamily: 'unset',
                              }}
                            >
                              {option.label}
                            </ListItem>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Box>
              <Box>
                <Grid container justifyContent="space-between" alignItems="center">
                  <Grid item>
                    <Typography className={classes.inputLabel} gutterBottom>
                      Course Title
                    </Typography>
                  </Grid>
                  <Grid item>
                    <p className={classes.subLabel}>{40 - (titleValue?.length ? titleValue?.length : 0)}</p>
                  </Grid>
                </Grid>
                <Controller
                  name="title"
                  control={control}
                  defaultValue=""
                  render={({ field }) => (
                    <TextField
                      className={classes.inputFields}
                      {...field}
                      placeholder="Branding Blueprint"
                      variant="outlined"
                      name="title"
                      InputProps={{
                        inputProps: { maxLength: 40 },
                      }}
                      fullWidth
                      onChange={(e) => {
                        field.onChange(e)
                        setHasUnsavedChanges(true)
                        hideCourseUpdatedToast.current = false
                      }}
                    />
                  )}
                />
              </Box>
              <Box>
                <Grid container justifyContent="space-between" alignItems="center">
                  <Grid item>
                    <Typography className={classes.inputLabel} gutterBottom>
                      Description
                    </Typography>
                  </Grid>
                  <Grid item>
                    <p className={classes.subLabel}>{95 - (descriptionValue?.length ? descriptionValue?.length : 0)}</p>
                  </Grid>
                </Grid>
                <Controller
                  name="description"
                  control={control}
                  defaultValue=""
                  render={({ field }) => (
                    <TextField
                      {...field}
                      placeholder="Course description..."
                      size="small"
                      variant="outlined"
                      multiline
                      rows={5}
                      InputProps={{
                        inputProps: { maxLength: 95 },
                      }}
                      fullWidth
                      name="description"
                      onChange={(e) => {
                        field.onChange(e)
                        setHasUnsavedChanges(true)
                        hideCourseUpdatedToast.current = false
                      }}
                    />
                  )}
                />
              </Box>
              <Box>
                <Typography className={classes.inputLabel} gutterBottom>
                  Thumbnail
                </Typography>
                <Controller
                  name="thumbnail"
                  control={control}
                  defaultValue={''}
                  render={({ field }) => {
                    if (field?.value) {
                      return (
                        <Box
                          sx={{
                            width: '100%',
                            height: '137px',
                            minHeight: '137px',
                            position: 'relative',
                          }}
                        >
                          <Button
                            clickable
                            onClick={(event) => setMenuAnchorThumbnail(event.currentTarget)}
                            variant="outlined"
                            sx={{
                              zIndex: 1,
                              position: 'absolute',
                              bottom: '8px',
                              right: '8px',
                              margin: '0px',
                              height: '32px',
                              width: '32px',
                              backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
                              borderColor: isDarkTheme ? '#F3F5F5' : 'rgb(23, 23, 26)',
                              borderRadius: 12,
                              minHeight: '32px',
                              minWidth: '32px',
                              color: theme.palette.text.disabled,
                              '&:hover': {
                                backgroundColor: isDarkTheme ? '#29292c' : '#F3F5F5',
                                borderColor: isDarkTheme ? '#F3F5F5' : '#29292c',
                              },
                              '& svg': {
                                position: 'relative',
                                top: '1px',
                              },
                            }}
                          >
                            <MoreHoriz />
                          </Button>
                          <Menu
                            anchorEl={menuAnchorThumbnail}
                            keepMounted
                            open={Boolean(menuAnchorThumbnail)}
                            onClose={() => setMenuAnchorThumbnail(null)}
                            sx={{
                              '& .MuiPaper-root': {
                                borderRadius: '12px',
                                boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                                border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                                backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                                backgroundImage: 'unset',
                                padding: '6px',
                              },
                              '& .MuiList-root': {
                                padding: '0px',
                              },
                            }}
                          >
                            <ListItem
                              sx={{
                                justifyContent: 'left',
                                fontFamily: 'unset',
                                height: '40px',
                                margin: '0px',
                                cursor: 'pointer',
                                '&:hover': {
                                  backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                                  borderRadius: '12px',
                                },
                              }}
                              onClick={handleRemoveThumbnail}
                            >
                              Remove Thumbnail
                            </ListItem>
                          </Menu>
                          <Button
                            component="label"
                            variant="outlined"
                            sx={{
                              zIndex: 1,
                              position: 'absolute',
                              bottom: '8px',
                              height: '32px',
                              right: '44px',
                              backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
                              borderColor: isDarkTheme ? '#F3F5F5' : 'rgb(23, 23, 26)',
                              borderRadius: 12,
                              minHeight: '32px',
                              '&:hover': {
                                backgroundColor: isDarkTheme ? '#29292c' : '#F3F5F5',
                                borderColor: isDarkTheme ? '#F3F5F5' : '#29292c',
                              },
                            }}
                          >
                            <Typography
                              sx={{
                                fontSize: '13px',
                                fontFamily: 'Graphik Medium',
                                color: theme.palette.text.primary,
                                lineHeight: '16px',
                              }}
                            >
                              <span
                                style={{
                                  marginRight: '8px',
                                }}
                              >
                                <SVGPhoto width={13} height={13} />
                              </span>
                              Replace
                            </Typography>
                            <VisuallyHiddenInput
                              onChange={(e) => {
                                //get file
                                if (e.target.files) {
                                  handleDropThumbnail(e.target.files[0])
                                }
                              }}
                              type="file"
                            />
                          </Button>
                          <Image
                            src={
                              isCourseSettingsLoading ? (
                                <Skeleton variant="rectangular" width="100%" height="177px" />
                              ) : courseThumbnail?.croppedImg?.url ? (
                                courseThumbnail?.croppedImg?.url
                              ) : isDarkTheme ? (
                                '/assets/default/images/dark-card.png'
                              ) : (
                                '/assets/default/images/light-card.png'
                              )
                            }
                            alt={courseSettings?.name || 'Thumbnail image'}
                            layout="fill"
                            priority
                            objectFit="cover"
                            style={{
                              borderRadius: '12px',
                            }}
                          />
                        </Box>
                      )
                    }
                    return (
                      <Box
                        className="background-color18 border-color02"
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          height: 216,
                          borderRadius: '12px',
                          borderStyle: 'dashed',
                          borderWidth: '1px',
                          cursor: 'pointer',
                        }}
                      >
                        <AppDropzone
                          file={field?.value?.[0]}
                          onDropFile={handleDropThumbnail}
                          placeholder={
                            <Box className="text-center">
                              <Box
                                className="flex justify-center"
                                color={theme.palette.primary.main}
                                marginBottom={'14px'}
                              >
                                <SVGPhotoAdd width={20} height={20} />
                              </Box>
                              <Typography
                                sx={{
                                  fontSize: '14px',
                                  fontFamily: 'Graphik Medium',
                                  color: theme.palette.text.primary,
                                  lineHeight: '16px',
                                }}
                                marginBottom={'8px'}
                              >
                                Add Thumbnail
                              </Typography>
                              <Typography
                                sx={{
                                  fontSize: '12px',
                                  fontFamily: 'Graphik Regular',
                                  lineHeight: '16px',
                                  color: theme.palette.text.disabled,
                                }}
                              >
                                Recommended size <br />
                                1120px x 570px
                              </Typography>
                            </Box>
                          }
                        />
                      </Box>
                    )
                  }}
                />
              </Box>
            </Box>
            <Box
              sx={{
                zIndex: 2,
                mt: 3,
                bottom: 0,
                position: 'sticky',
                width: '100%',
                backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
              }}
            >
              {/* <Divider /> */}
            </Box>
          </>
        )}
        {openImageCropper && (
          <ImageCropDialog
            open={openImageCropper}
            onSaved={(image) => {
              setOpenImageCropper(false)
              setCourseThumbnail(image)
              setHasUnsavedChanges(true)
              hideCourseUpdatedToast.current = false
            }}
            onClose={() => {
              setCourseThumbnail(previousCourseThumbnail)
              setOpenImageCropper(false)
            }}
            setImage={() => {}}
            image={courseThumbnail?.originalImg?.url}
            aspectRatio={560 / 285}
            initialCroppedArea={{
              croppedArea: courseThumbnail?.croppedImg?.croppedArea,
              croppedAreaPixels: courseThumbnail?.croppedImg?.croppedAreaPixels,
              zoom: courseThumbnail?.croppedImg?.zoom || courseThumbnail?.croppedImg?.zoom,
            }}
            onCancel={() => {
              setCourseThumbnail(previousCourseThumbnail)
              setOpenImageCropper(false)
            }}
            placeholderHeight={thumbnailPlaceholderDimensions.height}
            placeholderWidth={thumbnailPlaceholderDimensions.width}
          />
        )}
        <AppWarning
          sx={{
            '& .MuiButton-root': {
              borderRadius: '10px',
            },
          }}
          showTitle={false}
          content={
            <Box>
              <Error
                fontSize="large"
                sx={{
                  margin: 0,
                  padding: 0,
                }}
              />
              <Typography
                sx={{
                  fontSize: '18px',
                  fontFamily: 'Graphik Medium',
                  color: theme.palette.text.primary,
                  marginTop: '16px',
                }}
              >
                You have unsaved changes!
              </Typography>
              <Typography
                sx={{
                  fontSize: '14px',
                  fontFamily: 'Graphik Regular',
                  lineHeight: '20px',
                  color: theme.palette.text.disabled,
                  marginTop: '12px',
                }}
              >
                Are you sure you want to leave without saving?
              </Typography>
              <Typography
                sx={{
                  fontSize: '14px',
                  fontFamily: 'Graphik Regular',
                  lineHeight: '20px',
                  color: theme.palette.text.disabled,
                  marginTop: '16px',
                }}
              >
                All unsaved changes will be lost.
              </Typography>
            </Box>
          }
          cancelButtonText="Cancel"
          proceedButtonText="Confirm"
          open={openWarningUnsavedChanges}
          onClose={() => {
            setOpenWarningUnsavedChanges(false)
          }}
          onProceed={() => {
            router.push(`${getCommunityBaseURL(membership)}/library`)
          }}
        />

        <AppWarning
          sx={{
            '& .proceed-button': {
              backgroundColor: '#F34646',
              color: '#FFFFFF',
              '&:hover': {
                backgroundColor: '#F34646',
              },
            },
            '& .MuiButtonBase-root': {
              borderRadius: '10px !important',
            },
          }}
          showTitle={false}
          content={
            <Box>
              <SVGTrash width={20} height={24} styles={{ color: '#F34646', display: 'inline-block' }} />
              <Typography
                sx={{
                  fontFamily: 'Graphik Medium',
                  fontSize: '18px',
                  lineHeight: '24px',
                  marginTop: '18px',
                }}
              >
                Permanently Delete?
              </Typography>
              <Typography
                sx={{
                  fontSize: '14px',
                  fontFamily: 'Graphik Regular',
                  lineHeight: '20px',
                  color: theme.palette.text.disabled,
                  marginTop: '12px',
                }}
              >
                Deleting the selected section will also delete all lessons and content associated with this section.
              </Typography>
              <Typography
                sx={{
                  fontSize: '14px',
                  fontFamily: 'Graphik Regular',
                  lineHeight: '20px',
                  color: theme.palette.text.disabled,
                  marginTop: '20px',
                }}
              >
                This action cannot be undone.
              </Typography>
            </Box>
          }
          cancelButtonText="Cancel"
          proceedButtonText="Delete"
          open={openWarningSectionDelete}
          onClose={() => setOpenWarningSectionDelete(false)}
          onProceed={async () => {
            /* save current lesson */
            await saveLesson(getLessonValues())
            deleteSection(sectionIdToDelete)
            setOpenWarningSectionDelete(false)
            setSectionIdToDelete(null)
          }}
        />
        <AppWarning
          showTitle={false}
          sx={{
            '& .proceed-button': {
              backgroundColor: '#F34646',
              color: '#FFFFFF',
              '&:hover': {
                backgroundColor: '#F34646',
              },
            },
            '& .MuiButtonBase-root': {
              borderRadius: '10px !important',
            },
          }}
          content={
            <Box>
              <SVGTrash width={20} height={24} styles={{ color: '#F34646', display: 'inline-block' }} />
              <Typography
                sx={{
                  marginTop: '18px',
                  fontFamily: 'Graphik Medium',
                  fontSize: '18px',
                  lineHeight: '24px',
                }}
              >
                Permanently Delete?
              </Typography>
              <Typography
                sx={{
                  fontSize: '14px',
                  fontFamily: 'Graphik Regular',
                  lineHeight: '20px',
                  color: theme.palette.text.disabled,
                  marginTop: '12px',
                }}
              >
                Deleting the selected lesson will also delete all content associated with this lesson.
              </Typography>
              <Typography
                sx={{
                  fontSize: '14px',
                  fontFamily: 'Graphik Regular',
                  lineHeight: '20px',
                  color: theme.palette.text.disabled,
                  marginTop: '20px',
                }}
              >
                This action cannot be undone.
              </Typography>
            </Box>
          }
          cancelButtonText="Cancel"
          proceedButtonText="Confirm"
          open={openWarningLessonDelete}
          onClose={() => setOpenWarningLessonDelete(false)}
          onProceed={async () => {
            deleteLesson(lessonIdToDelete.sectionId, lessonIdToDelete.lessonId)
            setOpenWarningLessonDelete(false)
            setLessonIdToDelete(null)
          }}
        />
      </Box>
      {openEditSectionModal && (
        <EditSectionModal
          open={openEditSectionModal}
          onCancel={() => setOpenEditSectionModal(false)}
          section={editedSection}
          onSave={async (newName: string, newVisibility: string) => {
            await editSection(editedSection.id, {
              name: newName,
              visibility: newVisibility,
            })
            setOpenEditSectionModal(false)
          }}
        />
      )}
    </>
  )
}

export default CourseBuilderMenu
