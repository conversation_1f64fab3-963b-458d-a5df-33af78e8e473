import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'
import Tooltip from '@mui/material/Tooltip'
import Typography from '@mui/material/Typography'
import clsx from 'clsx'
import Image from 'next/image'
import React, { useMemo } from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { CroppedImage } from '@/components/images/cropped-image'
import useAppCookie from '@/memberup/components/hooks/use-app-cookie'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import { getLocationInfo } from '@/memberup/libs/utils'
import { selectMembership, selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const MUMenu: React.FC = () => {
  const { isDarkTheme, theme, newGradient } = useAppTheme()
  const membership = useAppSelector((state) => selectMembership(state))
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const { getAppCookie } = useAppCookie()
  const originMembershipStr = getAppCookie('origin-membership')
  const originMembership = originMembershipStr ? JSON.parse(originMembershipStr) : {}
  const isUniversity = membership?.slug === 'university'
  const mainMembership = isUniversity ? originMembership : membership

  const renderSiteIcon = useMemo(() => {
    const favicon = isUniversity ? originMembership.favicon : membershipSetting?.favicon
    if (favicon && favicon !== 'null') {
      return (
        <CroppedImage
          cropArea={{ x: 0, y: 0, width: 100, height: 100 }}
          src={favicon}
          alt="Community icon"
          width={30}
          height={30}
        />
      )
    }
    if (mainMembership?.name) {
      return (
        <Typography className="bold" variant="h6" style={{ fontSize: 20 }}>
          {mainMembership.name.charAt(0).toUpperCase()}
        </Typography>
      )
    }
    return null
  }, [membership, membershipSetting])

  const handleClick = (slug) => {
    const { domain, port, protocol, isLocalhost } = getLocationInfo()
    const isUniversity = slug === 'university'

    location.href =
      `${protocol}//${slug}.${domain}` +
      (port ? `:${port}` : '') +
      (isUniversity
        ? `?origin=${membership.slug}&name=${membership.name}${
            membershipSetting?.favicon ? `&favicon=${membershipSetting?.favicon}` : ''
          }`
        : '')
  }

  return (
    <Box
      className="mu-menu"
      sx={{
        height: '100%',
        width: 72,
        padding: '11px',
        borderRightColor: isDarkTheme ? '#17171A' : '#F3F5F5',
        borderRightWidth: 1,
        borderRightStyle: 'solid',
      }}
    >
      <Stack alignItems="center">
        <Tooltip title="MemberUp University" arrow placement="right">
          <Box
            className={clsx('d-flex align-center app-image-wrapper justify-center', {
              active: isUniversity,
            })}
            sx={{
              height: 40,
              width: 40,
              maxWidth: 40,
              cursor: 'pointer',
              borderRadius: '12px',
              mt: '16px',
              '&.active, &:hover': {
                outlineColor: theme.palette.text.primary,
                outlineOffset: '3px',
                outlineStyle: 'solid',
                outlineWidth: '2px',
              },
              background: 'linear-gradient(to right bottom, #434344, #39393a, #2f2f30, #252627, #1c1d1e)',
            }}
            onClick={() => handleClick('university')}
          >
            <AppImg src="/assets/default/logos/memberup-university.png" alt="MU icon" width={40} height={40} />
          </Box>
        </Tooltip>
        <Tooltip title={mainMembership?.name || ''} arrow placement="right">
          <Box
            className={clsx('d-flex align-center app-image-wrapper justify-center', {
              active: !isUniversity,
            })}
            sx={{
              background: newGradient,
              height: 40,
              width: 40,
              maxWidth: 40,
              cursor: 'pointer',
              borderRadius: '12px',
              marginTop: '16px',
              overflow: 'hidden',
              '&.active, &:hover': {
                outlineColor: theme.palette.text.primary,
                outlineOffset: '3px',
                outlineStyle: 'solid',
                outlineWidth: '2px',
              },
            }}
            onClick={() => handleClick(mainMembership.slug)}
          >
            {renderSiteIcon}
          </Box>
        </Tooltip>
      </Stack>
    </Box>
  )
}

export default MUMenu
