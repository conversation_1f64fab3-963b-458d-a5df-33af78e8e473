import {
  Box,
  Collapse,
  Divider,
  Grid,
  IconButton,
  LinearProgress,
  List,
  ListItemButton,
  ListItemText,
  Skeleton,
  Typography,
} from '@mui/material'
import { makeStyles } from '@mui/styles'
import { rgbToHex } from '@mui/system'
import Image from 'next/image'
import { tint } from 'polished'

import AppTooltip from '../common/app-tooltip'
import useAppTheme from '../hooks/use-app-theme'
import SVGAudioCourse from '../svgs/audio-course'
import SVGChevronDown from '../svgs/chevron-down'
import SVGCircleCheck from '../svgs/circle-check'
import SVGTextCourse from '../svgs/text-course'
import SVGVideoCourse from '../svgs/video-course'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { cropString } from '@memberup/shared/src/libs/string-utils'
import { ChevronLeft24Icon } from '@/components/icons'

const useStyles = makeStyles((theme) => ({
  activeLessonText: {
    color: '#000',
  },
  navigationalButton: {
    borderRadius: 2,
    '&:hover': {
      backgroundColor: theme.palette.primary.main,
      color: '#fff',
    },
  },
  listItem: {
    padding: '0px 10px',
    margin: '0px',
    borderRadius: '12px',
    height: '56px',
  },
}))

export default function CourseNavigator({
  courseSettings,
  sections,
  selectedSectionId,
  selectedLessonId,
  handleBackButtonClick,
  calculateCompletionPercentage,
  handleSectionClick,
  handleLessonClick,
  areAllLessonsCompleted,
  openSectionIndex,
  user,
  userProfile,
  isLoading,
  isMarkAsDoneBtnDisabled,
}) {
  const classes = useStyles()
  const { theme, isDarkTheme } = useAppTheme()
  const maxLengthNavigatorItem = 30

  // Convert sections map to array and sort
  const sectionsArray = Object.values(sections).sort((a: any, b: any) => a.sequence - b.sequence)

  // Convert lessons map to array and sort for each section
  const orderedSections = sectionsArray.map((section: any) => {
    const lessonsArray = Object.values(section.ContentLibraryCourseLesson).sort(
      (a: any, b: any) => a.sequence - b.sequence,
    )
    return {
      ...section,
      ContentLibraryCourseLesson: lessonsArray,
    }
  })

  return (
    <Box
      sx={{
        height: '100%',
        background:
          theme.palette.mode === 'dark' ? 'linear-gradient(180deg, #2e2e2f 0%, #2e2e2f 25%, #17171a 50%)' : 'initial',
      }}
    >
      <Box
        sx={{
          width: '100%',
          height: '161px',
          minHeight: '161px',
          position: 'relative',
          backgroundColor: tint(-0.22, theme.palette.primary.main),
        }}
      >
        <IconButton
          color="info"
          onClick={handleBackButtonClick}
          aria-label="back to library"
          size="large"
          sx={{
            position: 'absolute',
            top: '10px',
            left: '10px',
            height: '40px',
            backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
            color: '#8d94a2',
            zIndex: 3,
            padding: '0px',
            width: '40px',
          }}
        >
          <ChevronLeft24Icon />
        </IconButton>
        {isLoading || !courseSettings?.title ? (
          <Skeleton variant="rectangular" width="100%" height="176.5px" sx={{ backgroundColor: 'black' }} />
        ) : (
          <Image
            src={
              courseSettings?.thumbnail?.croppedImg?.url
                ? courseSettings?.thumbnail?.croppedImg?.url
                : isDarkTheme
                  ? '/assets/default/images/dark-card.png'
                  : '/assets/default/images/light-card.png'
            }
            alt={courseSettings?.title}
            layout="fill"
            priority
          />
        )}
      </Box>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          height: '149px',
          minHeight: '149px',
          flexDirection: 'column',
          padding: '20px',
        }}
      >
        <Box>
          <Typography
            sx={{
              fontSize: '18px',
              fontFamily: 'Graphik Semibold',
              color: theme.palette.text.primary,
              lineHeight: '24px',
              marginBottom: '24px',
            }}
          >
            {cropString(courseSettings?.title, 27)}
          </Typography>
        </Box>
        <Box sx={{ width: '100%', position: 'relative', marginBottom: '8px' }}>
          <LinearProgress
            variant="determinate"
            sx={{
              height: '8px',
              '& .MuiLinearProgress-bar': { background: isDarkTheme ? '#AEE78B' : '#48B705' },
            }}
            value={calculateCompletionPercentage() || 0}
          />
        </Box>
        <Grid container>
          <Grid item xs={6}>
            <Typography
              sx={{
                marginTop: '4px',
                fontFamily: 'Graphik Semibold',
                fontSize: '12px',
                color: theme.palette.text.primary,
              }}
            >
              Your Progress
            </Typography>
          </Grid>
          <Grid
            item
            xs={6}
            sx={{
              display: 'flex',
              justifyContent: 'flex-end',
            }}
          >
            <AppProfileImage
              imageUrl={userProfile?.image || user?.image}
              cropArea={userProfile?.image_crop_area || user?.image_crop_area}
              name={user?.first_name || user?.email}
              size={20}
              isClickable={false}
            />
            <Typography
              sx={{
                marginTop: '4px',
                marginLeft: '4px',
                fontFamily: 'Graphik Semibold',
                fontSize: '12px',
              }}
            >
              {calculateCompletionPercentage()}%
            </Typography>
          </Grid>
        </Grid>
      </Box>
      <Divider sx={{ borderColor: 'rgba(141, 148, 163, 0.16)', borderWidth: '1px' }} />

      {isLoading && (
        <Box sx={{ padding: '16px' }}>
          <Skeleton variant="rectangular" height={40} sx={{ marginBottom: '12px', borderRadius: '8px' }} />

          {/* Skeletons for lessons */}
          {[...Array(3)].map((_, index) => (
            <Skeleton
              key={index}
              variant="rectangular"
              height={32}
              sx={{ marginBottom: '8px', borderRadius: '8px', marginLeft: '16px' }}
            />
          ))}

          <Skeleton variant="rectangular" height={40} sx={{ marginBottom: '12px', borderRadius: '8px' }} />

          {/* Skeletons for lessons */}
          {[...Array(2)].map((_, index) => (
            <Skeleton
              key={index}
              variant="rectangular"
              height={32}
              sx={{ marginBottom: '8px', borderRadius: '8px', marginLeft: '16px' }}
            />
          ))}

          {/* Divider */}
          <Divider sx={{ marginBottom: '16px' }} />

          {/* Skeleton for Add Section button */}
          <Skeleton
            variant="rectangular"
            height={36}
            sx={{ marginBottom: '16px', borderRadius: '20px', width: '100%' }}
          />
        </Box>
      )}
      {(!orderedSections || Object.keys(orderedSections).length === 0) && !isLoading && (
        <Box sx={{ padding: '16px' }}>
          <Typography
            sx={{
              fontSize: '14px',
              fontFamily: 'Graphik Semibold',
              color: theme.palette.text.primary,
              lineHeight: '20px',
              display: 'flex',
              flexWrap: 'wrap',
            }}
          >
            No published lessons available yet
          </Typography>
        </Box>
      )}
      {
        <Box
          sx={{
            padding: '16px 4px 16px 20px',
          }}
        >
          {/* List of Videos */}
          <List sx={{ padding: 0 }}>
            {orderedSections.map((section, sectionIndex) => (
              <div key={section.id}>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '0px 0px',
                    borderRadius: '12px',
                    height: '56px',
                    marginBottom: '8px',
                  }}
                >
                  <ListItemText
                    primary={
                      <>
                        <AppTooltip
                          leaveDelay={200}
                          title={
                            <Typography sx={{ fontFamily: 'Graphik Regular', fontSize: '14px' }}>
                              {section.name}
                            </Typography>
                          }
                          placement="top"
                          sx={{
                            display: section.name.length > maxLengthNavigatorItem ? 'block' : 'none',
                            wordBreak: 'break-all',
                          }}
                        >
                          <Typography
                            sx={{
                              fontSize: '14px',
                              fontFamily: 'Graphik Semibold',
                            }}
                          >
                            {cropString(section.name, maxLengthNavigatorItem)}
                          </Typography>
                        </AppTooltip>
                      </>
                    }
                  />
                  {areAllLessonsCompleted(section) && <SVGCircleCheck styles={{ marginRight: '1px' }} />}
                  <IconButton
                    sx={{
                      height: '32px',
                      width: '32px',
                      backgroundColor: isDarkTheme ? '#17171A' : '#F3F5F5',
                    }}
                    onClick={() => handleSectionClick(sectionIndex)}
                  >
                    {openSectionIndex.includes(sectionIndex) ? (
                      <SVGChevronDown
                        styles={{
                          transform: 'rotate(180deg)',
                        }}
                      />
                    ) : (
                      <SVGChevronDown width={'50px'} height={'50px'} />
                    )}
                  </IconButton>
                </Box>
                <Collapse in={openSectionIndex.includes(sectionIndex)} timeout="auto" unmountOnExit>
                  {' '}
                  <List disablePadding sx={{ marginLeft: '-3px' }}>
                    {section.ContentLibraryCourseLesson.map((lesson, lessonIndex) => {
                      const selected = selectedSectionId === section.id && selectedLessonId === lesson.id

                      const hexPrimary = rgbToHex(theme.palette.primary.main).slice(0, -2)
                      return (
                        <ListItemButton
                          key={lesson.id}
                          selected={selected}
                          onClick={() => handleLessonClick(section?.id, lesson?.id)}
                          className={classes.listItem}
                          style={{
                            backgroundColor: selected && (isDarkTheme ? '#FFFFFF08' : `${hexPrimary}10`),
                          }}
                          disabled={isMarkAsDoneBtnDisabled}
                        >
                          {lesson.done ? (
                            <SVGCircleCheck
                              styles={{
                                color: selected ? (theme.palette.mode === 'dark' ? '#fff' : '#000') : '#8d94a3',
                                marginRight: '-10px',
                              }}
                            />
                          ) : lesson.type === 'video' ? (
                            <SVGVideoCourse
                              styles={{
                                color: selected ? (theme.palette.mode === 'dark' ? '#fff' : '#000') : '#8d94a3',
                                marginRight: '-13px',
                              }}
                            />
                          ) : lesson.type === 'audio' ? (
                            <SVGAudioCourse
                              styles={{
                                color: selected ? (theme.palette.mode === 'dark' ? '#fff' : '#000') : '#8d94a3',
                                marginRight: '-10px',
                              }}
                            />
                          ) : (
                            <SVGTextCourse
                              styles={{
                                color: selected ? (theme.palette.mode === 'dark' ? '#fff' : '#000') : '#8d94a3',
                                marginRight: '-8px',
                              }}
                            />
                          )}
                          <ListItemText
                            primary={
                              <>
                                <AppTooltip
                                  leaveDelay={200}
                                  title={
                                    <Typography sx={{ fontFamily: 'Graphik Regular', fontSize: '14px' }}>
                                      {lesson.title}
                                    </Typography>
                                  }
                                  placement="top"
                                  sx={{
                                    display: lesson.title.length > maxLengthNavigatorItem ? 'block' : 'none',
                                  }}
                                >
                                  <Typography
                                    sx={{
                                      fontSize: '14px',
                                      fontFamily: 'Graphik Semibold',
                                      color: !(selectedSectionId === section.id && selectedLessonId === lesson.id)
                                        ? theme.palette.mode == 'dark'
                                          ? 'rgb(141, 148, 163)'
                                          : 'rgb(88, 93, 102)'
                                        : theme.palette.text.primary,
                                      lineHeight: '20px',
                                      marginLeft: '20px',
                                    }}
                                  >
                                    {cropString(lesson.title, maxLengthNavigatorItem)}
                                  </Typography>
                                </AppTooltip>
                              </>
                            }
                          />
                        </ListItemButton>
                      )
                    })}
                  </List>
                </Collapse>
              </div>
            ))}
          </List>
        </Box>
      }
    </Box>
  )
}
