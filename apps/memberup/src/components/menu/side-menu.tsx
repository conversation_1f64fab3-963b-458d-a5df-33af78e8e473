import { ExpandLess, ExpandMore } from '@mui/icons-material'
import AddIcon from '@mui/icons-material/Add'
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew'
import AssessmentIcon from '@mui/icons-material/Assessment'
import EventIcon from '@mui/icons-material/Event'
import ExpandLessIcon from '@mui/icons-material/ExpandLess'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import Grid3x3Icon from '@mui/icons-material/Grid3x3'
import LogoutOutlinedIcon from '@mui/icons-material/LogoutOutlined'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import NotificationsOutlinedIcon from '@mui/icons-material/NotificationsOutlined'
import PeopleIcon from '@mui/icons-material/People'
import Box from '@mui/material/Box'
import Collapse from '@mui/material/Collapse'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import List from '@mui/material/List'
import ListItem from '@mui/material/ListItem'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import ListSubheader from '@mui/material/ListSubheader'
import Typography from '@mui/material/Typography'
import useMediaQuery from '@mui/material/useMediaQuery'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import _debounce from 'lodash/debounce'
import { signOut } from 'next-auth/react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import React, { useCallback, useMemo, useRef, useState } from 'react'

import SpaceList from '../feed/space-list.jsx'
import SVGHashtag from '../svgs/hashtag'
import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { hasSpaceLimitBeenReached } from '@memberup/shared/src/libs/membership-settings'
import { RECURRING_INTERVAL_ENUM, SPACE_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { CreditCard24Icon, Lock24Icon, Notifications24Icon, Settings24Icon, User24Icon } from '@/components/icons'
import { CommunitySwitcher } from '@/components/layout'
import AppLogo from '@/memberup/components/common/app-logo'
import RewardfulTickets from '@/memberup/components/dialogs/rewardful-tickets'
import useAppCookie from '@/memberup/components/hooks/use-app-cookie'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import SideMenuLinks from '@/memberup/components/menu/side-menu-links'
import SVGCamcorder from '@/memberup/components/svgs/camcorder'
import SVGClose from '@/memberup/components/svgs/close'
import SVGCreditCard from '@/memberup/components/svgs/credit-card'
import SVGDashboard from '@/memberup/components/svgs/dashboard'
import SVGDifference from '@/memberup/components/svgs/difference'
import SVGFilm from '@/memberup/components/svgs/film'
import SVGHome from '@/memberup/components/svgs/home'
import SVGMembers from '@/memberup/components/svgs/members'
import SVGSettings from '@/memberup/components/svgs/settings'
import SVGSpaces from '@/memberup/components/svgs/spaces'
import SVGSpark from '@/memberup/components/svgs/spark'
import SVGUser from '@/memberup/components/svgs/user'
import { isEditorEmpty } from '@/memberup/libs/mentions'
import { getCommunityBaseURL, getCommunityURL } from '@/memberup/libs/utils'
import { selectCurrentPostDraft } from '@/memberup/store/features/feedSlice'
import { clearLikes } from '@/memberup/store/features/likesSlice'
import { selectMembership, selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import { selectChannels, setActiveChannel } from '@/memberup/store/features/spaceSlice'
import { openDialog } from '@/memberup/store/features/uiSlice'
import { selectUser, selectUserFullName, selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { USER_ROLE_ENUM } from '@/shared-types/enum'

const useStyles = makeStyles((theme) => ({
  root: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    width: 240,
    '& .app-logo-wrapper': {
      cursor: 'pointer',
      padding: '12px 16px 0 16px',
    },
    '& .MuiListSubheader-root': {
      lineHeight: '20px',
      paddingLeft: 14,
      paddingRight: 14,
      marginBottom: 8,
      position: 'relative',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
    '& .MuiListItem-root, & .MuiListItemButton-root': {
      borderRadius: 10,
      padding: 8,
      paddingLeft: 14,
      paddingRight: 14,
      height: 40,
      '&.creator-menu-item': {
        height: 32,
      },
    },
    '& .MuiListItemIcon-root': {
      minWidth: 'auto',
      marginRight: 16,
      height: 16,
      width: 16,
    },
    '& .side-menu-footer-item': {
      position: 'relative',
      display: 'flex',
      alignItems: 'center',
      color: theme.palette.text.disabled,
      flexWrap: 'wrap',
      fontSize: 12,
      padding: 16,
      textAlign: 'center',
      width: 240,
    },
    '& .side-menu-footer-item-add': {
      position: 'absolute',
      backgroundColor: theme.palette.background.default,
      borderRadius: '50%',
      left: 27,
      top: 27,
      width: 15,
      height: 15,
      padding: 1,
      zIndex: 1,
    },
    '& .side-menu-footer-item-add-icon': {
      color: theme.palette.primary.dark,
      fontSize: 12,
    },
    '&.mobile': {
      width: '100%',
    },
  },
  avatar: {
    background: `linear-gradient(225deg, #B9E7FA 0%, #DDD6F3 33%, #FAACA8 100%)`,
    color: '#946CF5',
    fontSize: 16,
    marginRight: 8,
    width: 28,
    height: 28,
  },
  membersWrapper: {
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 14,
    paddingRight: 14,
    display: 'flex',
    flexWrap: 'wrap',
    overflow: 'hidden',
    width: '100%',
    '& .member-image-wrapper': {
      margin: 2,
      cursor: 'pointer',
    },
    '& .all-members-wrapper': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#FFFFFF',
      borderRadius: 14,
      width: 28,
      height: 28,
      margin: 2,
      cursor: 'pointer',
    },
  },
  arrow: {
    position: 'fixed',
    borderRadius: 20,
    bottom: 16,
    paddingTop: 2,
    right: 16,
    width: 36,
    height: 40,
    zIndex: 1,
  },
}))

export const topMenuItems = [
  {
    icon: (
      <ListItemIcon style={{ marginRight: 13 }}>
        <SVGFilm width={16} height={16} styles={{ position: 'relative', top: '1px', left: '1px' }} />
      </ListItemIcon>
    ),
    name: 'Content',
    pathname: '/[slug]/library',
  },
  {
    icon: (
      <ListItemIcon style={{ marginRight: 13 }}>
        <EventIcon style={{ width: 18, height: 18, position: 'relative', top: '-1px' }} />
      </ListItemIcon>
    ),
    name: 'Events',
    pathname: '/[slug]/events',
  },
  {
    icon: (
      <ListItemIcon style={{ marginRight: 13 }}>
        <PeopleIcon style={{ position: 'relative', top: '-4px', maxWidth: '21px' }} />
      </ListItemIcon>
    ),
    name: 'Members',
    pathname: '/[slug]/members',
    regex: '\\/\\[slug\\]\\/members$',
  },
]

const creatorTopMenuItems = [
  {
    icon: <SVGSpark />,
    name: 'Spark',
    pathname: `/settings/spark`,
  },
  {
    icon: <AssessmentIcon style={{ width: 16, height: 16 }} />,
    name: 'Analytics',
    pathname: '/settings/analytics',
  },
]

const adminMenuItems = [
  {
    icon: <SVGSettings styles={{ position: 'relative', top: '-3px', left: '2px' }} />,
    name: 'General',
    menuItems: [
      {
        name: 'Community Settings',
        pathname: '/settings/community-settings',
      },
      {
        name: 'Emails',
        pathname: '/settings/emails',
      },
      {
        name: 'Membership Questions',
        pathname: '/settings/membership-questions',
      },
    ],
  },
  {
    icon: <SVGCreditCard />,
    name: 'Monetization',
    role: USER_ROLE_ENUM.owner,
    menuItems: [
      {
        icon: <SVGCamcorder />,
        name: 'Community Pricing',
        pathname: '/settings/community-pricing',
      },
    ],
  },
  {
    icon: <SVGMembers />,
    name: 'Members',
    menuItems: [
      {
        icon: <SVGHome />,
        name: 'Member Management',
        pathname: '/settings/members',
      },
      {
        icon: <SVGHome />,
        name: 'Member Requests',
        pathname: '/settings/member-requests',
      },
      {
        icon: <SVGHome />,
        name: 'Admin Roles',
        pathname: '/settings/admin-roles',
      },
      {
        icon: <SVGHome />,
        name: 'Invite Members',
        pathname: '/settings/invite-members',
      },
      {
        icon: <SVGCamcorder />,
        name: 'Invite Links',
        pathname: '/settings/invite-links',
      },
      {
        icon: <SVGCamcorder />,
        name: 'Moderation',
        pathname: '/settings/moderation',
      },
    ],
  },
  {
    icon: <SVGSpaces />,
    name: 'Spaces',
    menuItems: [
      {
        icon: <SVGSpaces />,
        name: 'Organize Spaces',
        pathname: '/settings/organize-spaces',
      },
    ],
  },
  {
    icon: <SVGDifference />,
    name: 'Branding',
    menuItems: [
      {
        icon: <SVGFilm />,
        name: 'Themes',
        pathname: '/settings/themes',
      },
      {
        icon: <SVGFilm />,
        name: 'Signup & Login',
        pathname: '/settings/signup-pages',
      },
    ],
  },
  {
    icon: <SVGUser styles={{ position: 'relative', top: '-3px', left: '2px' }} />,
    name: 'Account',
    role: USER_ROLE_ENUM.owner,
    menuItems: [
      {
        icon: <SVGCamcorder />,
        name: 'MemberUp Billing',
        pathname: '/settings/memberup-billing',
      },
      // {
      //   icon: <SVGCamcorder />,
      //   name: 'Upgrade',
      //   pathname: '/settings/upgrade',
      // },
    ],
  },
]

const AccountSettingsMenuList: React.FC<{
  handleClickListItem: (item: string, pathname?: string) => void
}> = ({ handleClickListItem }) => {
  const router = useRouter()
  const { newGradient, isMobile } = useAppTheme()
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const { isCurrentUserAdmin } = useCheckUserRole()

  const items = [
    {
      icon: <User24Icon className="h-4 w-4" />,
      name: 'Profile',
      pathname: '/settings/account/profile',
    },
    {
      icon: <Settings24Icon className="h-4 w-4" />,
      name: 'Account',
      pathname: '/settings/account/account',
    },
    {
      icon: <Lock24Icon className="h-4 w-4" />,
      name: 'Password',
      pathname: '/settings/account/password',
    },
    // {
    //   icon: <UserAdd24Icon className="w-4 h-4" />,
    //   name: 'Referrals',
    //   pathname: '/settings/account/referrals',
    // },
    {
      icon: <Notifications24Icon className="h-4 w-4" />,
      name: 'Notifications',
      pathname: '/settings/account/notifications',
    },
  ]

  if (
    !isCurrentUserAdmin &&
    membershipSetting?.stripe_connect_account &&
    membershipSetting?.stripe_connect_account?.enabled !== false
  ) {
    items.push(
      {
        icon: <CreditCard24Icon className="h-4 w-4" />,
        name: 'Payment Methods',
        pathname: '/settings/account/payment-methods',
      },
      {
        icon: <CreditCard24Icon className="h-4 w-4" />,
        name: 'Payment History',
        pathname: '/settings/account/payment-history',
      },
    )
  }

  return (
    <List dense sx={{ paddingTop: '22px', paddingBottom: '12px', paddingLeft: 2, paddingRight: 2 }}>
      {items.map((cm, index) => {
        const selected = router.pathname === cm.pathname
        return (
          <ListItemButton
            key={`creator-menu-item-${index}`}
            className={clsx('top-menu-item', 'font-family-semibold', 'mb-1 mt-0')}
            selected={selected}
            dense
            sx={{
              height: '36px !important',
              background: !isMobile && selected ? newGradient : undefined,
              '&.Mui-selected:hover': {
                background: !isMobile && selected ? newGradient : undefined,
              },
            }}
            href={cm.pathname}
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              handleClickListItem(cm.name, cm.pathname)
            }}
          >
            {!isMobile && <ListItemIcon sx={{ color: 'inherit' }}>{cm.icon}</ListItemIcon>}
            <ListItemText disableTypography={true} primary={cm.name} />
          </ListItemButton>
        )
      })}
    </List>
  )
}

const SettingsMenuList: React.FC<{
  handleClickListItem: (item: string, pathname?: string) => void
}> = ({ handleClickListItem }) => {
  const router = useRouter()
  const { newGradient, isMobile, isDarkTheme } = useAppTheme()
  const membership = useAppSelector((state) => selectMembership(state))

  const user = useAppSelector((state) => selectUser(state))

  const userMembership = user?.user_memberships.find((um) => um.membership.slug === membership.slug)
  const userCommunityRole = userMembership?.user_role || USER_ROLE_ENUM.member

  const defaultExpandedMenuItem = useMemo(() => {
    return (
      adminMenuItems.find((cm) => cm.menuItems.findIndex((mi) => router.pathname.endsWith(mi.pathname)) >= 0) || null
    )
  }, [router.pathname])
  const [expandedMenuItemName, setExpandedMenuItemName] = useState<string>(defaultExpandedMenuItem?.name || null)
  const menuItemClass = isMobile ? 'text-base text-only' : undefined

  const filteredAdminMenuItems = adminMenuItems.filter((item) => {
    if (item.role) {
      return item.role === userCommunityRole
    }
    return true
  })

  return (
    <>
      <List
        dense
        sx={{ paddingTop: '22px', paddingBottom: '12px', paddingLeft: 2, paddingRight: 2 }}
        subheader={
          <ListSubheader
            className={clsx('font-family-graphik-medium', {
              'text-base font-bold': isMobile,
            })}
            component="div"
            style={{ fontSize: '16px', marginBottom: '24px' }}
          >
            Creator Dashboard
          </ListSubheader>
        }
      >
        {creatorTopMenuItems.map((cm, index) => {
          const selected = router.pathname === `${getCommunityBaseURL(membership)}cm.pathname`
          const url = getCommunityBaseURL(membership) + cm.pathname
          return (
            <ListItemButton
              key={`creator-menu-item-${index}`}
              className={clsx('top-menu-item', 'font-family-semibold', 'mb-1 mt-0')}
              selected={selected}
              dense
              sx={{
                height: '36px !important',
                color: isDarkTheme ? '#8D94A3' : '#585D66',
                background: !isMobile && selected ? newGradient : undefined,
                '&.Mui-selected:hover': {
                  background: !isMobile && selected ? newGradient : undefined,
                },
              }}
              href={url}
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                setExpandedMenuItemName(null)
                handleClickListItem(cm.name, url)
              }}
            >
              {!isMobile && <ListItemIcon sx={{ color: 'inherit' }}>{cm.icon}</ListItemIcon>}
              <ListItemText disableTypography={true} primary={cm.name} />
            </ListItemButton>
          )
        })}
      </List>

      <Divider sx={{ marginBottom: 8 }} />

      <List
        dense
        sx={{
          paddingTop: 3,
          paddingBottom: 3,
          paddingLeft: 2,
          paddingRight: 2,
          '& .MuiSvgIcon-root': {
            maxWidth: '21px',
          },
        }}
      >
        {filteredAdminMenuItems.map((cm) => {
          const isExpanded = expandedMenuItemName === cm.name
          return (
            <React.Fragment key={`menu-item-${cm.name}`}>
              <ListItemButton
                dense
                className="top-menu-item mb-1 mt-0"
                sx={{
                  color: isDarkTheme ? '#8D94A3' : '#585D66',
                }}
                onClick={() => {
                  if (expandedMenuItemName === cm.name) {
                    setExpandedMenuItemName(null)
                  } else {
                    setExpandedMenuItemName(cm.name)
                  }
                }}
              >
                <ListItemIcon
                  sx={{
                    color: 'inherit',
                  }}
                >
                  {cm.icon}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography className="font-family-graphik-semibold text-sm" variant="body1" color="inherit">
                      {cm.name}
                    </Typography>
                  }
                />
                {isExpanded ? <ExpandLessIcon className="color03" /> : <ExpandMoreIcon className="color03" />}
              </ListItemButton>
              <Collapse in={isExpanded}>
                <List component="div" disablePadding sx={{ marginLeft: '12px' }}>
                  {cm.menuItems.map((mi, j) => {
                    const selected = router.pathname.endsWith(mi.pathname)
                    return (
                      <ListItemButton
                        key={`creator-member-menu-item-${j}`}
                        className={clsx('creator-menu-item', 'font-family-semibold', 'border-color07', 'mb-1 mt-0')}
                        selected={selected}
                        dense
                        sx={{
                          background: !isMobile && selected ? newGradient : undefined,
                          color: isDarkTheme ? '#8D94A3' : '#585D66',
                        }}
                        href={getCommunityBaseURL(membership) + mi.pathname}
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          handleClickListItem('Creator', getCommunityBaseURL(membership) + mi.pathname)
                        }}
                      >
                        <ListItemText disableTypography={true} primary={mi.name} />
                      </ListItemButton>
                    )
                  })}
                </List>
              </Collapse>
            </React.Fragment>
          )
        })}
      </List>
    </>
  )
}

const MemberMenuList: React.FC<{
  handleClickListItem: (item: string, pathname?: string) => void
  onCloseSideMenu: () => void
}> = ({ handleClickListItem, onCloseSideMenu }) => {
  const classes = useStyles()
  const router = useRouter()
  const { theme, secondaryBackgroundColor, newGradient, isMobile } = useAppTheme()
  const dispatch = useAppDispatch()
  //const membership = useAppSelector((state) => selectMembership(state))
  const spaces = useAppSelector((state) => selectChannels(state))
  const members = useAppSelector((state) => selectMembersMap(state))
  const menuItemClass = isMobile ? 'text-base font-bold text-only' : undefined
  const homeMenuItemClass = isMobile ? 'text-base font-bold text-only' : undefined
  const { isCurrentUserAdmin } = useCheckUserRole()
  const currentPostDraft = useAppSelector((state) => selectCurrentPostDraft(state))
  const membership = useAppSelector((state) => selectMembership(state))

  const isMemberOfCommunity = false

  let updatedTopMenuItems = topMenuItems

  const communityCardHidden = useMediaQuery(theme.breakpoints.between('sm', 'lg'))

  if (communityCardHidden) {
    updatedTopMenuItems = [
      ...topMenuItems,
      {
        icon: (
          <ListItemIcon style={{ marginRight: 13 }}>
            <Settings24Icon className="-mt-1" />
          </ListItemIcon>
        ),
        name: 'Settings',
        pathname: '/settings/account/profile/',
      },
    ]
  }

  const renderTopLevel = useMemo(() => {
    const topLevelSpaces = (
      spaces?.docs?.filter(
        (c: any) => c.active && c.space_type === SPACE_TYPE_ENUM.space && c.top_level && c.visibility,
      ) || []
    ).sort((a: any, b: any) => a.sequence - b.sequence)

    return (
      <List
        dense
        className="member-menu-list px-2"
        sx={{
          paddingTop: '0px',
          paddingBottom: !isMobile ? 4 : 0,
        }}
        aria-label="top menu list"
      >
        {updatedTopMenuItems.map((cm, index) => {
          let selected = router.pathname === cm.pathname
          if (cm.regex) {
            selected = Boolean(router.pathname.match(cm.regex))
          }
          return (
            <ListItemButton
              key={`menu-item-${index}`}
              className={clsx(homeMenuItemClass, 'top-menu-item mb-1 mt-0')}
              selected={selected}
              dense
              sx={{
                height: '36px !important',
                background: !isMobile && selected ? newGradient : undefined,
                '&.Mui-selected:hover': {
                  background: !isMobile && selected ? newGradient : undefined,
                },
              }}
              href={`${getCommunityURL(membership, cm.pathname)}`}
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                handleClickListItem(cm.name, `${getCommunityURL(membership, cm.pathname)}`)
              }}
            >
              {!isMobile && cm.icon}
              <ListItemText
                primary={
                  <Typography
                    sx={{
                      fontFamily: isMobile ? 'Graphik Semibold' : 'Graphik SemiBold',
                      fontSize: isMobile ? '20px !important' : '14px',
                      fontWeight: 500,
                      background: isMobile && selected ? secondaryBackgroundColor : 'initial',
                      WebkitBackgroundClip: isMobile && selected ? 'text' : 'initial',
                      WebkitTextFillColor: isMobile && selected ? 'transparent' : 'initial',
                    }}
                  >
                    {cm.name}
                  </Typography>
                }
              />
            </ListItemButton>
          )
        })}

        {topLevelSpaces.map((item, index) => {
          const selected = router.query?.slug && item.slug === (router.query.slug as string)
          return (
            <ListItemButton
              key={item.id}
              className={clsx(homeMenuItemClass, 'top-menu-item')}
              selected={selected}
              sx={{
                height: '36px !important',
                background: !isMobile && selected ? newGradient : undefined,
                '&.Mui-selected:hover': {
                  background: !isMobile && selected ? newGradient : undefined,
                },
              }}
              dense
              onClick={() => {
                dispatch(setActiveChannel(item))
                onCloseSideMenu()
              }}
            >
              {!isMobile && (
                <ListItemIcon>
                  <Grid3x3Icon />
                </ListItemIcon>
              )}
              <ListItemText className="text-ellipsis" disableTypography={true} primary={item.name} />
            </ListItemButton>
          )
        })}
      </List>
    )
  }, [isMobile, spaces, router, homeMenuItemClass, handleClickListItem, dispatch])

  const communityURL = getCommunityBaseURL(membership)

  const renderSpaces = useMemo(() => {
    const dispSpaces =
      spaces?.docs?.filter((c) => c.active && c.space_type === SPACE_TYPE_ENUM.space && c.visibility) || []

    return (
      <>
        {dispSpaces.length > 0 && (
          <>
            {isCurrentUserAdmin && !isMobile && (
              <>
                <IconButton
                  className="no-padding background-color15"
                  sx={{
                    display: { xs: 'block', sm: 'none', md: 'block' },
                    padding: '3px',
                    position: 'absolute',
                    top: '13px',
                    right: '34px',
                    width: '24px',
                    height: '24px',
                    zIndex: 1,
                  }}
                  size="small"
                  aria-label="add space"
                  onClick={() => {
                    handleClickListItem('Edit Space', '')
                  }}
                >
                  <AddIcon fontSize="small" />
                </IconButton>
                <IconButton
                  className="organize-spaces-button no-padding"
                  sx={{
                    display: { xs: 'block', sm: 'none', md: 'block' },
                    backgroundColor: 'none',
                    padding: '3px',
                    position: 'absolute',
                    top: '13px',
                    right: '8px',
                    width: '24px',
                    height: '24px',
                    zIndex: 1,
                  }}
                  size="small"
                  aria-label="more space"
                  onClick={() => handleClickListItem('', `/settings/organize-spaces`)}
                >
                  <MoreHorizIcon fontSize="small" />
                </IconButton>
              </>
            )}

            {dispSpaces.map((item, index) => {
              const selected = router.query?.slug && item.slug === (router.query.slug as string)
              return (
                <ListItemButton
                  key={item.id}
                  className={clsx(menuItemClass, 'border-color07')}
                  selected={selected}
                  dense
                  sx={{
                    margin: isMobile ? '5px 0px 5px 35px' : 'inherit',
                    height: '36px !important',
                    background: !isMobile && selected ? newGradient : undefined,
                    '&.Mui-selected:hover': {
                      background: !isMobile && selected ? newGradient : undefined,
                    },
                  }}
                  href={`${communityURL}/space/${item.slug}`}
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    const pathname = `/${membership.slug}/space/${item.slug}`
                    const shouldWarnUserAboutEditing =
                      !isEditorEmpty(currentPostDraft?.editorState) || currentPostDraft?.title

                    if (shouldWarnUserAboutEditing) {
                      dispatch(
                        openDialog({
                          dialog: 'WarningEditingContent',
                          open: true,
                          props: { pathname },
                        }),
                      )

                      return
                    } else {
                      if (router.pathname !== pathname) {
                        router.push({ pathname }, undefined, { shallow: false })
                      }
                    }
                    dispatch(setActiveChannel(item))
                    onCloseSideMenu()
                  }}
                  data-cy="space-menu-item"
                  data-space={item.id}
                >
                  <SVGHashtag />
                  &nbsp;&nbsp;
                  <ListItemText className="text-ellipsis" disableTypography={true} primary={`${item.name}`} />
                </ListItemButton>
              )
            })}
          </>
        )}
      </>
    )
  }, [isMobile, spaces, router, menuItemClass, handleClickListItem, dispatch, isCurrentUserAdmin])

  return (
    <>
      <SpaceList
        isAdminOrCreator={isCurrentUserAdmin}
        isMobile={isMobile}
        router={router}
        menuItemClass={menuItemClass}
        onListItemClick={handleClickListItem}
      />
      {renderTopLevel}
    </>
  )
}

const SideMenu: React.FC<{ onCloseSideMenu: () => void }> = ({ onCloseSideMenu }) => {
  const classes = useStyles()
  const { theme, newGradient } = useAppTheme()
  const router = useRouter()
  const dispatch = useAppDispatch()
  const user = useAppSelector((state) => selectUser(state))
  const userProfile = useAppSelector((state) => selectUserProfile(state))
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const membership = useAppSelector((state) => selectMembership(state))
  const userFullName = useAppSelector((state) => selectUserFullName(state))
  const listRef = useRef(null)
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  const [isArrowUp, setIsArrowUp] = useState(false)
  const [isOpenRewardfulTickets, setIsOpenRewardfulTickets] = useState(false)
  const isCreatorRoute = useMemo(() => router.pathname.includes('/settings'), [router.pathname])
  const isAccountSettingsRoute = router.pathname.startsWith('/settings/account')
  const { isCurrentUserAdmin } = useCheckUserRole()
  const { deleteAppCookie } = useAppCookie()
  const homeMenuItemClass = isMobile ? 'text-base text-only' : undefined
  const nextRouteRef = useRef<string | null>(null)
  const currentPostDraft = useAppSelector((state) => selectCurrentPostDraft(state))
  const [openUpgradeDialog, setOpenUpgradeDialog] = useState(false)
  const spaces = useAppSelector((state) => selectChannels(state))

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleScroll = useCallback(
    _debounce(() => {
      setIsArrowUp(listRef.current?.scrollHeight - listRef.current?.offsetHeight - listRef.current?.scrollTop < 50)
    }, 300),
    [],
  )

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleClickListItem = (item: string, pathname?: string) => {
    if (pathname) {
      const shouldWarnUserAboutEditing = !isEditorEmpty(currentPostDraft?.editorState) || currentPostDraft?.title
      if (shouldWarnUserAboutEditing) {
        dispatch(openDialog({ dialog: 'WarningEditingContent', open: true, props: { pathname } }))
      } else {
        if (router.pathname !== pathname) {
          router.push({ pathname }, undefined, { shallow: false })
        }
      }
    } else {
      switch (item) {
        case 'Organize Spaces':
          router.push(`${getCommunityBaseURL(membership)}/settings/organize-spaces`)
          break
        case 'Edit Space':
          if (hasSpaceLimitBeenReached(membershipSetting.plan, spaces.docs.length)) {
            dispatch(
              openDialog({
                dialog: 'UpgradePlanSpaceLimit',
                open: true,
                props: { recurringInterval: RECURRING_INTERVAL_ENUM.year },
              }),
            )
            return
          }
          dispatch(openDialog({ dialog: 'EditSpace', open: true, props: {} }))
          break
        case 'View Profile':
          router.push(`/@${user.username}`)
          break
        case 'Settings':
          router.push('/settings/account/profile')
          break
        case 'Search':
          dispatch(openDialog({ dialog: 'Search', open: true, props: {} }))
          break
        case 'Log Out':
          localStorage.setItem('signout', 'true')
          dispatch(clearLikes())
          deleteAppCookie()
          signOut({ redirect: false })
          break
      }
    }
    onCloseSideMenu()
  }

  const renderBottomSection = useMemo(() => {
    if (isCreatorRoute) {
      return (
        <Box className="flex-item">
          <Divider />
          <List dense className="px-2 pt-2">
            <ListItemButton
              key={`menu-item-community`}
              className="color04 text-base"
              onClick={() => handleClickListItem('Community', '/community')}
            >
              <ListItemText
                className="back-to-community-button inline items-center"
                primary={
                  <div className="flex flex-nowrap items-center">
                    <ArrowBackIosNewIcon className="mr-1" style={{ color: 'inherit', width: 10, height: 10 }} />
                    <Typography style={{ color: 'inherit', fontSize: 14 }}>Back to Community</Typography>
                  </div>
                }
              ></ListItemText>
            </ListItemButton>
          </List>
        </Box>
      )
    }
    const excludedMemberships = process.env.NEXT_PUBLIC_EXCLUDED_MEMBERSHIPS_BRANDING
      ? process.env.NEXT_PUBLIC_EXCLUDED_MEMBERSHIPS_BRANDING.split(',')
      : []
    return (
      <>
        {!(excludedMemberships.length && excludedMemberships.includes(membership.id)) && (
          <Box className="flex-item">
            <Divider />
            <div className="side-menu-footer-item justify-center text-center">
              <span className="text-[#8D94A3]">Powered by</span>&nbsp;&nbsp;
              <Link
                href={`https://memberup.com?utm_campaign=community&utm_medium=community_referral&utm_source=${membership.slug}`}
                target="_blank"
              >
                <AppImg src={`/assets/default/logos/memberup-logo.png`} width={96} height={14} alt="MemberUp Logo" />
              </Link>
            </div>
          </Box>
        )}
      </>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isCreatorRoute, membershipSetting])

  const isGettingStartedRoute =
    router.pathname === '/getting-started' || router.pathname === '/settings/getting-started'

  const gettingStartedLink =
    router.pathname.indexOf('/settings') >= 0 ? '/settings/getting-started' : '/getting-started'

  const [openAccount, setOpenAccount] = useState(false)

  const handleClickAccount = () => {
    setOpenAccount(!openAccount)
  }

  const openIntercom = () => {
    const event = new CustomEvent('openIntercom')
    window.dispatchEvent(event)
  }

  const handleItemClick = (linkItem: any) => {
    switch (linkItem.id) {
      case 'help-and-support':
        openIntercom()
        break
      case 'affiliate-program':
        setIsOpenRewardfulTickets(true)
        break
    }
  }
  return (
    <Box id="side-menu" className={clsx(classes.root, { mobile: isMobile })}>
      <Box className={clsx('side-menu-logo app-logo-wrapper flex-item', isMobile ? 'pt-0' : 'page-inner-pt')}>
        <Grid className="side-menu-logo-container" container alignItems="center">
          <Grid item xs>
            <Box
              className="mb-4 mt-2"
              sx={{
                display: 'block',
                height: 52,
                maxWidth: '178px',
                textAlign: 'left',
              }}
            >
              <CommunitySwitcher />
              <Link href={`${getCommunityBaseURL(membership)}`}>
                <AppLogo style={{ fontSize: '20px' }} />
              </Link>
            </Box>
          </Grid>
          <Grid item sx={{ display: { xs: 'block', sm: 'none' } }}>
            <IconButton size="small" aria-label="close" onClick={onCloseSideMenu}>
              <SVGClose fontSize={14} />
            </IconButton>
          </Grid>
        </Grid>
      </Box>

      <Box
        className="flex-item-auto pr-0"
        ref={listRef}
        sx={{ marginTop: '20px', overflowX: 'hidden', overflowY: 'auto' }}
        onScroll={() => handleScroll()}
      >
        {isAccountSettingsRoute && !isMobile && <AccountSettingsMenuList handleClickListItem={handleClickListItem} />}
        {(isMobile || !isAccountSettingsRoute) &&
          (isCreatorRoute ? (
            <SettingsMenuList handleClickListItem={handleClickListItem} />
          ) : (
            <MemberMenuList handleClickListItem={handleClickListItem} onCloseSideMenu={onCloseSideMenu} />
          ))}
        {isMobile && !isCreatorRoute && (
          <>
            <ListItemButton key={'menu-item-account'} sx={{ marginX: 2 }} onClick={handleClickAccount}>
              <ListItemText>
                <Typography
                  sx={{
                    fontWeight: '700',
                    fontFamily: 'Graphik Semibold !important',
                    padding: '8px 0px',
                    fontSize: '20px !important',
                  }}
                >
                  Account
                </Typography>
              </ListItemText>
              {openAccount ? <ExpandLess /> : <ExpandMore />}
            </ListItemButton>
            <Collapse in={openAccount} timeout="auto" unmountOnExit>
              <List dense sx={{ borderRadius: 3, marginX: 3 }} aria-labelledby="profile-subheader">
                <ListItem
                  className="background-color02 text-base"
                  onClick={() => handleClickListItem('View Profile')}
                  sx={{ padding: '35px 15px 35px 15px !important' }}
                >
                  <Grid container alignItems="center" spacing={3}>
                    <Grid item sx={{ paddingTop: '8px !important' }}>
                      <ListItemIcon style={{ marginRight: 8 }}>
                        <AppProfileImage
                          imageUrl={userProfile?.image || user?.image}
                          cropArea={userProfile?.image_crop_area || user?.image_crop_area}
                          name={user?.first_name || user?.image}
                        />
                      </ListItemIcon>
                    </Grid>
                    <Grid item xs>
                      <ListItemText
                        className="text-ellipsis"
                        primary={userFullName}
                        secondary={
                          <Typography color="text.disabled" variant="caption" className="text-xs">
                            View my profile
                          </Typography>
                        }
                      />
                    </Grid>
                  </Grid>
                </ListItem>
                <Divider />
                {isCurrentUserAdmin && (
                  <ListItem
                    className="text-only"
                    onClick={() =>
                      handleClickListItem('Dashboard', `${getCommunityBaseURL(membership)}/settings/analytics`)
                    }
                  >
                    <ListItemIcon className="color03">
                      <SVGDashboard />
                    </ListItemIcon>
                    <ListItemText disableTypography={true} primary="Creator Dashboard" />
                  </ListItem>
                )}
                <ListItem className="text-only" onClick={() => handleClickListItem('Settings')}>
                  <ListItemIcon className="color03">
                    <SVGUser />
                  </ListItemIcon>
                  <ListItemText disableTypography={true} primary="Settings" />
                </ListItem>
                <ListItem className="text-only" onClick={() => handleClickListItem('Notifications', '/notifications')}>
                  <ListItemIcon>
                    <NotificationsOutlinedIcon
                      sx={{ top: '-3px', left: '-3px', position: 'relative' }}
                      className="color03"
                    />
                  </ListItemIcon>
                  <ListItemText disableTypography={true} primary="Notifications" />
                </ListItem>
                <ListItem className="text-only" onClick={() => handleClickListItem('Log Out')}>
                  <ListItemIcon>
                    <LogoutOutlinedIcon sx={{ top: '-2px', position: 'relative' }} className="color03" />
                  </ListItemIcon>
                  <ListItemText disableTypography={true} primary="Log Out" />
                </ListItem>
              </List>
            </Collapse>
          </>
        )}
      </Box>
      {!isAccountSettingsRoute && !isCreatorRoute && isCurrentUserAdmin && !isMobile && (
        <Box className="px-4 pb-4" sx={{ minHeight: 64 }}>
          <SideMenuLinks handleItemClick={handleItemClick} />
          {isOpenRewardfulTickets && (
            <RewardfulTickets
              open={isOpenRewardfulTickets}
              onClose={() => {
                setIsOpenRewardfulTickets(false)
              }}
            />
          )}
        </Box>
      )}
      {renderBottomSection}
    </Box>
  )
}

export default React.memo(SideMenu)
