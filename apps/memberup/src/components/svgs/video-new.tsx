import React from 'react'

const SVGVideoNew: React.FC<{ width?: number; height?: number }> = ({ width, height }) => {
  return (
    <svg width={width || 16} height={height || 16} viewBox="0 0 16 16" version="1.1">
      <g id="🏠-Home-Feed---Create" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g
          id="🌑-Create-Post---Create---Title+Description---Empty"
          transform="translate(-520, -535)"
          fill="currentColor"
          fillRule="nonzero"
        >
          <g id="Group-26" transform="translate(396, 231)">
            <g id="Group-2" transform="translate(0, 276)">
              <g id="media" transform="translate(16, 16)">
                <g id="Group-28" transform="translate(96, 0)">
                  <g id="ic20-play-circle" transform="translate(12, 12)">
                    <path
                      d="M8,0 C12.418278,0 16,3.581722 16,8 C16,12.418278 12.418278,16 8,16 C3.581722,16 0,12.418278 0,8 C0,3.581722 3.581722,0 8,0 Z M8,2 C4.6862915,2 2,4.6862915 2,8 C2,11.3137085 4.6862915,14 8,14 C11.3137085,14 14,11.3137085 14,8 C14,4.6862915 11.3137085,2 8,2 Z M7.50387103,4.1362211 L11.503871,7.1362211 C12.1264642,7.49940048 12.1630874,8.3674256 11.6137404,8.7899986 L7.50387103,11.8637789 C6.83721439,12.2526619 6,11.7717908 6,11 L6,5 C6,4.22820917 6.83721439,3.74733806 7.50387103,4.1362211 Z"
                      id="Shape"
                    ></path>
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGVideoNew
