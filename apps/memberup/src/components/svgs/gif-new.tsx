import React from 'react'

const SVGGifNew: React.FC<{ width?: number; height?: number }> = ({ width, height }) => {
  return (
    <svg width={width || 20} height={height || 16} viewBox="0 0 20 16" version="1.1">
      <g id="🏠-Home-Feed---Create" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g
          id="🌑-Create-Post---Create---Title+Description---Empty"
          transform="translate(-614, -535)"
          fill="currentColor"
        >
          <g id="Group-26" transform="translate(396, 231)">
            <g id="Group-2" transform="translate(0, 276)">
              <g id="media" transform="translate(16, 16)">
                <g id="Group-30" transform="translate(192, 0)">
                  <path
                    d="M26.9125,12 C28.6176791,12 30,13.3692744 30,15.05836 L30,24.94164 C30,26.6307256 28.6176791,28 26.9125,28 L13.0875,28 C11.3823208,28 10,26.6307256 10,24.94164 L10,15.05836 C10,13.3692744 11.3823208,12 13.0875,12 L26.9125,12 Z M20.4908016,16.000105 C20.0310624,16.000105 19.6583701,16.3689544 19.6583701,16.8239532 L19.6583701,23.1761519 C19.6583701,23.6311506 20.0310624,24 20.4908016,24 C20.9505409,24 21.3232332,23.6311506 21.3232332,23.1761519 L21.3232332,16.8239532 C21.3232332,16.3689544 20.9505409,16.000105 20.4908016,16.000105 Z M14.6637809,16.0000256 C13.9600196,15.9969523 13.2838558,16.2707977 12.7842217,16.7613241 C12.2845877,17.2518504 12.0024671,17.918821 12,18.6153285 L12,21.3834583 C12.0024671,22.0799658 12.2845877,22.7469365 12.7842217,23.2374628 C13.2838558,23.7279891 13.9600196,24.0018346 14.6637809,23.9987613 C16.1266643,23.9987613 17.3158587,22.8312113 17.3275691,21.3834583 L17.3275691,20.9431939 C17.3275691,20.3971953 16.8803311,19.9545761 16.328644,19.9545761 L15.3297261,19.9545761 C14.9619347,19.9545761 14.6637809,20.2496556 14.6637809,20.6136546 C14.6637809,20.9776536 14.9619347,21.2727331 15.3297261,21.2727331 L15.6626987,21.2727331 L15.6626987,21.3834583 C15.6456131,21.9190457 15.2051831,22.3462166 14.6637809,22.3523037 C14.3979746,22.3548051 14.1423572,22.2512261 13.954646,22.0649544 C13.7669349,21.8786827 13.6629587,21.6254257 13.666195,21.3623678 L13.666195,18.6364191 C13.6633195,18.3735884 13.7674551,18.1206738 13.9551265,17.9346896 C14.1427978,17.7487053 14.3982083,17.6453062 14.6637809,17.6478013 C15.0338233,17.6589565 15.3698242,17.8644057 15.5454924,18.1869275 C15.6834856,18.4482949 15.9518871,18.6169489 16.2495928,18.6293592 C16.5472984,18.6417695 16.8290796,18.4960507 16.988792,18.2470936 C17.1485044,17.9981366 17.161884,17.6837638 17.0238908,17.4223964 C16.5616267,16.5545544 15.6550157,16.0081964 14.6637809,16.0000256 Z M27.1675685,16.000105 L24.486473,16.000105 C24.0267337,16.000105 23.6540415,16.3689544 23.6540415,16.8239532 L23.6540415,23.1682429 C23.6540415,23.6232417 24.0267337,23.9920911 24.486473,23.9920911 C24.9462122,23.9920911 25.3189045,23.6232417 25.3189045,23.1682429 L25.3189045,20.7955603 L26.9691168,20.7955603 C27.428856,20.7955603 27.8015483,20.4267109 27.8015483,19.9717121 C27.8015483,19.5167134 27.428856,19.147864 26.9691168,19.147864 L25.3189045,19.147864 L25.3189045,17.6478013 L27.1675685,17.6478013 C27.6273077,17.6478013 28,17.2789519 28,16.8239532 C28,16.3689544 27.6273077,16.000105 27.1675685,16.000105 Z"
                    id="Combined-Shape"
                  ></path>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGGifNew
