import React from 'react'

const SVGFavorite: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 23} height={height || 20} viewBox="0 0 23 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-393.000000, -1835.000000)">
          <g transform="translate(369.000000, 1215.000000)">
            <g transform="translate(24.000000, 618.000000)">
              <g transform="translate(0.000000, 2.000000)">
                <path
                  d="M21.1753998,1.95725997 C19.9847946,0.695125705 18.3511124,0 16.5749435,0 C15.2472916,0 14.0314178,0.410249374 12.9610138,1.21925701 C12.4208984,1.62761978 11.9314957,2.12722534 11.5,2.71035572 C11.0686798,2.12739684 10.5791016,1.62761978 10.0388107,1.21925701 C8.96858218,0.410249374 7.75270842,0 6.42505647,0 C4.64888762,0 3.01502993,0.695125705 1.82442473,1.95725997 C0.648033131,3.20464445 0,4.90875724 0,6.75590849 C0,8.65708503 0.724891674,10.3973862 2.28118896,12.2328748 C3.67341616,13.8747299 5.6743698,15.5414537 7.99153139,17.4714438 C8.782753,18.1305526 9.67961118,18.8776455 10.6108627,19.673447 C10.8568802,19.88406 11.1725617,20 11.5,20 C11.8272629,20 12.1431198,19.88406 12.3887863,19.67379 C13.3200378,18.877817 14.2174225,18.1303811 15.008995,17.4709292 C17.3258057,15.5412822 19.3267593,13.8747299 20.7189865,12.2327033 C22.2752838,10.3973862 23,8.65708503 23,6.755737 C23,4.90875724 22.3519669,3.20464445 21.1753998,1.95725997 Z"
                  fill="currentColor"
                  fillRule="nonzero"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGFavorite
