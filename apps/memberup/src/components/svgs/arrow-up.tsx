import React from 'react'

const SVGArrowUp: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={'10px' || width} height={'16px' || height} viewBox="0 0 10 16" style={{ ...styles }}>
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/20px/arrow-up" transform="translate(-5, -2)" fill="currentColor">
          <path
            d="M10,18 C9.44771525,18 9,17.5522847 9,17 L9,5.415 L6.70710678,7.70710678 C6.34662282,8.06759074 5.77939176,8.09532028 5.38710056,7.79029539 L5.29289322,7.70710678 C4.93240926,7.34662282 4.90467972,6.77939176 5.20970461,6.38710056 L5.29289322,6.29289322 L9.29289322,2.29289322 L9.336853,2.2514958 L9.336853,2.2514958 L9.40469339,2.19633458 L9.40469339,2.19633458 L9.5159379,2.12467117 L9.5159379,2.12467117 L9.62866398,2.07122549 L9.62866398,2.07122549 L9.73400703,2.03584514 L9.73400703,2.03584514 L9.88252582,2.00683422 L9.88252582,2.00683422 L10,2 L10.0752385,2.00278786 L10.0752385,2.00278786 L10.2007258,2.02024007 L10.2007258,2.02024007 L10.3121425,2.04973809 L10.3121425,2.04973809 L10.4232215,2.09367336 L10.4232215,2.09367336 L10.5207088,2.14599545 L10.5207088,2.14599545 L10.6167501,2.21278596 C10.6484659,2.23766884 10.6786405,2.26442691 10.7071068,2.29289322 L14.7071068,6.29289322 C15.0976311,6.68341751 15.0976311,7.31658249 14.7071068,7.70710678 C14.3466228,8.06759074 13.7793918,8.09532028 13.3871006,7.79029539 L13.2928932,7.70710678 L11,5.415 L11,17 C11,17.5522847 10.5522847,18 10,18 Z"
            id="Path"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGArrowUp
