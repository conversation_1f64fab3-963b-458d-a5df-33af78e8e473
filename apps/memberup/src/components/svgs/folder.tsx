import React from 'react'

const SVGFolder: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={width || '12px'} height={height || '10px'} style={{ ...styles }} viewBox="0 0 12 10" version="1.1">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/16px/folder" transform="translate(-2, -3)" fill="currentColor">
          <path
            d="M5.90609126,3 L3,3 C2.44771525,3 2,3.44771525 2,4 L2,12 C2,12.5522847 2.44771525,13 3,13 L13,13 C13.5522847,13 14,12.5522847 14,12 L14,6 C14,5.44771525 13.5522847,5 13,5 L8,5 C7.69795487,5 7.4120927,4.86347917 7.22223913,4.62856028 L5.90609126,3 Z"
            id="Path"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGFolder
