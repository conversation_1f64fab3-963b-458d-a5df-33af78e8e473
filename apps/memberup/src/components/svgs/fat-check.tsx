import React from 'react'

const SVGFatCheck: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={width || '10px'} height={height || '8px'} viewBox="0 0 10 8" style={{ ...styles }} version="1.1">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/12px/check" transform="translate(-1, -2)" fill="currentColor">
          <path
            d="M2.70710678,6.29290182 C2.31658249,5.90237753 1.68341751,5.90237753 1.29289322,6.29290182 C0.902368928,6.68342611 0.902368928,7.3165911 1.29289322,7.7071154 L3.29289322,9.7071154 C3.69801827,10.1122404 4.36002195,10.0948313 4.74329415,9.6689733 L10.7389221,3.67303458 C11.1083812,3.26252456 11.0751028,2.63023472 10.6645927,2.2607757 C10.2540827,1.89131668 9.62179292,1.9245951 9.25233392,2.33510512 L3.96180516,7.5476002 L2.70710678,6.29290182 Z"
            id="Path"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGFatCheck
