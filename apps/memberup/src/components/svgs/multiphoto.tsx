import React from 'react'

const <PERSON><PERSON>ultiPhoto: React.FC<{ width?: number; height?: number }> = ({ width, height }) => {
  return (
    <svg width={width || '14px'} height={height || '14px'} viewBox="0 0 14 14" version="1.1">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/16px/photos" transform="translate(-1, -1)" fill="currentColor">
          <path
            d="M9,4 C10.6568542,4 12,5.34314575 12,7 L12,12 C12,13.6568542 10.6568542,15 9,15 L4,15 C2.34314575,15 1,13.6568542 1,12 L1,7 C1,5.34314575 2.34314575,4 4,4 L9,4 Z M9,6 L4,6 C3.44771525,6 3,6.44771525 3,7 L3,12.697 L5,11 L8,13 L10,11 L10,7 C10,6.44771525 9.5522847,6 9,6 Z M5.5,7 C6.32842712,7 7,7.67157288 7,8.5 C7,9.32842712 6.32842712,10 5.5,10 C4.67157288,10 4,9.32842712 4,8.5 C4,7.67157288 4.67157288,7 5.5,7 Z M7,1 L10.5008426,1.00294657 C12.9831653,1.00503588 14.9949641,3.01683468 14.9970534,5.49915744 L15,9 L14.9932723,9.11662113 C14.9399506,9.57570299 14.575703,9.93995063 14.1166211,9.99327227 L14,10 L13.8833789,9.99327227 C13.424297,9.93995063 13.0600494,9.57570299 13.0067277,9.11662113 L13,9 L13,6 C13,4.34314575 11.6568542,3 10,3 L7,3 L6.88337887,2.99327227 C6.38604019,2.93550716 6,2.51283584 6,2 C6,1.48716416 6.38604019,1.06449284 6.88337887,1.00672773 L7,1 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGMultiPhoto
