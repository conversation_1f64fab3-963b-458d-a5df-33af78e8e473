import React from 'react'

const SVGM<PERSON>berUser: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={width || '20px'} height={height || '20px'} viewBox="0 0 20 20" style={{ ...styles }} version="1.1">
      <g id="Icons/system/20px/users" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <path
          d="M7.5,10 C8.8739019,10 10.1301441,10.5129203 11.0940916,11.3609507 C11.6809009,11.1284735 12.324822,11 13,11 C15.7614237,11 18,13.1490332 18,15.8 C18,16.4627417 17.4403559,17 16.75,17 L3.375,17 C2.61560847,17 2,16.3731986 2,15.6 C2,12.5072054 4.46243388,10 7.5,10 Z M7,3 C8.65685425,3 10,4.34314575 10,6 C10,7.65685425 8.65685425,9 7,9 C5.34314575,9 4,7.65685425 4,6 C4,4.34314575 5.34314575,3 7,3 Z M14,5 C15.1045695,5 16,5.8954305 16,7 C16,8.1045695 15.1045695,9 14,9 C12.8954305,9 12,8.1045695 12,7 C12,5.8954305 12.8954305,5 14,5 Z"
          id="Shape"
          fill="currentColor"
        ></path>
      </g>
    </svg>
  )
}

export default SVGMemberUser
