import React from 'react'

const SVGLock: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 48} height={height || 48} viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-696.000000, -318.000000)" fill="currentColor">
          <g transform="translate(520.000000, 244.000000)">
            <g transform="translate(176.000000, 74.000000)">
              <path d="M26,20 C26,21.1045695 25.1045695,22 24,22 C22.8954305,22 22,21.1045695 22,20 C22,18.8954305 22.8954305,18 24,18 C25.1045695,18 26,18.8954305 26,20 Z M48,24 C48,37.254834 37.254834,48 24,48 C10.745166,48 0,37.254834 0,24 C0,10.745166 10.745166,0 24,0 C37.2488939,0.0143296952 47.9856703,10.7511061 48,24 Z M30.0000522,20 C30.0126434,16.9455842 27.7286394,14.3691629 24.6947393,14.0155156 C21.6608392,13.6618682 18.8454802,15.643881 18.1552528,18.6193141 C17.4650253,21.5947472 19.1202537,24.6138265 22,25.632 L22,32 C22,33.1045695 22.8954305,34 24,34 C25.1045695,34 26,33.1045695 26,32 L26,25.632 C28.3887017,24.7874909 29.9894972,22.5335709 30.0000522,20 L30.0000522,20 Z"></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGLock
