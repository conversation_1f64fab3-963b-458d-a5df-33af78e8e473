export default function SVGClock2({ width, height }: { width?: number; height?: number }) {
  return (
    <svg
      width={width || 18}
      height={height || 18}
      viewBox="0 0 19 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ display: 'inline-block', verticalAlign: 'middle' }}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.6377 2.43277C13.7798 2.43277 17.1377 5.79063 17.1377 9.93277C17.1377 14.0749 13.7798 17.4328 9.6377 17.4328C5.49556 17.4328 2.1377 14.0749 2.1377 9.93277C2.1377 5.79063 5.49556 2.43277 9.6377 2.43277ZM9.6377 3.93277C6.32399 3.93277 3.6377 6.61906 3.6377 9.93277C3.6377 13.2465 6.32399 15.9328 9.6377 15.9328C12.9514 15.9328 15.6377 13.2465 15.6377 9.93277C15.6377 6.61906 12.9514 3.93277 9.6377 3.93277ZM9.6377 5.43277C10.0223 5.43277 10.3393 5.7223 10.3826 6.0953L10.3877 6.18277V9.62211L11.668 10.9024C11.9609 11.1953 11.9609 11.6702 11.668 11.9631C11.3977 12.2335 10.9722 12.2543 10.678 12.0255L10.6074 11.9631L9.10737 10.4631C8.99016 10.3459 8.91575 10.1939 8.8942 10.0314L8.8877 9.93277V6.18277C8.8877 5.76856 9.22348 5.43277 9.6377 5.43277Z"
        fill="currentColor"
      />
    </svg>
  )
}
