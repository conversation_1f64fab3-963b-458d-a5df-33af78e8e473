import React from 'react'

const SVGNewPlay: React.FC<{ width?: number; height?: number }> = ({ width, height }) => {
  return (
    <svg
      width={width || '32px'}
      height={height || '32px'}
      viewBox="0 0 32 32"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="🌕-Light/Feed/thumbnail/video" transform="translate(-44, -44)">
          <g id="play" transform="translate(44, 44)">
            <circle id="Oval" fillOpacity="0.6" fill="#585D66" cx="16" cy="16" r="16"></circle>
            <g id="Icons/system/20px/play-solid" transform="translate(7, 6)" fill="#FFFFFF">
              <rect id="Rectangle" fillOpacity="0" x="0" y="0" width="20" height="20"></rect>
              <g id="ic20-play" transform="translate(4, 3)" fillRule="nonzero">
                <path
                  d="M0,1.00150607 C0,0.22971524 0.83721439,-0.25115586 1.50387103,0.13772717 L11.503871,6.13772717 C12.1653763,6.52360527 12.1653763,7.4794069 11.503871,7.865285 L1.50387103,13.865285 C0.83721439,14.254168 0,13.7732969 0,13.0015061 L0,1.00150607 Z"
                  id="Shape"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGNewPlay
