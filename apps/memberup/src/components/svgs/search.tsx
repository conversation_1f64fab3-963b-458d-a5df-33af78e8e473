import React from 'react'

const SVGSearch: React.FC<{
  width?: number
  height?: number
  strokeWidth?: number
  styles?: any
}> = ({ width, height, strokeWidth, styles }) => {
  return (
    <svg
      width={width || 16}
      height={height || 17}
      viewBox="0 0 16 17"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      style={styles}
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-3.000000, -2.000000)" fill="currentColor" fillRule="nonzero">
          <g transform="translate(3.000000, 2.000000)">
            <path
              d="M7.5,0 C11.6421356,0 15,3.35786438 15,7.5 C15,9.46558301 14.2438686,11.2545621 13.006611,12.5919318 L15.7097475,15.2955339 C16.1002718,15.6860582 16.1002718,16.3192232 15.7097475,16.7097475 C15.3192232,17.1002718 14.6860582,17.1002718 14.2955339,16.7097475 L11.457734,13.8719423 C10.3089722,14.5869731 8.95271989,15 7.5,15 C3.35786438,15 0,11.6421356 0,7.5 C0,3.35786438 3.35786438,0 7.5,0 Z M7.5,2 C4.46243388,2 2,4.46243388 2,7.5 C2,10.5375661 4.46243388,13 7.5,13 C10.5375661,13 13,10.5375661 13,7.5 C13,4.46243388 10.5375661,2 7.5,2 Z"
              id="Combined-Shape"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGSearch
