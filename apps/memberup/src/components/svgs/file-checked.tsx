import React from 'react'

const SV<PERSON><PERSON>ileChecked: React.FC<{
  width?: number
  height?: number
  styles?: any
}> = ({ width, height, styles }) => {
  return (
    <svg width={width || 20} height={height || 18} style={{ ...styles }} viewBox="0 0 20 22" version="1.1">
      <g
        xmlns="http://www.w3.org/2000/svg"
        id="Icons/system/20px/file-check-solid"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
      >
        <path
          d="M10,2 C10.2652165,2 10.5195704,2.10535684 10.7071068,2.29289322 L14.7071068,6.29289322 C14.8946432,6.4804296 15,6.73478351 15,7 L15,8 L13,8 C9.6862915,8 7,10.6862915 7,14 L7,17.999 L6,18 C4.34314575,18 3,16.6568542 3,15 L3,5 C3,3.34314575 4.34314575,2 6,2 L10,2 Z M15.2523339,11.3351051 C15.6217929,10.9245951 16.2540827,10.8913167 16.6645927,11.2607757 C17.0751028,11.6302347 17.1083812,12.2625246 16.7389221,12.6730346 L12.7432941,16.6689733 C12.3600219,17.0948313 11.6980183,17.1122404 11.2928932,16.7071154 L9.29289322,14.7071154 C8.90236893,14.3165911 8.90236893,13.6834261 9.29289322,13.2929018 C9.68341751,12.9023775 10.3165825,12.9023775 10.7071068,13.2929018 L11.9618052,14.5476002 Z"
          id="Combined-Shape"
          fill="currentColor"
        />
      </g>
    </svg>
  )
}

export default SVGFileChecked
