import React from 'react'

const SVGUser: React.FC<{
  width?: number
  height?: number
  strokeWidth?: number
  styles?: any
}> = ({ width, height, strokeWidth, styles }) => {
  return (
    <svg
      width={width || 20}
      height={height || 20}
      viewBox="0 0 20 16"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      style={{ ...styles }}
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-2.000000, -2.000000)" fill="currentColor" fillRule="nonzero">
          <g transform="translate(2.000000, 2.000000)">
            <path
              d="M5,10 L11,10 C13.7614237,10 16,12.2385763 16,15 C16,15.5522847 15.5522847,16 15,16 C14.4871642,16 14.0644928,15.6139598 14.0067277,15.1166211 L13.9949073,14.8237272 C13.9070404,13.3072462 12.6927538,12.0929596 11.1762728,12.0050927 L11,12 L5,12 C3.34314575,12 2,13.3431458 2,15 C2,15.5522847 1.55228475,16 1,16 C0.44771525,16 0,15.5522847 0,15 C0,12.3112453 2.12230671,10.1181819 4.78311038,10.0046195 L5,10 L11,10 L5,10 Z M8,0 C10.7614237,0 13,2.23857625 13,5 C13,7.76142375 10.7614237,10 8,10 C5.23857625,10 3,7.76142375 3,5 C3,2.23857625 5.23857625,0 8,0 Z M8,2 C6.34314575,2 5,3.34314575 5,5 C5,6.65685425 6.34314575,8 8,8 C9.6568542,8 11,6.65685425 11,5 C11,3.34314575 9.6568542,2 8,2 Z"
              id="Shape"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGUser
