import React from 'react'

const SVGSpark: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 12} height={height || 17} viewBox="0 0 12 17" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-28.000000, -299.000000)" fill="currentColor">
          <g transform="translate(8.000000, 288.000000)">
            <g transform="translate(16.000000, 10.000000)">
              <rect fillOpacity="0" x="0" y="0" width="20" height="20"></rect>
              <path d="M4.19036364,10.620366 L12.1703286,2.06267707 C12.3586546,1.86071686 12.6750441,1.84966416 12.8770043,2.03799017 C13.0168686,2.16841231 13.0700894,2.36718877 13.0141038,2.5500482 L11.1943812,8.4936053 L14.5551226,8.4936053 C14.831265,8.4936053 15.0551226,8.71746292 15.0551226,8.9936053 C15.0551226,9.12156481 15.0060637,9.24465462 14.9180459,9.3375336 L6.8870584,17.8120689 C6.69711205,18.0125059 6.38064381,18.0210102 6.1802068,17.8310639 C6.04185762,17.6999557 5.99008389,17.5015942 6.0467102,17.3195961 L7.86942323,11.4613604 L4.55604495,11.4613604 C4.27990257,11.4613604 4.05604495,11.2375028 4.05604495,10.9613604 C4.05604495,10.834792 4.10404543,10.7129333 4.19036364,10.620366 Z"></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGSpark
