import React from 'react'

const SVGExternalLink: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={'16px' || width} height={'16px' || height} viewBox="0 0 16 16" style={{ ...styles }}>
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/20px/open-in-new" transform="translate(-2, -2)" fill="currentColor">
          <path
            d="M7,2 C7.55228475,2 8,2.44771525 8,3 C8,3.51283584 7.61395981,3.93550716 7.11662113,3.99327227 L7,4 L5,4 C4.48716416,4 4.06449284,4.38604019 4.00672773,4.88337887 L4,5 L4,15 C4,15.5128358 4.38604019,15.9355072 4.88337887,15.9932723 L5,16 L15,16 C15.5128358,16 15.9355072,15.6139598 15.9932723,15.1166211 L16,15 L16,13 C16,12.4871642 16.3860402,12.0644928 16.8833789,12.0067277 L17,12 C17.5128358,12 17.9355072,12.3860402 17.9932723,12.8833789 L18,13 L18,15 C18,16.5976809 16.75108,17.9036609 15.1762728,17.9949073 L15,18 L5,18 C3.40231912,18 2.09633912,16.75108 2.00509269,15.1762728 L2,15 L2,5 C2,3.40231912 3.24891996,2.09633912 4.82372721,2.00509269 L5,2 L7,2 Z M11.7071068,9.70710678 C11.3466228,10.0675907 10.7793918,10.0953203 10.3871006,9.79029539 L10.2928932,9.70710678 C9.93240926,9.34662282 9.90467972,8.77939176 10.2097046,8.38710056 L10.2928932,8.29289322 L14.584,4 L13,4 C12.4477153,4 12,3.55228475 12,3 C12,2.44771525 12.4477153,2 13,2 L17,2 L17.081,2.003 L17.2007258,2.02024007 L17.3121425,2.04973809 L17.4232215,2.09367336 L17.5207088,2.14599545 L17.6167501,2.21278596 L17.7071068,2.29289322 L17.8036654,2.40469339 L17.8753288,2.5159379 L17.9063462,2.57690085 L17.9401141,2.65834962 L17.9641549,2.73400703 L17.9930928,2.8819045 L18,3 L18,7 C18,7.51283584 17.6139598,7.93550716 17.1166211,7.99327227 L17,8 C16.4477153,8 16,7.55228475 16,7 L16,5.414 L11.7071068,9.70710678 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGExternalLink
