import React from 'react'

const SVGClock: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg
      width={width || 12}
      height={height || 12}
      viewBox="0 0 12 12"
      version="1.1"
      style={{ ...styles }}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1080.000000, -262.000000)">
          <g transform="translate(1074.000000, 256.000000)">
            <g transform="translate(4.000000, 4.000000)">
              <rect x="0" y="0" width="16" height="16"></rect>
              <g strokeWidth="1" fillRule="evenodd" transform="translate(2.000000, 2.000000)" fill="currentColor">
                <path
                  d="M6,0 C9.3137085,0 12,2.6862915 12,6 C12,9.3137085 9.3137085,12 6,12 C2.6862915,12 0,9.3137085 0,6 C0,2.6862915 2.6862915,0 6,0 Z <PERSON>6,2 C5.44771525,2 5,2.44771525 5,3 L5,6 C5,6.26521649 5.10535684,6.5195704 5.29289322,6.70710678 L7.29289322,8.7071068 C7.68341751,9.0976311 8.3165825,9.0976311 8.7071068,8.7071068 C9.0976311,8.3165825 9.0976311,7.68341751 8.7071068,7.29289322 L7,5.58578644 L7,3 C7,2.44771525 6.55228475,2 6,2 Z"
                  id="Shape"
                  fillRule="nonzero"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGClock
