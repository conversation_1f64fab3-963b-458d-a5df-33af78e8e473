import Box from '@mui/material/Box'
import List from '@mui/material/List'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import { makeStyles } from '@mui/styles'
import { useLayoutEffect, useState } from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { selectMembershipAssetsPath } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    position: 'relative',
    width: '100%',
    height: '100%',
    '& .MuiListItem-root, & .MuiListItemButton-root': {
      borderColor: theme.palette.divider,
      borderRadius: 8,
      borderStyle: 'solid',
      borderWidth: 1,
      paddingLeft: 4,
      paddingRight: 4,
      paddingTop: 4,
      paddingBottom: 4,
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
    '& .MuiListItemIcon-root': {
      width: 84,
      height: 48,
      borderRadius: 8,
      marginRight: 8,
      overflow: 'hidden',
      '& img': {
        width: '100%',
        objectFit: 'cover',
      },
    },
  },
}))

const LiveDrafts: React.FC = () => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const membershipAssetsPath = useAppSelector((state) => selectMembershipAssetsPath(state))
  const [requestDrafts, setRequestDrafts] = useState(false)
  const [drafts, setDrafts] = useState({
    total: 0,
    docs: [
      {
        thumbnail: `${membershipAssetsPath}/logos/shop.png`,
        description: 'Taking ControlOf Your Destiny With Aaaaaaaaaaaaaaaaaaaa',
      },
    ],
  })

  useLayoutEffect(() => {
    // setRequestLibraries(true)
    // getCalendarEventsApi({})
    //   .then((res) => {
    //     setCalendarEvents({
    //       total: res.data.data.total,
    //       docs: res.data.data.docs.map((d) => {
    //         d.id = d.id
    //         d.start = d.scheduled_timestamp
    //           ? new Date(d.scheduled_timestamp * 1000)
    //           : new Date(d.createdAt)
    //         return d
    //       }),
    //     })
    //   })
    //   .catch((err) => {})
    //   .finally(() => {
    //     setRequestLibraries(false)
    //   })
  }, [])

  return (
    <Box className={classes.root}>
      <List dense>
        {drafts.docs.map((item, index) => (
          <ListItemButton
            key={`live-${index}`}
            dense
            // className={clsx(classes.listItem)}
          >
            <ListItemIcon className="app-image-wrapper">
              <AppImg src={item.thumbnail} height={100} width={100} alt="Thumbnail Placeholder" />
            </ListItemIcon>
            <ListItemText primary={item.description} />
          </ListItemButton>
        ))}
      </List>
    </Box>
  )
}

export default LiveDrafts
