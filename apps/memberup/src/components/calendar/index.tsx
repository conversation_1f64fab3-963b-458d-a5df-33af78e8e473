import AccessTimeIcon from '@mui/icons-material/AccessTime'
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew'
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos'
import Check from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import MenuIcon from '@mui/icons-material/Menu'
import SettingsIcon from '@mui/icons-material/Settings'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'
import Select from '@mui/material/Select'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react'

import LibraryDrafts from './library-drafts'
import LiveDrafts from './live-drafts'
import PostDrafts from './post-drafts'
import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { getCalendarEventsApi } from '@memberup/shared/src/services/apis/calendar.api'

const useStyles = makeStyles((theme) => ({
  root: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    height: '100%',
    '& .MuiSelect-outlined': {
      backgroundColor: '#2F3033',
      borderRadius: 12,
    },
    '& .MuiOutlinedInput-notchedOutline': {
      border: 'none',
    },
    '& .fc-toolbar-title': {
      color: theme.palette.text.primary,
    },
    '& .fc--button': {
      display: 'none',
    },
    '& .fc-button-primary': {
      backgroundColor: theme.palette.text.primary,
      borderRadius: '50%',
      color: theme.palette.background.default,
      minWidth: 40,
      height: 40,
      '&:hover, &:disabled': {
        backgroundColor: theme.palette.text.primary,
        color: theme.palette.background.default,
      },
    },
    '& .fc-direction-ltr .fc-button-group > .fc-button:not(:last-child)': {
      borderTopRightRadius: '50%',
      borderBottomRightRadius: '50%',
    },
    '& .fc-today-button': {
      backgroundColor: 'transparent',
      border: 'none',
      color: theme.palette.text.primary,
      '&.fc-button-primary:hover, &.fc-button-primary:disabled': {
        backgroundColor: 'transparent',
        color: theme.palette.text.primary,
      },
    },
    '& .fc-daygrid, .fc-list-table': {
      color: theme.palette.text.primary,
    },
    '& .fc-col-header-cell': {
      textAlign: 'left',
    },
    '& .fc-daygrid-day-top': {
      flexDirection: 'row',
      paddingLeft: 2,
      paddingTop: 2,
    },
    '& .fc-daygrid-day.fc-day-today': {
      background: 'inherit',
      '& .fc-daygrid-day-number': {
        background: theme.palette.primary.main,
        borderRadius: 10,
        textAlign: 'center',
        width: 24,
      },
    },
    '& .fc-day-sun, .fc-day-sat': {
      width: 28,
      '&.fc-col-header-cell a': {
        display: 'none',
      },
    },
    '& .fc-daygrid-day-events': {
      display: 'flex',
      flex: '1 1 0',
      height: 121,
      paddingLeft: 4,
      paddingRight: 4,
      paddingBottom: 16,
      marginBottom: 12,
      '& .fc-daygrid-event-harness': {
        flex: 1,
        overflow: 'hidden',
      },
      '& .fc-daygrid-event': {
        marginLeft: 4,
        marginRight: 4,
      },
      '& .fc-daygrid-day-bottom': {
        position: 'absolute',
        bottom: 0,
        color: theme.palette.primary.main,
      },
    },
    '& .fc-event': {
      '&:hover': {
        '& td': {
          backgroundColor: 'inherit',
        },
      },
    },
    '& .fc-theme-standard th, .fc-theme-standard .fc-list-day-cushion, .fc-list-day > *': {
      borderColor: theme.palette.divider,
    },
    '& .fc-scrollgrid, .fc-list, .fc-theme-standard td, .fc-theme-standard th': {
      borderColor: theme.palette.divider,
    },
    '& .fc-theme-standard .fc-list-day-cushion, .fc .fc-list-sticky .fc-list-day > *': {
      backgroundColor: 'inherit',
    },
    '& .fc-list-empty': {
      backgroundColor: 'transparent',
    },
    '& .memberup-postEvent': {
      height: 102,
      width: '100%',
      maxWidth: 210,
      borderColor: theme.palette.divider,
      borderRadius: 12,
      borderStyle: 'solid',
      borderWidth: 1,
      color: theme.palette.text.primary,
      overflow: 'hidden',
      padding: '6px 20px 6px 32px',
      position: 'relative',
    },
    '& .memberup-liveEvent': {
      height: 102,
      width: '100%',
      maxWidth: 210,
      borderRadius: 12,
      color: theme.palette.text.primary,
      fontSize: 10,
      overflow: 'hidden',
      position: 'relative',
      '& img': {
        width: '100%',
        objectFit: 'contain',
      },
    },
    '& .memberup-userImageWrapper': {
      width: 20,
      height: 20,
      borderRadius: 10,
      borderStyle: 'solid',
      borderWidth: 2,
      borderColor: '#FFFFFF',
      fontSize: 10,
      position: 'absolute',
      left: 4,
      top: 4,
    },
    '& .memberup-rightIcon': {
      position: 'absolute',
      right: 4,
      top: 4,
      fontSize: 14,
    },
    '& .memberup-eventTime': {
      display: 'flex',
      alignItems: 'center',
      position: 'absolute',
      bottom: 4,
      left: 4,
      borderRadius: 8,
      borderStyle: 'solid',
      borderWidth: 1,
      borderColor: theme.palette.action.disabled,
      fontSize: 10,
      padding: 2,
      '& .MuiSvgIcon-root': {
        width: 12,
        height: 12,
      },
    },
    '& .memberup-textContent': {
      height: 68,
    },
    '& .fc-popover': {
      backgroundColor: '#262629',
      borderRadius: 12,
      borderColor: theme.palette.divider,
    },
    '& .fc-popover-header': {
      padding: 8,
      backgroundColor: 'transparent',
      borderBottomWidth: 1,
      borderBottomStyle: 'solid',
      borderColor: theme.palette.divider,
    },
    '& .fc-popover-title': {
      color: theme.palette.text.primary,
    },
    '& .fc-popover-body': {
      padding: 6,
      '& .memberup-postEvent': {
        height: 48,
      },
      '& .memberup-textContent': {
        height: 34,
      },
      '& .memberup-eventTime': {
        display: 'none',
      },
    },
  },
}))

const AppFullCalendar: React.FC<{ weekends: boolean }> = ({ weekends }) => {
  const classes = useStyles()
  const calendarRef = useRef(null)
  const settingsButtonRef = useRef(null)
  const [requestCalendarEvents, setRequestCalendarEvents] = useState(false)
  const [calendarViewMode, setCalendarViewMode] = useState('dayGridMonth')
  const [calendarEvents, setCalendarEvents] = useState({
    total: 0,
    docs: [],
  })
  const [activeDraft, setActiveDraft] = useState('library')
  const [openDrafts, setOpenDrafts] = useState(false)
  const [openPopover, setOpenPopover] = useState(false)
  const [title, setTitle] = useState('')

  useLayoutEffect(() => {
    setRequestCalendarEvents(true)
    getCalendarEventsApi({})
      .then((res) => {
        setCalendarEvents({
          total: res.data.data.total,
          docs: res.data.data.docs.map((d) => {
            d.id = d.id
            d.start = d.scheduled_timestamp ? new Date(d.scheduled_timestamp * 1000) : new Date(d.createdAt)
            return d
          }),
        })
      })
      .catch((err) => {})
      .finally(() => {
        setRequestCalendarEvents(false)
      })
  }, [])

  useEffect(() => {
    const calendarApi = calendarRef.current?.getApi()
    setTitle(calendarApi.view.title)
  }, [])

  const renderEventContent = useCallback((eventInfo) => {
    const data = eventInfo.event.extendedProps
    const eventType = data?.feed_type ? 'postEvent' : 'liveEvent'
    const scheduledTime = data?.scheduled_timestamp ? formatDate({ date: data.scheduled_timestamp * 1000 }) : null

    if (eventType === 'postEvent') {
      return (
        <div className={clsx('memberup-postEvent', 'background-color08')}>
          <div className="memberup-userImageWrapper">
            <AppProfileImage
              imageUrl={data?.user?.profile?.image || data?.user?.image}
              cropArea={data?.user?.profile?.image_crop_area || data?.user?.image_crop_area}
              name={data?.user?.first_name}
              size={16}
              fontSize={16}
            />
          </div>
          <div className="memberup-rightIcon">#</div>
          <div className="memberup-textContent text-ellipsis" dangerouslySetInnerHTML={{ __html: data?.content }}></div>
          {Boolean(scheduledTime) && (
            <div className="memberup-eventTime">
              <AccessTimeIcon /> &nbsp;{scheduledTime}
            </div>
          )}
        </div>
      )
    }

    return (
      <div className="memberup-liveEvent">
        <AppImg src={data?.thumbnail} height={100} width={100} alt="Live Thumbnail" />
        <div className="memberup-eventTime">
          <AccessTimeIcon /> &nbsp;05:30PM
        </div>
      </div>
    )
  }, [])

  const handleClickToday = () => {
    const calendarApi = calendarRef.current?.getApi()
    calendarApi.today()
    setTitle(calendarApi.view.title)
  }

  const handleClickLeft = () => {
    const calendarApi = calendarRef.current?.getApi()
    calendarApi.prev()
    setTitle(calendarApi.view.title)
  }

  const handleClickRight = () => {
    const calendarApi = calendarRef.current?.getApi()
    calendarApi.next()
    setTitle(calendarApi.view.title)
  }

  const handleChangeView = (viewMode: 'dayGridMonth' | 'listMonth') => {
    setCalendarViewMode(viewMode)
    calendarRef.current?.getApi().changeView(viewMode)
  }

  const handleDateClick = (arg) => {
    console.log('handleDateClick =====', arg)
  }

  const handleEventClick = (arg) => {
    console.log('handleClickEvent', arg)
  }

  const handleReceiveEvent = (arg) => {
    console.log('handleReceiveEvent', arg)
  }

  const handleEventDrop = (arg) => {
    console.log('handleEventDrop', arg)
  }

  return (
    <Box className={classes.root}>
      <Grid container>
        <Grid item xs>
          <Box sx={{ pb: 1 }}>
            <Grid container alignItems={'center'}>
              <Grid item xs>
                <Typography variant="h6" color="inherit">
                  {title}
                </Typography>
              </Grid>
              <Grid item>
                <Button color="inherit" variant="text" onClick={handleClickToday}>
                  Today
                </Button>
                <IconButton onClick={handleClickLeft}>
                  <ArrowBackIosNewIcon />
                </IconButton>
                <IconButton onClick={handleClickRight}>
                  <ArrowForwardIosIcon />
                </IconButton>
                <IconButton ref={settingsButtonRef} onClick={() => setOpenPopover(!openPopover)}>
                  <SettingsIcon />
                </IconButton>
                <IconButton onClick={() => setOpenDrafts(!openDrafts)}>
                  {openDrafts ? <CloseIcon /> : <MenuIcon />}
                </IconButton>
              </Grid>
            </Grid>
          </Box>
          {/* <FullCalendar
            plugins={[dayGridPlugin, interactionPlugin, listPlugin, timeGridPlugin]}
            weekends={weekends || false}
            initialView="dayGridMonth"
            dateClick={handleDateClick}
            editable={true}
            droppable={true}
            events={calendarEvents.docs}
            nowIndicator={true}
            ref={calendarRef}
            navLinkDayClick={(args) => {
              console.log(args) //starting visible date
            }}
            navLinkWeekClick={(args) => {
              console.log(args) //starting visible date
            }}
            dayMaxEvents={2}
            headerToolbar={false}
            // dayCellContent={renderDayCellContent}
            eventContent={renderEventContent}
            eventReceive={handleReceiveEvent}
            eventClick={handleEventClick}
            eventDrop={handleEventDrop}
          /> */}
          <Menu
            anchorEl={settingsButtonRef.current}
            open={openPopover}
            onClose={() => setOpenPopover(false)}
            PaperProps={{
              sx: {
                borderRadius: 2,
                mt: '6px',
                p: '2px',
                '& .MuiList-root': {
                  paddingTop: 0,
                  paddingBottom: 0,
                },
                '& .MuiMenuItem-root': {
                  m: '4px 8px',
                  p: '0',
                  borderRadius: '4px',
                },
                '& .MuiListItemIcon-root': {
                  minWidth: 24,
                },
              },
            }}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
          >
            <MenuItem>
              <Grid container>
                <Grid item xs>
                  <Button
                    color="inherit"
                    className={
                      calendarViewMode === 'dayGridMonth' ? 'background-color14 no-pointer' : 'background-color15'
                    }
                    fullWidth
                    size="small"
                    variant="text"
                    onClick={() => handleChangeView('dayGridMonth')}
                  >
                    Grid
                  </Button>
                </Grid>
                <Grid item xs>
                  <Button
                    color="inherit"
                    className={
                      calendarViewMode === 'listMonth' ? 'background-color14 no-pointer' : 'background-color16'
                    }
                    fullWidth
                    size="small"
                    variant="text"
                    onClick={() => handleChangeView('listMonth')}
                  >
                    List
                  </Button>
                </Grid>
              </Grid>
            </MenuItem>
            {/* <MenuItem>
              <Grid container>
                <Grid item xs>
                  <Button
                    color="inherit"
                    disabled={calendarViewMode === 'dayGridMonth'}
                    fullWidth
                    size="small"
                    variant="contained"
                    onClick={() => handleChangeView('dayGridMonth')}
                  >
                    Day
                  </Button>
                </Grid>
                <Grid item xs>
                  <Button
                    color="inherit"
                    disabled={calendarViewMode === 'dayGridMonth'}
                    fullWidth
                    size="small"
                    variant="contained"
                    onClick={() => handleChangeView('dayGridMonth')}
                  >
                    Month
                  </Button>
                </Grid>
              </Grid>
            </MenuItem> */}
            <MenuItem>
              <ListItemIcon>
                <Check fontSize="small" />
              </ListItemIcon>
              Show Weekends
            </MenuItem>
            <MenuItem>Default Post Time</MenuItem>
          </Menu>
          {/* <Popover
            // className={classes.popover}
            anchorEl={settingsButtonRef.current}
            elevation={0}
            marginThreshold={0}
            open={openPopover}
            onClose={() => setOpenPopover(false)}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'center',
              horizontal: 'center',
            }}
            TransitionComponent={Fade}
          >
            <Box>Test</Box>
          </Popover> */}
        </Grid>
        {openDrafts && (
          <Grid item>
            <Box style={{ width: 260, paddingLeft: 24 }}>
              <Box>
                <Select
                  value={activeDraft}
                  displayEmpty
                  fullWidth
                  size="small"
                  onChange={(e) => setActiveDraft(e.target.value)}
                >
                  <MenuItem value="library">Library Drafts</MenuItem>
                  <MenuItem value="live">Live Drafts</MenuItem>
                  <MenuItem value="post">Post Drafts</MenuItem>
                </Select>
              </Box>
              <Box>
                {activeDraft === 'library' && <LibraryDrafts />}
                {activeDraft === 'live' && <LiveDrafts />}
                {activeDraft === 'post' && <PostDrafts />}
              </Box>
            </Box>
          </Grid>
        )}
      </Grid>
    </Box>
  )
}

export default AppFullCalendar
