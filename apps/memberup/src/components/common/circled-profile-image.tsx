import { Box } from '@mui/material'
import { styled } from '@mui/material/styles'
import Image from 'next/image'

import useAppTheme from '@/memberup/components/hooks/use-app-theme'

const RankingIndicator = styled('div')(() => ({
  width: '32px',
  height: '32px',
  borderRadius: '17px',
  borderWidth: '3px',
  borderStyle: 'solid',
  lineHeight: '30px',
  textAlign: 'center',
  fontSize: '16px',
  fontWeight: '500',
  position: 'absolute',
  zIndex: 20,
  right: '6px',
  bottom: '2px',
  userSelect: 'none',
  fontFamily: 'Graphik SemiBold',
}))

export default function CircledProfileImage({
  imageUrl,
  progress,
  ranking,
}: {
  imageUrl: string
  progress?: number
  ranking?: number
}) {
  const { isDarkTheme, theme } = useAppTheme()

  const dashOffset = ((progress || 0) / 100) * 427.2566

  return (
    <Box
      sx={{
        width: '140px',
        height: '140px',
        position: 'relative',
        '& svg': {
          position: 'absolute',
          left: '0',
          top: '0',
          zIndex: 10,
          transform: 'rotate(47deg)',
        },
        '& svg circle': {
          strokeDashoffset: 0,
          stroke: theme.palette.primary.main,
          strokeWidth: '4px',
        },
        '& svg #bar': {
          stroke: '#C0C1C5',
          strokeDashoffset: `${dashOffset}px}`,
        },
      }}
      className="circled-profile-picture"
    >
      <Box
        sx={{
          width: '126px',
          height: '126px',
          borderRadius: '50%',
          overflow: 'hidden',
          userSelect: 'none',
          position: 'absolute',
          left: '7px',
          top: '7px',
          zIndex: 10,
        }}
      >
        <Image src={imageUrl} alt="Profile Picture" fill style={{ objectFit: 'cover' }} sizes="252px" />
      </Box>
      <svg id="svg" width="140" height="140" viewBox="0 0 140 140" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <circle id="circle" r="68" cx="70" cy="70" fill="transparent" strokeDasharray="0" strokeDashoffset="0"></circle>
        <circle
          id="bar"
          r="68"
          cx="70"
          cy="70"
          fill="transparent"
          strokeDasharray="427.2566"
          strokeDashoffset="0"
        ></circle>
      </svg>
      {ranking !== undefined && (
        <RankingIndicator
          sx={{
            borderColor: isDarkTheme ? 'var(--dark-background-3)' : 'var(--pure-white)',
            backgroundColor: theme.palette.primary.main,
          }}
        >
          {ranking}
        </RankingIndicator>
      )}
    </Box>
  )
}
