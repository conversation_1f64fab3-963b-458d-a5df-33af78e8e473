import { I<PERSON><PERSON><PERSON>on, Popper } from '@mui/material'
import useTheme from '@mui/material/styles/useTheme'
import { useEffect, useRef, useState } from 'react'

import SVGEmojiNew from '../../svgs/emoji-new'
import EmojiMartPicker from './emoji-mart-picker'

const EmojiPickerWithButton = ({ onSelectedEmoji, isPost = false, theme, styles }) => {
  const [emojiPickerAnchor, setEmojiPickerAnchor] = useState(null)
  const wrapperRef = useRef(null)
  const buttonRef = useRef(null)
  const muiTheme = useTheme()

  // Use the 'open' state to manage the visibility of the picker
  const [open, setOpen] = useState(false)

  const handleOpenClosePicker = () => {
    setOpen(!open)
    setEmojiPickerAnchor((anchor) => (anchor ? null : buttonRef.current)) // Toggle anchor
  }

  useEffect(() => {
    function handleClickOutside(event) {
      if (buttonRef.current && buttonRef.current.contains(event.target)) {
        return
      }

      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setOpen(false)
        setEmojiPickerAnchor(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div
      style={{ position: 'relative' }}
      onClick={(e) => {
        e.stopPropagation()
        e.preventDefault()
      }}
    >
      <IconButton
        onClick={handleOpenClosePicker}
        ref={buttonRef}
        sx={{
          ml: isPost ? 0 : 2,
          color: styles,
          '&:hover': {
            backgroundColor: isPost ? 'transparent' : '',
            color: isPost ? (muiTheme.palette.mode === 'dark' ? '#fff' : '#000') : '',
          },
        }}
      >
        <SVGEmojiNew />
      </IconButton>
      {open && (
        <Popper
          open={Boolean(emojiPickerAnchor)}
          anchorEl={emojiPickerAnchor}
          placement="bottom-start"
          modifiers={[
            {
              name: 'offset',
              options: {
                offset: [0, 8],
              },
            },
          ]}
          style={{ zIndex: 2000 }}
        >
          <div className="emoji-picker-wrapper" ref={wrapperRef}>
            <EmojiMartPicker
              onPickerClose={() => {
                setOpen(false)
                setEmojiPickerAnchor(null)
              }}
              onEmojiSelect={(e) => {
                onSelectedEmoji(e)
                setOpen(false)
                setEmojiPickerAnchor(null)
              }}
              theme={theme}
            />
          </div>
        </Popper>
      )}
    </div>
  )
}

export default EmojiPickerWithButton
