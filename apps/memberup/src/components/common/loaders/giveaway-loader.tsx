import Skeleton from '@mui/material/Skeleton'
import Stack from '@mui/material/Stack'
import React from 'react'

const GiveawayLoader: React.FC<{
  animation?: 'pulse' | 'wave' | false
  foregroundColor?: string
  backgroundColor?: string
}> = ({ animation, foregroundColor, backgroundColor }) => {
  return (
    <Stack className="w-100" spacing={3}>
      <Skeleton variant="rounded" height={170} />
      <Skeleton variant="rounded" width={140} height={8} />
    </Stack>
  )
}
export default React.memo(GiveawayLoader)
