import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Skeleton from '@mui/material/Skeleton'
import Stack from '@mui/material/Stack'
import React from 'react'

const InboxChannelLoader: React.FC<{
  animation?: 'pulse' | 'wave' | false
  foregroundColor?: string
  backgroundColor?: string
}> = ({ animation, foregroundColor, backgroundColor }) => {
  return (
    <Card className="w-100" sx={{ borderRadius: '8px', padding: '8px' }}>
      <CardContent style={{ padding: 0 }}>
        <Stack className="h-100" alignItems="center" direction="row" spacing={3}>
          <Skeleton variant="circular" width={32} height={32} />
          <Stack spacing={2} flex={1}>
            <Skeleton variant="rounded" height={12} />
            <Skeleton variant="rounded" height={8} />
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  )
}
export default React.memo(InboxChannelLoader)
