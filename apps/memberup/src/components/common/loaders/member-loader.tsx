import Skeleton from '@mui/material/Skeleton'
import Stack from '@mui/material/Stack'
import React from 'react'

const MemberLoader: React.FC<{
  animation?: 'pulse' | 'wave' | false
  foregroundColor?: string
  backgroundColor?: string
}> = ({ animation, foregroundColor, backgroundColor }) => {
  return (
    <Stack className="w-100" spacing={3}>
      <Skeleton variant="rounded" height={104} sx={{ borderRadius: '44px' }} />
      <Skeleton variant="rounded" height={12} />
      {/* <Skeleton variant="rounded" height={10} /> */}
    </Stack>
  )
}
export default React.memo(MemberLoader)
