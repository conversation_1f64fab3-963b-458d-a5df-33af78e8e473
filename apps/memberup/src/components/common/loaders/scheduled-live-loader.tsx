import Skeleton from '@mui/material/Skeleton'
import Stack from '@mui/material/Stack'
import React from 'react'

const ScheduledLiveLoader: React.FC<{
  animation?: 'pulse' | 'wave' | false
  foregroundColor?: string
  backgroundColor?: string
}> = ({ animation, foregroundColor, backgroundColor }) => {
  return (
    <div className="w-100">
      <Stack alignItems="center" direction="row" spacing={2}>
        <Skeleton variant="rounded" width={104} height={56} />
        <Stack spacing={2}>
          <Skeleton variant="rounded" width={140} height={14} />
          <Skeleton variant="rounded" width={120} height={8} />
        </Stack>
      </Stack>
    </div>
  )
}

export default React.memo(ScheduledLiveLoader)
