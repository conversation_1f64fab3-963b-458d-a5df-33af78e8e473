import { CompositeDecorator, convertToRaw, EditorState } from 'draft-js'
import { stateToHTML } from 'draft-js-export-html'
import { stateFromHTML } from 'draft-js-import-html'

export const convertToHtml = (editorState: EditorState) => {
  const options = {
    entityStyleFn: (entity) => {
      const entityType = entity.get('type').toLowerCase()
      if (entityType === 'link') {
        const data = entity.getData()
        return {
          element: 'a',
          attributes: {
            href: data.url,
            'data-type': 'link',
            'data-preview': data.preview,
          },
        }
      }

      if (entityType === 'mention') {
        const data = entity.getData()
        return {
          element: 'span',
          attributes: {
            'data-type': 'mention',
            'data-user-id': data.mention.id,
            'data-user-name': `${data.mention.name}`,
          },
        }
      }
    },
  }
  const contentState = editorState.getCurrentContent()
  const html = stateToHTML(contentState, options)
  return html
}

export const createEditorStateFromHtml = (html) => {
  const options = {
    customInlineFn: (element, { Entity }) => {
      const getLinkData = (element: Element) => {
        return {
          url: element.getAttribute('href'),
          preview: element.getAttribute('data-preview'),
        }
      }

      const getMentionData = (element: Element) => {
        return {
          id: element.getAttribute('data-user-id'),
          name: element.getAttribute('data-user-name'),
        }
      }

      if (element.tagName === 'SPAN' && element.getAttribute('data-type') === 'mention') {
        return Entity('mention', getMentionData(element), 'SEGMENTED')
      }

      if (element.tagName === 'A' && element.getAttribute('data-type') === 'link') {
        return Entity('LINK', getLinkData(element))
      }
    },
  }

  let contentState = stateFromHTML(html, options)

  const updateEntities = (contentState) => {
    // Create a copy of the current content to modify
    let newContentState = contentState

    // Iterate over each block
    contentState.getBlocksAsArray().forEach((block) => {
      // Find ranges where entities are applied in this block
      block.findEntityRanges(
        (charMetadata) => charMetadata.getEntity() !== null,
        (start, end) => {
          // Get entity key for this range
          const entityKey = block.getEntityAt(start)

          // Get existing entity
          const entity = newContentState.getEntity(entityKey)

          // MENTION
          if (entity.getType() === 'mention') {
            const existingData = entity.getData()
            const updatedData = { mention: existingData }

            // Update the entity data
            newContentState = newContentState.replaceEntityData(entityKey, updatedData)
          }

          // LINK
          if (entity.getType() === 'LINK') {
            const existingData = entity.getData()
            const updatedData = {
              ...existingData,
              preview: (existingData.preview && JSON.parse(existingData.preview)) || false,
            }

            // Update the entity data
            newContentState = newContentState.replaceEntityData(entityKey, updatedData)
          }
        },
      )
    })
    return newContentState
  }

  return EditorState.createWithContent(updateEntities(contentState))
}
