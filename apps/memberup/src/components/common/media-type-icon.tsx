import { makeStyles } from '@mui/styles'
import Image from 'next/image'
import React from 'react'

import { TAttachment } from '@memberup/shared/src/types/types'
import { getFileTypeIcon } from '@/memberup/libs/utils'

const useStyles = makeStyles((theme) => ({
  iconContainer: {
    display: 'flex',
    justifyContent: 'center', // center horizontally
    alignItems: 'center',

    width: 40,
    height: 40,
    borderRadius: '12px',
    opacity: 1,
    backgroundColor: theme.palette.mode === 'dark' ? '#313236' : '#e1e2e4',
  },
  icon: {
    color: theme.palette.mode === 'dark' ? '#8d94a3' : '#585d66',
  },
}))

const MediaTypeIcon: React.FC<{ file: TAttachment }> = ({ file }) => {
  const classes = useStyles()

  const iconSrc = getFileTypeIcon(file.mimetype, file.filename)
  return (
    <div className={classes.iconContainer}>
      <Image
        width="18"
        height="24"
        className={classes.icon}
        alt="icon"
        src={`/assets/default/images/icons/${iconSrc}`}
      />
    </div>
  )
}

export default MediaTypeIcon
