import { styled } from '@mui/material/styles'
import Tooltip, { tooltipClasses, TooltipProps } from '@mui/material/Tooltip'

const AppTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.mode === 'dark' ? 'rgb(33, 33, 36)' : '#f6f7f8',
    minHeight: '40px',
    borderRadius: '12px',
    border: theme.palette.mode === 'dark' ? '1px solid #2e2f33' : '1px solid #e0e2e2',
    color: theme.palette.text.primary,
    fontFamily: 'Graphik Regular',
    fontSize: 12,
    margin: 'auto',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
}))

export default AppTooltip
