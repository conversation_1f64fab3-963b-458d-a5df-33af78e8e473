import { useSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import React from 'react'

import { useCheckRoute2 } from '@/memberup/components/hooks/use-check-route-2'

const AuthWrapper = ({ children }) => {
  const router = useRouter()
  const { status } = useCheckRoute2()

  if (status === 'loading') {
    return <>Loading...</>
  } else if (status === 'authenticated') {
    return <>Redirecting...</>
  } else {
    return <>{children}</>
  }
}

export default AuthWrapper
