import { MenuItem } from '@mui/material'

import Select from '@/memberup/components/common/select'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'

export default function CommunitySelect() {
  const { isDarkTheme } = useAppTheme()

  return (
    <Select
      defaultValue={10}
      sx={{
        '& .MuiSelect-select:before': {
          content: '"Contributions for: "',
          color: isDarkTheme ? 'var(--spark-gray)' : 'var(--font-light-ui-gray)',
          fontSize: '13px',
          fontFamily: 'Graphik Regular',
          lineHeight: '16px',
        },
      }}
    >
      <MenuItem value={10}>MemberUp</MenuItem>
      <MenuItem value={20}>Finance community</MenuItem>
      <MenuItem value={30}>A community with a super long name</MenuItem>
    </Select>
  )
}
