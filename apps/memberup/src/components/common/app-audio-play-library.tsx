import { Mic } from '@mui/icons-material'
import { Box, Typography } from '@mui/material'
import Image from 'next/image'
import AudioPlayer, { RHAP_UI } from 'react-h5-audio-player'

import useAppTheme from '../hooks/use-app-theme'
import { useStore } from '@/hooks/useStore'

const AppAudioLibraryoPlayer = ({ lesson, containerStyle = {}, lessonFile = null, edit = false }) => {
  const { theme } = useAppTheme()
  const { isMobile } = useAppTheme()
  const isDarkTheme = useStore((state) => state.ui.isDarkTheme)

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        height: '160px',
        overflow: 'hidden',
        borderRadius: edit ? '12px 12px 0px 0px' : '12px',
        backgroundColor: isDarkTheme ? '#494c54' : '#f3f5f5',
        ...containerStyle,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          p: '8px',
          height: '100%',
          width: '100%',
        }}
      >
        {lesson?.thumbnail_url ? (
          <Image
            src={lesson?.thumbnail_url}
            alt={lesson?.title}
            layout="fill"
            objectFit="cover"
            style={{ zIndex: '1', filter: 'blur(32px) brightness(0.7)' }}
          />
        ) : null}

        <Box
          sx={{
            backgroundColor: isDarkTheme ? '#333336' : '#ffffff',
            height: '100%',
            width: '144px',
            borderRadius: '12px',
            display: isMobile ? 'none' : 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            zIndex: '2',
          }}
        >
          {lesson?.thumbnail_url ? (
            <Image
              src={lesson?.thumbnail_url}
              alt={lesson?.title}
              style={{ borderRadius: '12px' }}
              layout="fill"
              objectFit="cover"
            />
          ) : (
            <Mic
              sx={{
                color: theme.palette.text.disabled,
                fontSize: '32px',
              }}
            />
          )}
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flexBasis: '0',
            flexGrow: '1',
            ml: isMobile ? '0' : '12px',
            zIndex: '2',
          }}
        >
          <Typography
            className="text-black-500 dark:text-white-500"
            sx={{
              fontFamily: 'Graphik Semibold',
              fontSize: '18px',
              lineHeight: '24px',
              mt: '12px',
            }}
          >
            {lesson?.media_file_title}
          </Typography>
          <Typography
            sx={{
              fontFamily: 'Graphik Medium',
              fontSize: '13px',
              lineHeight: '16px',
              color: '#fff',
              mt: '8px',
            }}
          >
            {lesson?.media_file_subtitle}
          </Typography>
          <Box
            sx={{
              marginTop: 'auto',
              '& .rhap_container': {
                borderRadius: '12px',
                height: '40px',
                width: '100%',
                p: '0px',
                pr: '8px',
                backgroundColor: 'rgba(0,0,0,0.34)',
              },
              '& .rhap_controls-section': {
                display: 'block',
                flex: 'none',
              },
              '& .rhap_volume-controls': {
                display: 'block !important',
                width: isMobile ? '50px !important' : '90px !important',
                marginLeft: '20px !important',
                marginRight: isMobile ? '20px !important' : '10px !important',
                flex: 'none !important',
              },
              '& .rhap_additional-controls': {
                display: 'none',
              },
              '& .rhap_volume-indicator': {
                background: '#fff !important',
              },
              '& .rhap_progress-filled': {
                backgroundColor: theme.palette.primary.main,
              },
              '& .rhap_progress-indicator': {
                backgroundColor: '#fff',
              },
              '& .rhap_progress-bar': {
                backgroundColor: '#b6b7b9 !important',
              },
              '& .rhap_time': {
                color: '#fff',
                fontSize: '12px',
                fontFamily: 'Graphik Regular',
                minWidth: '40px',
              },
              '& .rhap_container svg': {
                color: '#fff',
                p: '3px',
              },
              zIndex: '2',
            }}
          >
            <AudioPlayer
              src={lessonFile?.url ? lessonFile?.url : lesson?.media_file?.url}
              showSkipControls={false}
              showJumpControls={false}
              layout="horizontal-reverse"
              customAdditionalControls={[]}
              customProgressBarSection={[
                // hide duration and progress time
                RHAP_UI.PROGRESS_BAR,
                RHAP_UI.CURRENT_LEFT_TIME,
                RHAP_UI.VOLUME_CONTROLS,
              ]}
              customControlsSection={[
                RHAP_UI.MAIN_CONTROLS,
                //RHAP_UI.PROGRESS_BAR,
                //RHAP_UI.CURRENT_LEFT_TIME,
                //RHAP_UI.VOLUME_CONTROLS,
              ]}
              autoPlay={false}
              autoPlayAfterSrcChange={false}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  )
}

export default AppAudioLibraryoPlayer
