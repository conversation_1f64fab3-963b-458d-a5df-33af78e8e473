import PushPinIcon from '@mui/icons-material/PushPin'
import { Box } from '@mui/material'
import Label from '@mui/material/FormControlLabel' // This is not the standard way to label, but serves for demonstration purposes.
import Stack from '@mui/material/Stack'
import { useTheme } from '@mui/material/styles'
import ToggleButton from '@mui/material/ToggleButton'
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup'
import useMediaQuery from '@mui/material/useMediaQuery'
import { makeStyles } from '@mui/styles'
import React, { useEffect, useRef } from 'react'

import SVGHourglass from '../svgs/hourglass'
import SVGPushPinHeader from '../svgs/pinned'
import SVGSparkle from '../svgs/sparkle'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'

// TODO: Improve styles, also support light theme.
const useStyles = makeStyles((theme) => ({
  container: {
    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(33,33,36, 1)' : 'rgba(255,255,255, 1)',
    borderRadius: '16px',
    padding: '8px 0px 8px 24px',
    marginTop: '16px',
    marginBottom: '16px',
  },
  label: {
    color: theme.palette.mode === 'dark' ? 'rgba(141, 148, 163, 1)' : '#585D66',
    whiteSpace: 'nowrap',
    fontFamily: 'Graphik Medium',
    fontSize: '10px',
    [theme.breakpoints.up('sm')]: {
      fontSize: '12px',
    },
    fontWeight: 500,
    textAlign: 'left',
    margin: 0,
    marginRight: '5px',
  },
  button: {
    whiteSpace: 'nowrap',
    borderRadius: '12px',
    marginRight: '5px',
    padding: '14px 24px',
    [theme.breakpoints.down('sm')]: {
      padding: '12px 14px',
    },
    backgroundColor: 'transparent',
    color: theme.palette.mode === 'dark' ? 'rgba(141,148,163,1)' : '#585D66',
    fontFamily: 'Graphik Medium',
    fontSize: '10px',
    lineHeight: '13px !important',
    [theme.breakpoints.up('sm')]: {
      fontSize: '12px',
    },
    border: 'none !important',
    fontWeight: 500,
    '&.MuiToggleButton-root:hover': {
      borderRadius: '12px !important',
    },
    '&.Mui-selected': {
      borderRadius: '12px !important',
      backgroundColor:
        theme.palette.mode === 'dark' ? 'rgba(51, 51, 54, 0.5)' : adjustRGBA(theme.palette.primary.dark, 0.15),
      color: theme.palette.mode === 'dark' ? 'white' : theme.palette.primary.dark,
      '&:hover': {
        backgroundColor:
          theme.palette.mode === 'dark' ? 'rgba(51, 51, 54, 0.6)' : adjustRGBA(theme.palette.primary.dark, 0.18),
      },
    },
  },
}))

export default function FeedSortBy({
  onChange,
  sortBy,
}: {
  onChange: any
  sortBy: 'newest' | 'activity' | 'pinned-posts'
}) {
  const classes = useStyles()
  const { isCurrentUserAdmin } = useCheckUserRole()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

  const newestRef = useRef(null)
  const activityRef = useRef(null)
  const pinnedRef = useRef(null)

  useEffect(() => {
    if (isMobile) {
      switch (sortBy) {
        case 'newest':
          newestRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'start',
          })
          break
        case 'activity':
          activityRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'start',
          })
          break
        case 'pinned-posts':
          pinnedRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'start',
          })
          break
        default:
          break
      }
    }
  }, [sortBy, isMobile])

  const handleSortOrderChange = (event, newSortBy) => {
    if (newSortBy !== null) {
      onChange(newSortBy)
    }
  }

  return (
    <Stack spacing={2} direction="row" className={classes.container}>
      <Label className={classes.label} control={<span />} label={<span className={classes.label}>Sort by</span>} />
      <Box
        sx={{
          overflowX: isMobile ? 'auto' : 'visible',
          whiteSpace: 'nowrap',
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        }}
      >
        <ToggleButtonGroup
          value={sortBy}
          exclusive
          onChange={handleSortOrderChange}
          sx={{
            '& svg': {
              position: 'relative',
              top: '-1px',
            },
          }}
        >
          <ToggleButton
            ref={newestRef}
            className={classes.button}
            value="newest"
            aria-label="Newest"
            style={{ display: 'inline-block' }}
          >
            <SVGHourglass styles={{ marginRight: '8px' }} /> Newest Posts
          </ToggleButton>
          <ToggleButton
            ref={activityRef}
            className={classes.button}
            value="activity"
            aria-label="Recent Activity"
            style={{ display: 'inline-block' }}
          >
            <SVGSparkle styles={{ marginRight: '8px' }} /> Recent Activity
          </ToggleButton>
          <ToggleButton
            ref={pinnedRef}
            className={classes.button}
            value="pinned-posts"
            aria-label="Pinned Posts"
            style={{ display: 'inline-block' }}
          >
            <SVGPushPinHeader styles={{ marginRight: '8px' }} /> Pinned Posts
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>
    </Stack>
  )
}
