import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import VisibilityOnIcon from '@mui/icons-material/Visibility'
import { Menu, MenuItem, Modal, Stack, Typography } from '@mui/material'
import Box from '@mui/material/Box'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useChatContext } from 'stream-chat-react'

import EditPost from '../dialogs/feed/edit-post'
import SVGCloseNew from '../svgs/close-new'
import SVGEditNew from '../svgs/edit-new'
import SVGHidePost from '../svgs/hide-post'
import SVGPushPin from '../svgs/push-pin'
import SVGPushPinLarge from '../svgs/push-pin-filled'
import SVGPushPinLargeEmpty from '../svgs/push-pin-large'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { searchClient } from '@memberup/shared/src/config/algolia-client'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { getDateTimeFromNow } from '@memberup/shared/src/libs/date-utils'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { FEED_STATUS_ENUM, USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { IFeed, IUser } from '@memberup/shared/src/types/interfaces'
import { UserDetailsHoverCard } from '@/components/community/user-details-hover-card'
import { toast } from '@/components/ui/sonner'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import useCheckStreamUserRole from '@/memberup/components/hooks/use-check-stream-user-role'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import SVGReport from '@/memberup/components/svgs/report'
import SVGTrash from '@/memberup/components/svgs/trash'
import SVGVerified from '@/memberup/components/svgs/verified'
import { getCommunityBaseURL } from '@/memberup/libs/utils'
import { selectPinnedPostsCount } from '@/memberup/store/features/feedAggregationSlice'
import { reportFeed, setFeedToDelete } from '@/memberup/store/features/feedSlice'
import { setActiveMember } from '@/memberup/store/features/memberSlice'
import { selectChannels } from '@/memberup/store/features/spaceSlice'
import { openDialog } from '@/memberup/store/features/uiSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { formatDateLong } from '@/shared-libs/date-utils'
import { isUserActiveAndAcceptedInCommunity } from '@/shared-libs/profile'
import { hidePinnedPostApi } from '@/shared-services/apis/user.api'
import { IMembership } from '@/shared-types/interfaces'

const useStyles = makeStyles(() => ({
  icons: {
    position: 'relative',
    top: '6px',
    right: '2px',
  },
  iconsMobile: {
    position: 'relative',
    top: '0px',
    right: '0px',
  },
  menuMobile: {
    padding: '15px',
    borderRadius: '8px',
  },
}))

const MAX_PINNED_POSTS = 3

const FeedCardHeader: React.FC<{
  feed: IFeed
  isSinglePost?: boolean
  isPostPage?: boolean
  userData: IUser
  showPinnedMessageIndicator?: boolean
  onHide?: any
  onShow?: any
  extraHeaderComponents?: any
  onMessagePinnedSuccess?: any
  onMessageUnpinnedSuccess?: any
  onPinMessage?: any
  onUnpinMessage?: any
  membership: IMembership
}> = ({
  feed,
  isSinglePost,
  userData,
  showPinnedMessageIndicator = true,
  onHide,
  onShow,
  extraHeaderComponents,
  onMessagePinnedSuccess,
  onMessageUnpinnedSuccess,
  onPinMessage,
  onUnpinMessage,
  membership,
}) => {
  const user = useStore((state) => state.auth.user)
  const { profile: userProfile, updateProfile } = useStore((state) => state.auth)
  const [hideRequest, setHideRequest] = useState(false)
  const router = useRouter()
  const pinnedPostsCount = useAppSelector((state) => selectPinnedPostsCount(state))
  const dispatch = useAppDispatch()
  const { isMobile, theme } = useAppTheme()
  const mountedRef = useMounted(true)
  const classes = useStyles()
  const { client: streamChatClient } = useChatContext()
  const createdAtRef = useRef(new Date(feed.created_at || feed.createdAt).getTime())
  const { isCurrentUserAdmin } = useCheckUserRole()
  const { isAdminOrCreatorActor } = useCheckStreamUserRole(feed.user)
  const [anchorEl, setAnchorEl] = useState(null)
  const [, setForceUpdate] = useState(0)
  const isOwnPost = feed.user.id === user?.id
  const hideContextMenu = !isOwnPost && isAdminOrCreatorActor && user?.role === USER_ROLE_ENUM.member
  const [dateTooltipOpen, setDateTooltipOpen] = useState(false)
  const [editingPost, setEditingPost] = useState(false)
  const isDarkTheme = useStore((state) => state.ui.isDarkTheme)

  const isHidden = Boolean(userProfile?.pinned_posts_hidden?.[feed.id])

  useEffect(() => {
    if (!mountedRef.current) return
    createdAtRef.current = new Date(feed.created_at || feed.createdAt).getTime()
    setForceUpdate(new Date().getTime())
    const interval = setInterval(() => {
      if (!mountedRef.current) {
        createdAtRef.current = createdAtRef.current + 1000
        setForceUpdate(new Date().getTime())
      }
    }, 60000)
    return () => {
      clearInterval(interval)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [feed.created_at, feed.createdAt])

  const handleClickMore = (e: any) => {
    e.preventDefault()
    e.stopPropagation()
    setAnchorEl(e.currentTarget)
  }

  const handleClose = (e?: any) => {
    e?.preventDefault()
    e?.stopPropagation()
    setAnchorEl(null)
  }
  const handleDelete = () => {
    dispatch(setFeedToDelete(feed))
    searchClient.clearCache()
    handleClose()
  }
  const handleEdit = () => {
    setEditingPost(true)
    dispatch(openDialog({ dialog: 'EditPost', open: true, props: { data: feed, mode: 'edit' } }))
  }
  const handleReport = () => {
    dispatch(
      reportFeed({
        data: {
          id: feed.id as string,
          feed_status: FEED_STATUS_ENUM.reported,
          reports: [
            ...(feed.reports || []),
            {
              date: new Date().toUTCString(),
              name: getFullName(user.first_name, user.last_name, ''),
              email: user.email,
            },
          ],
        },
        messages: {
          success: 'Successfully reported to community admins.',
          fail: 'Failed to report to community admins.',
        },
      }),
    )
  }

  const openUserSidePanel = (user: IUser) => {
    if (!user) return
    const { id, membership_id, name, image, image_crop_area, role } = user
    const temp = (name || '').split(' ')
    dispatch(
      setActiveMember({
        membership_id: membership_id,
        id: id as string,
        first_name: temp[0] || '',
        last_name: temp[1] || '',
        email: '',
        image,
        image_crop_area,
        role,
      }),
    )
    dispatch(openDialog({ dialog: 'UserProfile', open: true, props: {} }))
  }

  const handlePin = async (pin: boolean) => {
    if (pin) {
      onPinMessage(feed)
    } else {
      onUnpinMessage(feed)
    }
  }

  const handleHide = async (hide: boolean, postId: string) => {
    setHideRequest(true)
    try {
      const result = await hidePinnedPostApi({ hide, post_id: postId })
      if (result.data.success) {
        updateProfile({ pinned_posts_hidden: result.data.data })
        if (hide) {
          onHide?.(feed.id)
        } else {
          onShow?.(feed.id)
        }
      }
    } catch (e) {
      toast.error(e.message)
    } finally {
      setHideRequest(false)
    }
  }

  const activityTime = useMemo(() => getDateTimeFromNow(createdAtRef.current), [])
  const canPinPosts = useMemo(() => {
    return pinnedPostsCount < MAX_PINNED_POSTS
  }, [pinnedPostsCount, MAX_PINNED_POSTS])

  const open = Boolean(anchorEl)

  const getSpaceData = (message: any) => {
    const [_, channelId] = message.cid.split(':')
    return membership.channels.find((s: any) => s.id === channelId)
  }

  const streamUser = feed.user as IUser
  const name = streamUser?.name || getFullName(streamUser?.first_name, streamUser?.last_name)
  const isFeedCreator = feed.user.id === user?.id
  const editable = isCurrentUserAdmin || isFeedCreator

  const showPinnedPostBar = showPinnedMessageIndicator && feed.pinned && !isSinglePost

  const spaceData = getSpaceData(feed)
  if (!spaceData) {
    // NOTE: We don't render the header if we don't have the membership in context yet.
    return null
  }

  const spaceURL = spaceData?.slug ? `${membership.slug}?space=${spaceData.slug}` : ''

  const linkUserProfile = streamUser?.status !== 'deleted' && streamUser?.username

  const isUserAllowedToPost = isUserActiveAndAcceptedInCommunity(user, membership)

  return (
    <Stack
      className="tailwind-component"
      direction="column"
      data-cy={'feed-card-header'}
      sx={{ display: 'flex', justifyContent: 'space-between', position: 'relative' }}
    >
      {showPinnedPostBar && (
        <Stack
          direction={'row'}
          sx={{
            position: 'absolute',
            width: 'calc(100% + 32px)',
            top: '-16px',
            left: '-16px',
            display: 'flex',
            background: `linear-gradient(to right, ${adjustRGBA(theme.palette.primary.dark, 0.6)}, transparent)`,
            color: '#fff',
            borderRadius: '30px',
            padding: '3px',
            marginBottom: '16px',
            alignItems: 'center',
          }}
        >
          <Box sx={{ ml: '6px', mt: '1.5px', mr: '3px' }}>
            <SVGPushPin />
          </Box>
          <Box
            sx={{
              color: '#fff',
              fontFamily: 'Graphik Medium',
              lineHeight: '1.42',
              fontSize: '12px',
              fontWeight: '500',
              marginLeft: '4px',
              flexGrow: 1,
            }}
          >
            Pinned Post
          </Box>

          {user && (
            <div className={''}>
              {!isHidden ? (
                <IconButton
                  className="text-black-700 dark:text-black-100"
                  disabled={hideRequest}
                  sx={{
                    '&.MuiIconButton-root': {
                      padding: '0px !important',
                    },
                    '&.MuiButtonBase-root': {
                      padding: '0px !important',
                      '&:hover': { backgroundColor: 'transparent !important' },
                    },
                  }}
                  aria-label="hide"
                  onClick={(e) => {
                    e.stopPropagation()
                    e.preventDefault()
                    handleHide(true, feed.id)
                  }}
                >
                  <SVGHidePost />
                  <div className={'ml-1 text-xs font-medium text-black-700 dark:text-black-100'}>Hide</div>
                </IconButton>
              ) : (
                <IconButton
                  sx={{
                    '&.MuiIconButton-root': {
                      padding: '0px !important',
                    },
                    '&.MuiButtonBase-root': {
                      padding: '0px !important',
                      '&:hover': { backgroundColor: 'transparent !important' },
                    },
                  }}
                  aria-label="hide"
                  onClick={(e) => {
                    e.stopPropagation()
                    e.preventDefault()
                    handleHide(false, feed.id)
                  }}
                >
                  <VisibilityOnIcon className="text-black-100" sx={{ fontSize: '16px' }} />
                  <Typography
                    sx={{ marginLeft: '6px' }}
                    className="text-xs font-medium text-black-700 dark:text-black-100"
                  >
                    Show
                  </Typography>
                </IconButton>
              )}
            </div>
          )}
        </Stack>
      )}
      <Box sx={{ position: 'relative' }} data-cy="post-header" className={clsx('pb-4', showPinnedPostBar && 'pt-8')}>
        <Grid
          container
          alignItems="flex-start"
          className="flex flex-nowrap"
          sx={{
            borderRadius: '16px',
            width: '100%',
            pt: 0,
          }}
        >
          <div
            className="mr-3.5"
            onClick={(e) => {
              e.stopPropagation()
              e.preventDefault()
              openUserSidePanel(feed.user)
            }}
          >
            <AppProfileImage
              className={cn(!linkUserProfile && 'cursor-auto')}
              imageUrl={streamUser?.image || streamUser?.profile?.image || ''}
              cropArea={streamUser?.image_crop_area || streamUser?.profile?.image_crop_area}
              name={name || ''}
              size={40}
            />
          </div>
          <div className="flex grow flex-row">
            <Grid container>
              <Grid item xs={12} className="d-flex align-center">
                {linkUserProfile ? (
                  <UserDetailsHoverCard username={streamUser.username}>
                    <Link
                      className="text-sm font-semibold text-black-700 hover:text-grey-700 dark:text-white-500 hover:dark:text-grey-100"
                      href={`/@${streamUser?.username}`}
                      onClick={(e) => e.stopPropagation()}
                    >
                      {name ? name : 'No Name'}
                      {isAdminOrCreatorActor && <SVGVerified className="ml-0.5 inline-block" width={16} height={16} />}
                    </Link>
                  </UserDetailsHoverCard>
                ) : (
                  <span className="cursor-default font-semibold text-black-700 dark:text-white-500">
                    {name ? name : 'No Name'}
                    {isAdminOrCreatorActor && <SVGVerified className="ml-0.5 inline-block" width={16} height={16} />}
                  </span>
                )}
              </Grid>
              <Grid item xs={12} className="d-flex align-center" sx={{ mt: '1px' }}>
                <TooltipProvider delayDuration={700}>
                  <Tooltip open={dateTooltipOpen} onOpenChange={setDateTooltipOpen}>
                    <TooltipTrigger
                      asChild
                      // Workaround from: https://github.com/radix-ui/primitives/issues/955#issuecomment-**********
                      onClick={() => setDateTooltipOpen((prevOpen) => !prevOpen)}
                      // Timeout runs setOpen after onOpenChange to prevent bug on mobile
                      onFocus={() => setTimeout(() => setDateTooltipOpen(true), 0)}
                      onBlur={() => setDateTooltipOpen(false)}
                    >
                      <div className="text-xs text-black-200 dark:text-black-100">{activityTime}&nbsp;in&nbsp;</div>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">{formatDateLong(new Date(feed.created_at))}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <Link
                  className="text-xs font-semibold text-black-200 hover:text-black-100 dark:text-black-100 dark:hover:text-black-200"
                  href={spaceURL}
                  onClick={(e) => {
                    e.stopPropagation()
                  }}
                >
                  {spaceData.name}
                </Link>
              </Grid>
            </Grid>
            <div className={''}>{extraHeaderComponents}</div>
          </div>
          {isSinglePost && !hideContextMenu && (
            <Grid item sx={{ paddingRight: { xs: '12px', sm: '0' } }}>
              {isUserAllowedToPost && (
                <IconButton
                  className={cn(
                    open
                      ? 'bg-white-400 text-black-700 dark:bg-grey-900 dark:text-white-500'
                      : 'text-grey-700 hover:text-grey-700 dark:text-black-100 dark:hover:text-white-500',
                  )}
                  size="small"
                  onClick={handleClickMore}
                  aria-label="more"
                  data-cy="post-header-more"
                >
                  <MoreHorizIcon />
                </IconButton>
              )}
            </Grid>
          )}
        </Grid>

        {open &&
          (isMobile ? (
            <Modal
              open={open}
              onClose={handleClose}
              aria-labelledby="slide-dialog-title"
              aria-describedby="slide-dialog-description"
              sx={{
                '& .MuiDialog-paper': {
                  backgroundColor: 'transparent',
                  height: '100%',
                  width: '100%',
                  maxHeight: '100%',
                  maxWidth: '100%',
                  margin: 0,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'flex-end',
                  alignItems: 'center',
                },
                '& .MuiMenuItem-root': {
                  p: '12px',
                  color: isDarkTheme ? '#8e94a2' : '#595d65',
                  '&:hover': {
                    color: isDarkTheme ? '#ffffff !important' : '#000000 !important',
                  },
                },
                '& .MuiTypography-body2': {
                  color: isDarkTheme ? '#8e94a2' : '#595d65',
                  fontSize: '12px',
                  lineHeight: '16px',
                },
                '& .MuiListItemText-primary': {
                  fontFamily: 'Graphik Medium',
                  fontSize: '13px',
                  color: isDarkTheme ? '#e1e1e1' : '#1c1c1c',
                  lineHeight: '16px',
                },
              }}
            >
              <div>
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    right: 0,
                    backgroundColor: isDarkTheme ? '#17171a' : '#fff',
                    width: '100%',
                    margin: 'auto',
                    padding: '10px 10px',
                    borderRadius: '12px 12px 0px 0px',
                  }}
                >
                  <IconButton
                    sx={{
                      background: isDarkTheme ? '#17171a' : '#ffffff',
                      color: '#8D94A3',
                      p: '14px',
                      m: '6px',
                      width: '40px',
                      height: '40px',
                      '&.MuiIconButton-root': {
                        //position: 'initial',
                      },
                      marginTop: '-80px',
                    }}
                    size="medium"
                    aria-label="close"
                    className="close large"
                    onClick={handleClose}
                  >
                    <SVGCloseNew />
                  </IconButton>
                  {isCurrentUserAdmin && (
                    <MenuItem
                      className={clsx(classes.menuMobile, !canPinPosts && !feed.pinned ? 'disabled' : '')}
                      onClick={() => handlePin(!feed.pinned)}
                      data-cy="pin-post"
                      sx={{
                        '&.disabled': {
                          opacity: 0.5,
                          cursor: 'not-allowed',
                        },
                      }}
                    >
                      <ListItemIcon className={classes.iconsMobile}>
                        {feed.pinned ? (
                          <SVGPushPinLarge styles={{ color: theme.palette.primary.main }} />
                        ) : (
                          <SVGPushPinLargeEmpty />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={feed.pinned ? 'Unpin this post' : 'Pin this post'}
                        secondary={!feed.pinned && !canPinPosts ? 'Max 3 pinned posts allowed' : ''}
                      />
                    </MenuItem>
                  )}
                  {isCurrentUserAdmin}
                  {editable && (
                    <MenuItem className={classes.menuMobile} onClick={handleEdit} data-cy="edit-post">
                      <ListItemIcon className={classes.iconsMobile}>
                        <SVGEditNew />
                      </ListItemIcon>
                      <ListItemText primary="Edit this post" secondary="Make changes to your post" />
                    </MenuItem>
                  )}
                  {editable && <Divider />}
                  {editable && (
                    <MenuItem className={classes.menuMobile} onClick={handleDelete} data-cy="delete-post">
                      <ListItemIcon className={classes.iconsMobile}>
                        <SVGTrash />
                      </ListItemIcon>
                      <ListItemText primary="Delete this post" secondary="Delete this post permanently" />
                    </MenuItem>
                  )}
                  {!isOwnPost && (
                    <MenuItem className={classes.menuMobile} onClick={handleReport} data-cy="report-post">
                      <ListItemIcon className={classes.iconsMobile}>
                        <SVGReport />
                      </ListItemIcon>
                      <ListItemText primary="Report this post" secondary="Post violates Community Standards" />
                    </MenuItem>
                  )}
                </Box>
              </div>
            </Modal>
          ) : hideContextMenu ? null : (
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              onClick={handleClose}
              sx={{
                '& .MuiBackdrop-root': {
                  backgroundColor: 'transparent',
                },
              }}
              PaperProps={{
                sx: {
                  mt: '6px',
                  width: 265,
                  borderRadius: '12px',
                  boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                  border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                  backgroundColor: isDarkTheme ? '#000000' : '#f7f7f8',
                  padding: '6px 6px 6px 4px',
                  '& .MuiList-root': {
                    paddingTop: 0,
                    paddingBottom: 0,
                  },
                  '& .MuiDivider-root': {
                    marginTop: 1,
                    marginBottom: 1,
                  },
                  '& .MuiMenuItem-root': {
                    p: '12px',
                    alignItems: 'flex-start',
                    color: isDarkTheme ? '#8e94a2' : '#595d65',
                    '&:hover': {
                      color: isDarkTheme ? '#ffffff !important' : '#000000 !important',
                    },
                  },
                  '& .MuiListItemIcon-root': {
                    color: 'inherit',
                    fontSize: 18,
                    minWidth: 28,
                  },
                  '& .MuiSvgIcon-root': {
                    fontSize: 18,
                  },
                  '& .MuiTypography-body2': {
                    color: isDarkTheme ? '#8e94a2' : '#595d65',
                    fontSize: '12px',
                    lineHeight: '16px',
                  },
                  '& .MuiListItemText-primary': {
                    fontFamily: 'Graphik Medium',
                    fontSize: '13px',
                    color: isDarkTheme ? '#e1e1e1' : '#1c1c1c',
                    lineHeight: '16px',
                  },
                  '& .MuiButtonBase-root': {
                    '&:hover': {
                      borderRadius: '8px',
                    },
                  },
                },
              }}
              transformOrigin={{ horizontal: 160, vertical: 'top' }}
              anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
              {isCurrentUserAdmin && (
                <MenuItem
                  sx={{
                    '&:hover': {
                      backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f9f9',
                    },
                    '&.disabled': {
                      opacity: 0.5,
                      cursor: 'not-allowed',
                    },
                  }}
                  onClick={() => handlePin(!feed.pinned)}
                  data-cy="pin-post"
                  className={!canPinPosts && !feed.pinned ? 'disabled' : ''}
                >
                  <ListItemIcon className={classes.icons}>
                    {feed.pinned ? (
                      <SVGPushPinLarge styles={{ color: theme.palette.primary.main }} />
                    ) : (
                      <SVGPushPinLargeEmpty />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={feed.pinned ? 'Unpin this post' : 'Pin this post'}
                    secondary={!feed.pinned && !canPinPosts ? 'Max 3 pinned posts allowed' : ''}
                  />
                </MenuItem>
              )}

              {editable && (
                <MenuItem
                  sx={{
                    '&:hover': {
                      backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f9f9',
                    },
                  }}
                  onClick={handleEdit}
                  data-cy="edit-post"
                >
                  <ListItemIcon className={classes.icons}>
                    <SVGEditNew />
                  </ListItemIcon>
                  <ListItemText primary="Edit this post" secondary="Make changes to your post" />
                </MenuItem>
              )}
              {editable && <Divider />}
              {editable && (
                <MenuItem
                  sx={{
                    '&:hover': {
                      backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f9f9',
                    },
                  }}
                  onClick={handleDelete}
                  data-cy="delete-post"
                >
                  <ListItemIcon className={classes.icons}>
                    <SVGTrash />
                  </ListItemIcon>
                  <ListItemText primary="Delete this post" secondary="Delete this post permanently" />
                </MenuItem>
              )}
              {!isOwnPost && (
                <MenuItem
                  sx={{
                    '&:hover': {
                      backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f9f9',
                    },
                  }}
                  onClick={handleReport}
                  data-cy="report-post"
                >
                  <ListItemIcon className={classes.icons}>
                    <SVGReport />
                  </ListItemIcon>
                  <ListItemText primary="Report this post" secondary="Post violates Community Standards" />
                </MenuItem>
              )}
            </Menu>
          ))}
      </Box>
      {membership?.id && (
        <EditPost
          membership={membership}
          data={feed}
          mode="edit"
          open={editingPost}
          onClose={() => {
            setEditingPost(null)
          }}
        />
      )}
    </Stack>
  )
}

export default React.memo(FeedCardHeader)
