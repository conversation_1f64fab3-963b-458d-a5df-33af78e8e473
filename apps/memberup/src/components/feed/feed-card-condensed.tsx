'use client'

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Link from 'next/link'
import React, { useCallback, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'

import FeedCardContentCondensed from './feed-card-content-condensed'
import { upsertFeedTrack } from '@memberup/shared/src/services/apis/feed-track.api'
import { IFeed } from '@memberup/shared/src/types/interfaces'
import CommentThread from '@/components/feed/CommentThread'
import { Separator } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import FeedDetailsDialog from '@/memberup/components/dialogs/feed/feed-details-dialog'
import FeedCardHeader from '@/memberup/components/feed/feed-card-header'
import LikesCommentsBar from '@/memberup/components/feed/likes-comments-bar'
import { addViewedFeed } from '@/memberup/store/features/feedTrackSlice'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { useAppDispatch } from '@/memberup/store/store'
import { isUserActiveAndAcceptedInCommunity } from '@/shared-libs/profile'

const FeedCardCondensed: React.FC<{
  feed: any
  styleOverride?: any
  showPinnedMessageIndicator?: boolean
  comments?: any
  membership?: any
  onHide?: any
  onShow?: any
  extraHeaderComponents?: any
  readonly?: boolean
  onMessagePinnedSuccess?: any
  onMessageUnpinnedSuccess?: any
  onPinMessage?: any
  onUnpinMessage?: any
}> = ({
  feed: message,
  styleOverride = null,
  showPinnedMessageIndicator = true,
  comments,
  membership,
  onHide,
  onShow,
  onPinMessage,
  onUnpinMessage,
  extraHeaderComponents,
  readonly,
  onMessagePinnedSuccess,
  onMessageUnpinnedSuccess,
}) => {
  const dispatch = useAppDispatch()
  const rootEleRef = useRef(null)
  const user = useStore((state) => state.auth.user)
  const userProfile = useStore((state) => state.auth.profile)
  const [openFeedDetails, setOpenFeedDetails] = useState(false)

  const isUserAllowedToPost = isUserActiveAndAcceptedInCommunity(user, membership)

  const members = useAppSelector((state) => selectMembersMap(state))
  const feedTrackIds = useSelector((state: any) => state.feedTrack.feedTrackIds)
  const isPostOwner = message.user.id === userProfile?.user_id
  const feed = message

  const latestCommentTrackDate = feedTrackIds?.[feed.id] ? feedTrackIds[feed.id] : null
  const hasCommentsUnread = message.latest_comment_timestamp > latestCommentTrackDate

  const hasPostBeenRead = feedTrackIds?.[feed.id] || isPostOwner

  const handleClose = useCallback(() => {
    setOpenFeedDetails(false)
  }, [])

  let cardStyles = {
    borderRadius: '16px',
    width: '100%',
    overflow: 'visible',
    height: 'auto',

    padding: 3,
    paddingLeft: 4,
    paddingRight: 4,
  }

  if (styleOverride) {
    cardStyles = {
      ...cardStyles,
      ...styleOverride,
    }
  }

  const renderInnerContent = (
    <div>
      <FeedCardContentCondensed feed={feed} hasPostBeenRead={hasPostBeenRead} />
      <LikesCommentsBar
        readonly={readonly || !isUserAllowedToPost}
        message={feed as unknown as IFeed}
        isDetailView={false}
        hasCommentsUnread={hasCommentsUnread}
        members={members}
      />
    </div>
  )

  const renderCardContent = useMemo(
    () => (
      <div>
        <FeedCardHeader
          feed={feed as unknown as IFeed}
          isSinglePost={false}
          isPostPage={false}
          userData={members[feed.user.id]}
          showPinnedMessageIndicator={showPinnedMessageIndicator}
          onHide={onHide}
          onShow={onShow}
          onPinMessage={() => alert('11')}
          extraHeaderComponents={extraHeaderComponents}
          onMessagePinnedSuccess={onMessagePinnedSuccess}
          onMessageUnpinnedSuccess={onMessageUnpinnedSuccess}
          membership={membership}
        />

        {!readonly ? (
          <Link
            href={`${membership.slug}/post/${feed.permalink || feed.id}`}
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              setOpenFeedDetails(true)
            }}
          >
            {renderInnerContent}
          </Link>
        ) : (
          renderInnerContent
        )}
      </div>
    ),
    [feed, hasPostBeenRead, hasCommentsUnread, members, onHide, onShow, showPinnedMessageIndicator],
  )

  return (
    <Card
      ref={rootEleRef}
      sx={cardStyles}
      data-post={feed.id}
      data-post-space={feed.cid}
      className="feed-card-condensed relative bg-white-500 p-6 dark:bg-black-500"
      onClick={
        readonly
          ? undefined
          : (e) => {
              e.preventDefault()

              setOpenFeedDetails(true)

              if (hasCommentsUnread) {
                const now = Math.floor(Date.now() / 1000)
                upsertFeedTrack({ feed_id: feed.id, updated_at: now })
                dispatch(addViewedFeed({ id: feed.id, updatedAt: now }))
              } else if (!hasPostBeenRead) {
                const now = Math.floor(Date.now() / 1000)
                upsertFeedTrack({ feed_id: feed.id, updated_at: now })
                dispatch(addViewedFeed({ id: feed.id, updatedAt: now }))
              }
            }
      }
    >
      <CardContent
        className="card-content"
        sx={{
          overflow: 'visible',
          overflowWrap: 'anywhere',
          pb: '0px !important',
          m: 0,
        }}
      >
        {renderCardContent}
        {openFeedDetails && (
          <FeedDetailsDialog
            membership={membership}
            open={true}
            feed={feed}
            onClose={handleClose}
            setOpenFeedDetails={setOpenFeedDetails}
            onPinMessage={onPinMessage}
            onUnpinMessage={onUnpinMessage}
          />
        )}
        {comments && comments.length > 0 && (
          <div className="feed-card-condensed-comments pt-5">
            <Separator className="mb-5" />
            {comments.map((comment: any) => (
              <CommentThread className="mb-4 last:mb-0" comment={comment} key={comment.id} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default FeedCardCondensed
