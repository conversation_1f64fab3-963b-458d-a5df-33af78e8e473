import Button from '@mui/material/Button'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import { makeStyles } from '@mui/styles'
import React, { useEffect, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { numberToCurrency } from '@memberup/shared/src/libs/numeric-utils'
import { getStripeBalanceTransactionsApi } from '@memberup/shared/src/services/apis/stripe.api'
import { STRIPE_BALANCE_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { IStripeAccount } from '@memberup/shared/src/types/interfaces'
import { selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    overflow: 'auto',
    maxHeight: 360,
    '& .MuiTableCell-head': {
      color: theme.palette.text.disabled,
    },
  },
}))

const EarningsTable: React.FC<{ connectedStripeAccount: IStripeAccount }> = ({ connectedStripeAccount }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const profile = useAppSelector((state) => selectUserProfile(state))
  const [requestEarnings, setRequestEarnings] = useState(false)
  const [allView, setAllView] = useState(false)
  const [earnings, setEarnings] = useState({
    data: [],
    hasMore: false,
    startingAfter: null,
  })

  const fetchMoreEarning = async (startingAfter?: string) => {
    try {
      if (requestEarnings) return
      setRequestEarnings(true)
      getStripeBalanceTransactionsApi(true, {
        accountId: connectedStripeAccount?.id,
        type: STRIPE_BALANCE_TYPE_ENUM.charge,
        limit: 6,
        starting_after: startingAfter,
      })
        .then((res) => {
          if (res.data.success) {
            setEarnings((prevValue) => ({
              data: startingAfter ? res.data.data.data : prevValue.data.concat(res.data.data.data),
              hasMore: res.data.data.has_more,
              startingAfter: res.data.data.data.length ? res.data.data.data[res.data.data.data.length - 1].id : null,
            }))
          }
        })
        .finally(() => {
          if (!mountedRef.current) return
          setRequestEarnings(false)
        })
    } catch (err: any) {
      if (!mountedRef.current) return
      setRequestEarnings(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }

  useEffect(() => {
    if (!connectedStripeAccount) return
    fetchMoreEarning()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [connectedStripeAccount])

  return (
    <div className={classes.root} id="earnings">
      <InfiniteScroll
        dataLength={earnings.data.length}
        next={() => fetchMoreEarning(earnings.startingAfter)}
        hasMore={earnings.hasMore && allView}
        loader={<div className="text-center">Loading...</div>}
        scrollableTarget="earnings"
      >
        <TableContainer>
          <Table stickyHeader aria-label="sticky table">
            <TableHead>
              <TableRow>
                <TableCell>Month</TableCell>
                <TableCell>Total Subscriptions</TableCell>
                <TableCell>Total Refunds</TableCell>
                <TableCell>Platform Fees</TableCell>
                <TableCell>Add-ons</TableCell>
                <TableCell>Earning</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {earnings.data.map((row, index) => (
                <TableRow key={`row-${index}`} hover role="checkbox">
                  <TableCell>{formatDate({ date: row.created * 1000, format: 'MMM yyyy' })}</TableCell>
                  <TableCell>
                    <b className="color01">{numberToCurrency(row.amount)}</b>
                  </TableCell>
                  <TableCell>
                    <b className="color01">{numberToCurrency(row.amount)}</b>
                  </TableCell>
                  <TableCell>
                    <b className="color01">{numberToCurrency(row.amount)}</b>
                  </TableCell>
                  <TableCell>
                    <b className="color01">{numberToCurrency(row.fee)}</b>
                  </TableCell>
                  <TableCell>
                    <b className="color01">{numberToCurrency(row.amount)}</b>
                  </TableCell>
                  <TableCell>
                    <b className="color01">{numberToCurrency(row.amount)}</b>
                  </TableCell>
                </TableRow>
              ))}
              {!requestEarnings && !earnings.data.length && (
                <TableRow>
                  <TableCell className="text-center" colSpan={5}>
                    No Earning
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </InfiniteScroll>
      {earnings.hasMore && !allView && (
        <>
          <br />
          <div className="text-center">
            <Button className="no-padding" variant="text" color="primary" onClick={() => setAllView(true)}>
              View all earnings
            </Button>
          </div>
        </>
      )}
    </div>
  )
}

export default EarningsTable
