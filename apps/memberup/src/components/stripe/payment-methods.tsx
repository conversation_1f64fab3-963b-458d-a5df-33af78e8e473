import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React from 'react'

import { AppPaymentMethodNumber } from '@memberup/shared/src/components/common/payment-method-number'
import { IUser } from '@memberup/shared/src/types/interfaces'

const useStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
  },
}))

const PaymentMethods: React.FC<{ user: IUser }> = ({ user }) => {
  const classes = useStyles()

  return (
    <div className={classes.root}>
      <Typography variant="subtitle1">
        <b>Payment Methods</b>
      </Typography>
      <br />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Grid container alignItems="center" spacing={2}>
            <Grid item></Grid>
            <Grid item xs>
              <AppPaymentMethodNumber number={4242} />
              <Typography variant="body2">
                <span className="color03">Expires March 2022</span>
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </div>
  )
}

export default PaymentMethods
