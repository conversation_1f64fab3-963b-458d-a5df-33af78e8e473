import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import Radio from '@mui/material/Radio'
import Slide, { SlideProps } from '@mui/material/Slide'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import React, { useMemo, useState } from 'react'

import { getFullName } from '@memberup/shared/src/libs/profile'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { useAppDispatch } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
    maxWidth: '100%',
    minWidth: 380,
    marginLeft: 'auto',
    textAlign: 'center',
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
    '& .MuiDialog-paperFullWidth': {
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      margin: 0,
    },
    '& .MuiDialogTitle-root .MuiTypography-h6': {
      fontSize: 16,
    },
    '& .MuiSelect-select': {
      paddingLeft: 8,
      paddingTop: 8,
      paddingBottom: 8,
    },
    '& .MuiInputBase-root': {
      width: '100%',
      paddingLeft: 8,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: '24px 24px 8px 24px',
  },
  dialogContent: {
    minHeight: 280,
    lineHeight: 1,
    padding: 24,
  },
  dialogAction: {
    backgroundColor: 'transparent',
  },
  subSection: {
    borderColor: theme.palette.divider,
    borderRadius: 12,
    borderStyle: 'solid',
    borderWidth: 1,
    padding: 32,
    position: 'relative',
  },
  activeSubSection: {
    borderWidth: 2,
  },
  radio: {
    position: 'absolute',
    left: 4,
    top: 4,
  },
}))

// eslint-disable-next-line react/display-name
const Transition = React.forwardRef<unknown, SlideProps>((props, ref) => (
  <Slide direction="left" {...props} ref={ref} />
))

const ChoosePlan: React.FC<{ open: boolean; onClose: () => void; member?: IUser }> = ({ member, open, onClose }) => {
  const classes = useStyles()
  const dispatch = useAppDispatch()
  const [reason, setReason] = useState('')
  const [requestBanMember, setRequestBanMember] = useState(false)
  const name = useMemo(
    () => getFullName(member?.first_name, member?.last_name),
    [member?.first_name, member?.last_name],
  )

  const handleSubmit = () => {}

  return (
    <Dialog
      maxWidth="xl"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      TransitionComponent={Transition}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="choose-plan-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="choose-plan-dialog-title">
        {/* Choose Plan
        <IconButton
          size="small"
          aria-label="close"
          className="close large color02"
          onClick={onClose}
        >
          <CloseIcon fontSize="inherit" />
        </IconButton> */}
      </DialogTitle>
      <DialogContent className={clsx(classes.dialogContent, 'color03')}>
        <div style={{ padding: 32, maxWidth: 746, margin: 'auto' }}>
          <Stepper alternativeLabel style={{ maxWidth: 300, margin: 'auto' }}>
            <Step active>
              <StepLabel className="color03">Choose Plan</StepLabel>
            </Step>
            <Step>
              <StepLabel>Payment</StepLabel>
            </Step>
          </Stepper>
          <br />
          <br />
          <br />
          <Typography className="bold text-center" variant="h4" gutterBottom style={{ fontSize: 24 }}>
            Choose Plan
          </Typography>
          <br />
          <Typography className="text-center" color="text.disabled" variant="body2">
            Setup custom pricing for a customer group or run pricing experiments.
          </Typography>
          <br />
          <br />
          <br />
          <Grid container alignItems="center" spacing={3}>
            <Grid item xs={6}>
              <div className={clsx(classes.subSection, classes.activeSubSection, 'background-color18')}>
                <Radio className={classes.radio} size="small" />
                <Typography color="text.disabled" variant="body2" gutterBottom>
                  Pay Yearly
                </Typography>
                <Typography className="bold" variant="h6" gutterBottom>
                  <span style={{ fontSize: 32 }}>$319</span>/mo
                </Typography>
                <Typography color="text.disabled" variant="body2" style={{ maxWidth: 236, margin: ' auto' }}>
                  All access to the club. No extra passwords to remember.
                </Typography>
              </div>
            </Grid>
            <Grid item xs={6}>
              <div className={clsx(classes.subSection)}>
                <Radio className={classes.radio} size="small" />
                <Typography color="text.disabled" variant="body2" gutterBottom>
                  Pay Monthly
                </Typography>
                <Typography className="bold" variant="h6" gutterBottom>
                  <span style={{ fontSize: 32 }}>$399</span>/mo
                </Typography>
                <Typography color="text.disabled" variant="body2" style={{ maxWidth: 236, margin: ' auto' }}>
                  All access to the club. No extra passwords to remember.
                </Typography>
              </div>
            </Grid>
          </Grid>
          <br />
          <br />
          <Divider />
          <br />
          <br />
          <Grid container spacing={3}>
            <Grid item xs={6}>
              <Typography className="bold text-left" variant="h5">
                Total per Year
              </Typography>
            </Grid>
            <Grid className="text-right" item xs={6}>
              <Typography className="bold" variant="h5">
                $3,828.00
              </Typography>
            </Grid>
          </Grid>
          <br />
          <br />
          <Button
            className="round-small"
            variant="contained"
            // disabled={!reason || requestBanMember}
            fullWidth
            onClick={() => onClose()}
          >
            {requestBanMember ? <CircularProgress size={16} /> : 'Continue to Payment'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ChoosePlan
