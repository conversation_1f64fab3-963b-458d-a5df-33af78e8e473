import CloseIcon from '@mui/icons-material/Close'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Divider from '@mui/material/Divider'
import IconButton from '@mui/material/IconButton'
import MenuItem from '@mui/material/MenuItem'
import Select from '@mui/material/Select'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import React, { useState } from 'react'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { banUserApi } from '@memberup/shared/src/services/apis/user.api'
import { IUser } from '@memberup/shared/src/types/interfaces'
import MemberListItem from '@/memberup/components/member/member-list-item'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
    '& .MuiDialogTitle-root .MuiTypography-h6': {
      fontSize: 16,
    },
    '& .MuiSelect-select': {
      paddingLeft: 8,
      paddingTop: 8,
      paddingBottom: 8,
    },
    '& .MuiInputBase-root': {
      width: '100%',
      paddingLeft: 8,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: '24px 24px 8px 24px',
  },
  dialogContent: {
    minHeight: 280,
    lineHeight: 1,
    padding: 24,
  },
  dialogAction: {
    backgroundColor: 'transparent',
  },
}))

const BanUser: React.FC<{ open: boolean; onClose: (banned: boolean) => void; user?: IUser }> = ({
  user,
  open,
  onClose,
}) => {
  const mountedRef = useMounted(true)
  const classes = useStyles()
  const [reason, setReason] = useState('')
  const [requestBanUser, setRequestBanUser] = useState(false)
  const firstName = user?.first_name || ''
  const name = getFullName(user?.first_name, user?.last_name)

  const handleSubmit = () => {
    setRequestBanUser(true)
    banUserApi(user.id, reason)
      .then(async (result) => {
        if (!mountedRef.current) return
        onClose(true)
      })
      .catch((err) => {})
      .finally(() => {
        if (!mountedRef.current) return
        setRequestBanUser(false)
      })
  }

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      aria-labelledby="assign-member-role-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="assign-member-role-dialog-title">
        Ban this Member?
        <IconButton size="small" aria-label="close" className="close large color02" onClick={() => onClose(false)}>
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent className={clsx(classes.dialogContent, 'color03')}>
        <MemberListItem
          image={user?.profile?.image || user?.image}
          cropArea={user?.profile?.image_crop_area}
          imageSize={40}
          name={firstName}
          primary={<Typography variant="body1">{name}</Typography>}
          secondary={<Typography variant="body2">Member since August 2021</Typography>}
        />
        <br />
        <br />
        <Typography variant="subtitle1" gutterBottom>
          Reason
        </Typography>
        <Select
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          inputProps={{ 'aria-label': 'Select a reason' }}
        >
          <MenuItem value="Reason1">Reason1</MenuItem>
          <MenuItem value="Reason2">Reason2</MenuItem>
        </Select>
        <br />
        <br />
        <br />
        <Divider />
        <br />
        <br />
        <Typography variant="body1">
          Banning {firstName} will remove any declined or pending subcription payments, prevent them from contacting you
          or anyone on your platform.&nbsp;
          {firstName} will be notified through email.
        </Typography>
        <br />
        <br />
        <Divider />
      </DialogContent>
      <DialogActions className={classes.dialogAction}>
        <Button
          className="round-small"
          variant="outlined"
          disabled={requestBanUser}
          fullWidth
          onClick={() => onClose(false)}
        >
          Cancel
        </Button>
        <Button
          className="round-small"
          variant="contained"
          disabled={!reason || requestBanUser}
          fullWidth
          onClick={() => handleSubmit()}
        >
          {requestBanUser ? <CircularProgress size={16} /> : 'Ban this Member'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default BanUser
