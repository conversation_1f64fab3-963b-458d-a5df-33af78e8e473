import { joi<PERSON>esolver } from '@hookform/resolvers/joi'
import { MoreHoriz } from '@mui/icons-material'
import { Box, Button, CircularProgress, FormControl, Grid, Menu, TextField, Typography } from '@mui/material'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import IconButton from '@mui/material/IconButton'
import ListItem from '@mui/material/ListItem'
import useTheme from '@mui/material/styles/useTheme'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Joi from 'joi'
import { useRouter } from 'next/navigation'
import React, { useEffect, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'

import VisuallyHiddenInput from '../../common/hidden-input'
import SVGPhoto from '../../svgs/photo'
import SVGPhotoAdd from '../../svgs/photo-add'
import { AppDropzone } from '@memberup/shared/src/components/common/app-dropzone'
import { createCourseApi } from '@memberup/shared/src/services/apis/content-library.api'
import { IMAGE_ACCEPT_ONLY } from '@memberup/shared/src/types/consts'
import AppWarning from '@/memberup/components/dialogs/warning'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import useUploadFiles from '@/memberup/components/hooks/use-upload-files'
import SVGClose from '@/memberup/components/svgs/close'
import InteractiveImageCropper from '@/memberup/components/ui/InteractiveImageCropper'
import { getContentLibrary } from '@/memberup/store/features/contentLibrarySlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { ContentLibrary } from '@/shared-types/types'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 8,
      maxWidth: 560,
      overflow: 'visible',
    },
  },
  inputFields: {
    height: 48,
    '& .MuiOutlinedInput-notchedOutline': {
      border: 'none',
    },
    '& .MuiOutlinedInput-input': {
      '&:focus': {
        border: '1px solid #4b4f57',
      },
    },
    '& .MuiInputBase-input': {
      boxSizing: 'border-box',
      height: '48px !important',
      fontFamily: 'Graphik Regular',
      fontSize: '14px',
      borderRadius: '12px !important',
      background: theme.palette.mode == 'dark' ? '#212126' : '#f2f3f4',
    },
    '& .MuiOutlinedInput-root': {
      borderRadius: '12px !important',
      height: '48px !important',
    },
  },
  multiInputFields: {
    '& .MuiOutlinedInput-notchedOutline': {
      border: 'none',
    },
    '& .MuiOutlinedInput-input': {
      '&:focus': {
        border: '1px solid #4b4f57',
      },
    },
    '& .MuiInputBase-input': {
      fontFamily: 'Graphik Regular',
      fontSize: '14px',
      borderRadius: '12px !important',
      padding: '12px 16px',
      background: theme.palette.mode == 'dark' ? '#212126' : '#f2f3f4',
    },
    '& .MuiOutlinedInput-root': {
      borderRadius: '12px !important',
      width: '100%',
      padding: '4px 0 5px',
    },
  },

  inputLabel: {
    fontSize: '14px',
    fontFamily: 'Graphik Medium',
    lineHeight: '16px',
    color: theme.palette.mode == 'dark' ? 'rgba(255, 255, 255, 0.87)' : 'rgba(0, 0, 0, 0.87)',
  },
  subLabel: {
    color: '#8D94A3',
    fontSize: '12px',
    fontFamily: 'Graphik Regular',
    fontWeight: '500',
  },
  inputSection: {
    height: 48,
    border: 'none',
    '& .MuiInputBase-input': {
      height: '100%',
      fontSize: '14px',
      fontFamily: 'Graphik Semibold',
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: 24,
  },
  dialogContent: {
    paddingLeft: 24,
    paddingRight: 24,
    paddingTop: 24,
    paddingBottom: 24,
  },
  textEditorWrapper: {
    borderColor: theme.palette.text.disabled,
    borderRadius: 4,
    borderStyle: 'solid',
    borderWidth: 1,
    height: 140,
    '&:focus': {
      borderColor: theme.palette.text.primary,
    },
  },
}))

type FormDataType = {
  title: string
  description?: string
}

const FormValue: FormDataType = {
  title: '',
  description: '',
}

const FormSchema = Joi.object({
  title: Joi.string().required().messages({
    'string.empty': `Title cannot be an empty field`,
  }),
  description: Joi.string().allow('').optional(),
}).options({ allowUnknown: true })

const CreateCourse: React.FC<{
  open: boolean
  onClose: () => void
  contentLibrary?: ContentLibrary
}> = ({ open, onClose, contentLibrary }) => {
  const classes = useStyles()
  const theme = useTheme()
  const { isDarkTheme } = useAppTheme()
  const [loading, setLoading] = useState(false)
  const [croppedImage, setCroppedImage] = useState(null)
  const [image, setImage] = useState(null)
  const [croppedArea, setCroppedArea] = useState(null)
  const [originalImageUrl, setOriginalImageUrl] = useState<string | null>(null)
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null)
  const [uploadZoom, setUploadZoom] = useState(1)
  const fileInputRef = useRef(null)
  const [menuAnchorEl, setMenuAnchorEl] = useState(null)
  const membership = useAppSelector((state) => state.membership.membership)
  const { uploadFiles, uploadProgress, uploadedFiles, handleUploadFiles } = useUploadFiles('library', membership.id)
  const [showWarning, setShowWarning] = useState(false)
  const [isFormTouched, setIsFormTouched] = useState(false)

  const { control, formState, handleSubmit, watch } = useForm<FormDataType>({
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    defaultValues: FormValue,
    resolver: joiResolver(FormSchema),
  })
  const descriptionValue = watch('description')
  const titleValue = watch('title')
  const router = useRouter()
  const dispatch = useAppDispatch()

  const isFormDirty = () => {
    return isFormTouched || croppedImage !== null
  }

  const handleFormChange = () => {
    setIsFormTouched(true)
  }

  const handleFormSubmit = async (data: FormDataType) => {
    setLoading(true)
    const getImageDimensions = (url) =>
      new Promise((resolve, reject) => {
        let img = new Image()
        img.onload = () => resolve({ width: img.width, height: img.height })
        img.onerror = reject
        img.src = url
      })
    try {
      if (croppedImage) {
        const responseCropped = await fetch(croppedImage)
        const blob = await responseCropped.blob()
        const file = new File([blob], 'cropped-image.jpg', { type: 'image/jpeg' })
        const originalDimensions = await getImageDimensions(croppedImage)
        const uploadedImage = await handleUploadFiles([file as any], 'Cloudinary')

        data.thumbnail = {
          ...uploadFiles[0],
          ...uploadedImage[0],
          originalImg: {
            url: originalImageUrl,
            dimensions: originalDimensions,
          },
          croppedImg: {
            url: uploadedImage[0].url,
            dimensions: originalDimensions,
            croppedArea,
            croppedAreaPixels,
            zoom: uploadZoom,
          },
        }
      }
      data.content_library_id = contentLibrary.id
      const result = await createCourseApi(data)
      dispatch(getContentLibrary(membership.slug))
      router.push(`/${membership.slug}/content/course-builder/${result.data.id}`)
      onClose()
    } catch (error) {
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (uploadProgress === 100) {
      if (uploadedFiles.length) {
        setImage(uploadedFiles[0])
      }
    }
  }, [uploadedFiles, uploadProgress])

  const handleDialogClose = (event, reason) => {
    if (isFormDirty()) {
      // If the form is dirty, show the warning
      setShowWarning(true)
    } else {
      // If the form is not dirty, close the dialog
      onClose()
    }
  }

  const onImageCropped = (croppedImageFile, croppedAreaPixels, croppedArea, zoom) => {
    setCroppedImage(croppedImageFile)
    handleFormChange()
    setOriginalImageUrl(image?.url)
    setCroppedArea(croppedArea)
    setCroppedAreaPixels(croppedAreaPixels)
    setUploadZoom(zoom)
    setLoading(false)
  }
  const handleDropFile = async (f) => {
    setLoading(true)
    await handleUploadFiles([f], 'Cloudinary')
    setLoading(false)
  }

  const handleRemoveThumbnail = () => {
    setImage(null)
  }

  const handleConfirmNavigation = () => {
    setShowWarning(false)
    onClose()
  }

  const handleCancelNavigation = () => {
    setShowWarning(false)
  }

  return (
    <>
      <Dialog
        maxWidth="sm"
        fullWidth={true}
        className={classes.root}
        open={open}
        onClose={handleDialogClose}
        TransitionProps={{
          in: open,
          timeout: {
            appear: 800,
            enter: 800,
            exit: 500,
          },
        }}
        aria-labelledby="new-content-dialog-title"
      >
        <DialogTitle className={classes.dialogTitle} id="new-content-dialog-title">
          New Course
          <IconButton size="small" aria-label="close" className="close" onClick={handleDialogClose}>
            <SVGClose />
          </IconButton>
        </DialogTitle>
        <DialogContent className={classes.dialogContent}>
          <form onSubmit={handleSubmit(handleFormSubmit)}>
            <div style={{ marginBottom: '24px' }}>
              <Grid container justifyContent="space-between" alignItems="center">
                <Grid item>
                  <Typography className={classes.inputLabel} gutterBottom>
                    Title
                  </Typography>
                </Grid>
                <Grid item>
                  <p className={classes.subLabel}>{40 - (titleValue?.length ? titleValue?.length : 0)}</p>
                </Grid>
              </Grid>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                    <TextField
                      className={classes.inputFields}
                      name="title"
                      placeholder="Enter Title"
                      size="small"
                      InputProps={{
                        inputProps: { maxLength: 40 },
                      }}
                      error={Boolean(error)}
                      helperText={error?.message}
                      value={value}
                      onChange={(e) => {
                        onChange(e.target.value)
                        handleFormChange()
                      }}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="title"
              />
            </div>
            <div style={{ marginBottom: '24px' }}>
              <Grid container justifyContent="space-between" alignItems="center">
                <Grid item>
                  <Typography className={classes.inputLabel} gutterBottom>
                    Description
                  </Typography>
                </Grid>
                <Grid item>
                  <p className={classes.subLabel}>{95 - (descriptionValue?.length ? descriptionValue?.length : 0)}</p>
                </Grid>
              </Grid>
              <Controller
                name="description"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <TextField
                    className={classes.multiInputFields}
                    {...field}
                    placeholder="Course description..."
                    size="small"
                    variant="outlined"
                    multiline
                    rows={5}
                    InputProps={{
                      inputProps: { maxLength: 95 },
                    }}
                    fullWidth
                    name="description"
                    onChange={(e) => {
                      field.onChange(e)
                      handleFormChange()
                    }}
                  />
                )}
              />
            </div>
            <div style={{ marginBottom: '24px' }}>
              <Typography variant="body1" color="inherit" gutterBottom className={classes.inputLabel}>
                Thumbnail
              </Typography>
              <Box
                className="background-color18 border-color02"
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: 250,
                  width: '100%',
                  borderRadius: '12px',
                  borderStyle: 'dashed',
                  borderWidth: image?.url ? '0px' : '1px',
                }}
              >
                {image?.url ? (
                  <Box sx={{ position: 'relative' }}>
                    <InteractiveImageCropper
                      image={image?.url}
                      onImageCropped={onImageCropped}
                      onCancel={() => {
                        setCroppedImage(null)
                        setImage(null)
                      }}
                      height={'260px'}
                      width={'511px'}
                      aspectRatioOverride={511 / 260}
                    />
                    <Button
                      component="label"
                      variant="outlined"
                      sx={{
                        zIndex: 1,
                        position: 'absolute',
                        bottom: '8px',
                        height: '32px',
                        right: '44px',
                        backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
                        borderColor: isDarkTheme ? '#F3F5F5' : 'rgb(23, 23, 26)',
                        borderRadius: 12,
                        minHeight: '32px',
                        '&:hover': {
                          backgroundColor: isDarkTheme ? '#29292c' : '#F3F5F5',
                          borderColor: isDarkTheme ? '#F3F5F5' : '#29292c',
                        },
                      }}
                    >
                      <Typography
                        sx={{
                          fontSize: '13px',
                          fontFamily: 'Graphik Medium',
                          color: theme.palette.text.primary,
                          lineHeight: '16px',
                        }}
                      >
                        <span
                          style={{
                            marginRight: '8px',
                          }}
                        >
                          <SVGPhoto width={13} height={13} />
                        </span>
                        Replace
                      </Typography>
                      <VisuallyHiddenInput
                        ref={fileInputRef}
                        onChange={(e) => {
                          //get file
                          if (e.target.files) {
                            handleDropFile(e.target.files[0])
                          }
                        }}
                        type="file"
                      />
                    </Button>
                    <IconButton
                      sx={{
                        zIndex: 1,
                        position: 'absolute',
                        bottom: '8px',
                        right: '8px',
                        margin: '0px',
                        height: '32px',
                        width: '32px',
                        backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
                        borderColor: isDarkTheme ? '#ffffff !important' : 'rgb(23, 23, 26)',
                        borderRadius: 12,
                        minHeight: '32px',
                        minWidth: '32px',
                        border: '1px solid',
                        color: theme.palette.text.disabled,
                        '&:hover': {
                          backgroundColor: isDarkTheme ? '#29292c' : '#F3F5F5',
                          borderColor: isDarkTheme ? '#ffffff' : '#29292c',
                        },
                      }}
                      onClick={(e) => {
                        e.stopPropagation()
                        setMenuAnchorEl(e.currentTarget)
                      }}
                    >
                      <MoreHoriz />
                    </IconButton>
                    <Menu
                      anchorEl={menuAnchorEl}
                      keepMounted
                      open={Boolean(menuAnchorEl)}
                      onClose={() => setMenuAnchorEl(null)}
                      sx={{
                        '& .MuiPaper-root': {
                          borderRadius: '12px',
                          boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                          border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                          backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                          backgroundImage: 'unset',
                          padding: '6px',
                        },
                        '& .MuiList-root': {
                          padding: '0px',
                        },
                      }}
                    >
                      <ListItem
                        sx={{
                          justifyContent: 'left',
                          fontFamily: 'unset',
                          height: '40px',
                          margin: '0px',
                          cursor: 'pointer',
                          '&:hover': {
                            backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                            borderRadius: '12px',
                          },
                        }}
                        onClick={() => {
                          setMenuAnchorEl(null)
                          handleRemoveThumbnail()
                        }}
                      >
                        Remove
                      </ListItem>
                    </Menu>
                  </Box>
                ) : (
                  <AppDropzone
                    width={550}
                    height={300}
                    file={image}
                    accept={IMAGE_ACCEPT_ONLY}
                    onDropFile={handleDropFile}
                    hideImage={true}
                    placeholder={
                      <Box className="text-center">
                        <Box color={theme.palette.primary.main} marginBottom={'14px'} className="flex justify-center">
                          <SVGPhotoAdd width={20} height={20} />
                        </Box>
                        <Typography
                          sx={{
                            fontSize: '14px',
                            fontFamily: 'Graphik Medium',
                            color: theme.palette.text.primary,
                            lineHeight: '16px',
                          }}
                          marginBottom={'8px'}
                        >
                          Add Thumbnail
                        </Typography>
                        <Typography
                          sx={{
                            fontSize: '12px',
                            fontFamily: 'Graphik Regular',
                            lineHeight: '16px',
                            color: theme.palette.text.disabled,
                          }}
                        >
                          Recommended size <br />
                          1120px x 570px
                        </Typography>
                      </Box>
                    }
                  />
                )}
              </Box>
            </div>
            <div style={{ marginBottom: '24px' }}></div>
            <div>
              <Button
                className="round-small"
                variant="contained"
                color="primary"
                sx={{ color: '#fff' }}
                disabled={!formState.isValid || loading}
                fullWidth
                type="submit"
              >
                {loading ? <CircularProgress size={24} /> : 'Create Course'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
      {showWarning && (
        <AppWarning
          title="Unsaved Changes"
          content="You have unsaved changes. Are you sure you want to leave?"
          cancelButtonText="Cancel"
          proceedButtonText="Leave"
          open={showWarning}
          onProceed={handleConfirmNavigation}
          onClose={handleCancelNavigation}
        />
      )}
    </>
  )
}

export default CreateCourse
