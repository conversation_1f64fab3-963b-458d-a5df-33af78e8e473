import CloseIcon from '@mui/icons-material/Close'
import SimCardDownloadOutlinedIcon from '@mui/icons-material/SimCardDownloadOutlined'
import Box from '@mui/material/Box'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import List from '@mui/material/List'
import ListItemButton from '@mui/material/ListItemButton'
import Stack from '@mui/material/Stack'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import { saveAs } from 'file-saver'
import dynamic from 'next/dynamic'
import React, { useEffect, useMemo, useRef, useState } from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { AppVideo } from '@memberup/shared/src/components/common/media/video'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'

const AppMuxPlayer = dynamic(() => import('@memberup/shared/src/components/common/app-mux-player'), {
  ssr: false,
})

const useStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
    '& .MuiDialog-container': {
      alignItems: 'unset',
    },
    '& .MuiDialog-paper': {
      backgroundColor: 'transparent',
      boxShadow: 'none',
    },
    '& .MuiDialog-paperFullWidth': {
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      maxWidth: 1600,
      margin: 'auto',
    },
  },
  dialogContent: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 320,
    width: '100%',
    margin: 'auto',
    padding: '12px',
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: '32px 24px 8px 24px',
  },
}))

const AssetsDialog: React.FC<{
  open: boolean
  asset: any
  otherAssets: any[]
  onClose: () => void
}> = ({ open, asset, otherAssets, onClose }) => {
  const mountedRef = useMounted(true)
  const muxPlayerWrapper = useRef(null)
  const classes = useStyles()
  const theme = useTheme()
  const [activeAsset, setActiveAsset] = useState(asset)
  const [muxPlayerWrapperHeight, setMuxPlayerWrapperHeight] = useState(0)

  const handleResize = () => {
    if (mountedRef.current) {
      setMuxPlayerWrapperHeight(muxPlayerWrapper.current?.clientHeight || 0)
    }
  }

  useEffect(() => {
    if (!window?.addEventListener) return

    window.addEventListener('resize', handleResize)
    handleResize()

    return () => window?.removeEventListener('resize', handleResize)
  }, [])

  const handleDownload = (asset) => {
    if (!asset.pdf) return
    saveAs(asset.pdf, `${asset.download_title}.pdf`)
  }

  const renderActiveAsset = useMemo(() => {
    if (activeAsset?.mux_asset) {
      return (
        <AppMuxPlayer
          autoPlay={true}
          asset={activeAsset.mux_asset}
          streamType="on-demand"
          wrapperStyle={{ width: '100%', height: '100%' }}
          playerStyle={{ width: '100%', height: '100%' }}
          onEnded={(e) => {
            if (otherAssets?.length) {
              const tempIndex = otherAssets.findIndex((o) => o === activeAsset) + 1
              if (tempIndex < otherAssets.length - 1) setActiveAsset(otherAssets[tempIndex])
            }
          }}
          onResize={(e) => handleResize()}
        />
      )
    }

    if (activeAsset?.url) {
      return <AppVideo url={activeAsset.url} />
    }

    if (activeAsset?.thumbnails) {
      return (
        <AppImg
          src={activeAsset.thumbnails}
          alt={activeAsset.title}
          width={356}
          height={580}
          style={{ width: '100%', height: '100%' }}
        />
      )
    }

    return null
  }, [activeAsset])

  return (
    <Dialog
      maxWidth="lg"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      aria-labelledby="asset-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="asset-dialog-title">
        <IconButton size="small" aria-label="close" className="close large" sx={{ color: 'white' }} onClick={onClose}>
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent id="asset-dialog-content" className={classes.dialogContent}>
        <Box sx={{ maxWidth: 1200, width: '100%', margin: 'auto' }}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Box sx={{ position: 'relative' }}>
                <Grid container>
                  <Grid item xs>
                    <Box
                      ref={muxPlayerWrapper}
                      sx={{
                        borderRadius: 4,
                        lineHeight: 0,
                        overflow: 'hidden',
                      }}
                    >
                      {renderActiveAsset}
                    </Box>
                  </Grid>
                  {/*  <Grid
                    item
                    sx={{
                      display: { xs: 'none', md: 'block' },
                    }}
                  >
                    <Box
                      sx={{
                        backgroundColor: theme.palette.background.paper,
                        borderRadius: muxPlayerWrapperHeight ? 4 : 0,
                        height: muxPlayerWrapperHeight,
                        width: 320,
                        overflow: 'auto',
                        p: muxPlayerWrapperHeight ? 2 : 0,
                      }}
                    >
                      <List sx={{ p: 0 }}>
                        {otherAssets?.map((item, index) => (
                          <ListItemButton key={`asset-${index}`} sx={{ p: 2, borderRadius: 2 }}>
                            <LibraryCard
                              library={item}
                              editable={false}
                              layout="list"
                              maxTitleLength={54}
                              onClickLibrary={() => {
                                setActiveAsset(item)
                              }}
                            />
                          </ListItemButton>
                        ))}
                      </List>
                    </Box>
                  </Grid> */}
                </Grid>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Box
                sx={{
                  backgroundColor: theme.palette.background.paper,
                  borderRadius: 4,
                  p: 4,
                }}
              >
                <Grid container spacing={3}>
                  <Grid item xs={12} md={true}>
                    <Typography color="text.disabled" variant="body2" gutterBottom>
                      Getting Started
                    </Typography>
                    <Typography variant="h6" sx={{ mb: 3 }}>
                      {activeAsset.title}
                    </Typography>
                    <Typography color="text.disabled" variant="body1" sx={{ maxHeight: 240, overflowY: 'auto' }}>
                      {activeAsset.description}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md="auto">
                    <Box
                      sx={{
                        backgroundColor: theme.palette.divider,
                        borderRadius: 3,
                        cursor: 'pointer',
                        p: 2,
                        width: 328,
                      }}
                      onClick={() => handleDownload(activeAsset)}
                    >
                      <Stack alignItems="center" direction="row" spacing={2}>
                        <Box
                          className="d-flex align-center background-color18 justify-center"
                          sx={{
                            borderRadius: 3,
                            maxWidth: 48,
                            maxHeightL: 48,
                            minHeight: 48,
                          }}
                        >
                          <SimCardDownloadOutlinedIcon fontSize="large" />
                        </Box>
                        <Box>
                          <Typography variant="body1">
                            <b>{activeAsset.download_title}</b>
                          </Typography>
                        </Box>
                      </Stack>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
    </Dialog>
  )
}

AssetsDialog.displayName = 'AssetsDialog'

export default AssetsDialog
