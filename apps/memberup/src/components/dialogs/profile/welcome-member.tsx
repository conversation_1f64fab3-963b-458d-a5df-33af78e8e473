import CloseIcon from '@mui/icons-material/Close'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import LinearProgress from '@mui/material/LinearProgress'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import React from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { selectMembershipAssetsPath } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
      backgroundColor: theme.palette.text.primary,
      maxWidth: 550,
    },
    textAlign: 'center',
  },
  dialogTitle: {
    borderBottom: 'none',
  },
  dialogContent: {
    minHeight: 320,
    lineHeight: 1,
    padding: 24,
    '&.MuiDialogContent-root': {
      paddingTop: 24,
    },
    '& img': {
      borderRadius: 16,
    },
  },
  title: {
    fontSize: 20,
  },
  profileImage: {
    width: 400,
    height: 284,
  },
}))

const WelcomeMember: React.FC<{
  open: boolean
  onContinue: () => void
  onClose: () => void
}> = ({ open, onContinue, onClose }) => {
  const classes = useStyles()
  const membershipAssetsPath = useAppSelector((state) => selectMembershipAssetsPath(state))

  return (
    <Dialog
      maxWidth="sm"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="library-layout-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="library-layout-dialog-title">
        <IconButton size="small" aria-label="close" className="close large color03" onClick={onClose}>
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent className={clsx(classes.dialogContent, 'color03')}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <AppImg
              className={classes.profileImage}
              height={100}
              width={100}
              src={`${membershipAssetsPath}/images/profile.png`}
              alt="Profile Picture"
            />
          </Grid>
          <Grid item xs={12}>
            <Typography className={classes.title} variant="h5" color="secondary">
              Welcome to Daddy Issues
              <br />
              memeber comunity
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography variant="body1" color="inherit">
              Connect with members message to get to know members,
              <br />
              share thoughts or discuss ideas.
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Button
              className="app-button round-small"
              variant="contained"
              color="primary"
              fullWidth
              onClick={onContinue}
            >
              Continue
            </Button>
          </Grid>
          <Grid item xs={12}>
            <LinearProgress variant="determinate" value={20} />
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default WelcomeMember
