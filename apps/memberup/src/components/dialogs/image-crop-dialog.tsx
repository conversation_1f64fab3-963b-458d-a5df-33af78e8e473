import LoadingButton from '@mui/lab/LoadingButton'
import { Box } from '@mui/material'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import useTheme from '@mui/material/styles/useTheme'
import React, { useEffect, useState } from 'react'

import InteractiveImageCropper from '../ui/InteractiveImageCropper'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { useStore } from '@/hooks/useStore'
import useUploadFiles from '@/memberup/components/hooks/use-upload-files'

const ImageCropDialog = ({
  open,
  onClose,
  onSaved,
  setImage,
  image,
  aspectRatio,
  initialCroppedArea,
  onCancel,
  placeholderHeight,
  placeholderWidth,
  dialogCustomStyles = {},
}) => {
  const [croppedImage, setCroppedImage] = useState(null)

  const [isSaving, setIsSaving] = useState(false)
  const theme = useTheme()
  const membership = useStore((state) => state.community.membership)
  const { uploadedFiles, uploadProgress, handleUploadFiles } = useUploadFiles('library', membership.id)

  const handleImageCropped = (croppedImage, croppedAreaPixels, croppedArea, zoom) => {
    setCroppedImage({
      croppedImage,
      croppedAreaPixels,
      croppedArea,
      zoom,
    })
  }

  const handleSave = async () => {
    // Handle the cropped image (e.g., upload it to a server)
    setIsSaving(true)
    const responseCropped = await fetch(croppedImage.croppedImage)
    const blob: any = await responseCropped.blob()
    const file = new File([blob], 'cropped-image.jpg', { type: 'image/jpeg' })
    const uploadedImage = await handleUploadFiles([file as any], 'Cloudinary')
    // Close the dialog

    onSaved({
      ...uploadedImage[0],
      originalImg: {
        url: image,
        dimensions: { width: blob.width, height: blob.height },
      },
      croppedImg: {
        url: uploadedImage[0].url,
        dimensions: { width: blob.width, height: blob.height },
        croppedArea: croppedImage.croppedArea,
        croppedAreaPixels: croppedImage.croppedAreaPixels,
        zoom: croppedImage.zoom,
      },
    })
    setIsSaving(false)
  }
  useEffect(() => {
    if (uploadProgress === 100) {
      setImage(uploadedFiles[0])
    }
  }, [uploadedFiles, uploadProgress])

  return (
    <Dialog open={open} onClose={onClose} sx={dialogCustomStyles}>
      <Box sx={{ width: '600px' }}>
        <DialogTitle sx={{ border: 'none' }}>Adjust Image</DialogTitle>
        <DialogContent>
          {image && (
            <InteractiveImageCropper
              image={image}
              onImageCropped={handleImageCropped}
              onCancel={() => {
                setCroppedImage(null)
                setImage(null)
                onClose()
              }}
              aspectRatioOverride={aspectRatio}
              initialCroppedArea={initialCroppedArea}
              height={placeholderHeight}
              width={placeholderWidth}
            />
          )}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', width: '100%', mt: '16px' }}>
            <Button
              sx={{
                borderRadius: '10px',
                width: '100px',
              }}
              onClick={onCancel}
            >
              Cancel
            </Button>
            <LoadingButton
              loading={isSaving}
              variant="contained"
              sx={{
                borderRadius: '10px',
                color: '#fff',
                width: '100px',
                backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
              }}
              onClick={handleSave}
            >
              Save
            </LoadingButton>
          </Box>
        </DialogContent>
      </Box>
    </Dialog>
  )
}

export default ImageCropDialog
