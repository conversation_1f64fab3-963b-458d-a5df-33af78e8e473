import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import FormHelperText from '@mui/material/FormHelperText'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import List from '@mui/material/List'
import ListItem from '@mui/material/ListItem'
import ListItemButton from '@mui/material/ListItemButton'
import useTheme from '@mui/material/styles/useTheme'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import _debounce from 'lodash/debounce'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import type { UserResponse } from 'stream-chat'
import { useChannelStateContext, useChatContext } from 'stream-chat-react'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { THEME_MODE_ENUM, USER_ROLE_ENUM, USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import SVGTrash from '@/memberup/components/svgs/trash'
import SVGVerified from '@/memberup/components/svgs/verified'
import { requestRefreshChannels } from '@/memberup/store/features/inboxSlice'
import { setActiveMember } from '@/memberup/store/features/memberSlice'
import { openDialog } from '@/memberup/store/features/uiSlice'
import { useAppDispatch } from '@/memberup/store/hooks'

const AppMessagingEditChannel: React.FC<{ open: boolean; onClose: () => void }> = ({ open, onClose }) => {
  const dispatch = useAppDispatch()
  const theme = useTheme()
  const mountedRef = useMounted(true)
  const { client: streamChatClient } = useChatContext()
  const { channel } = useChannelStateContext()
  const channelMembersRef = useRef({})
  const [inputChannelName, setInputChannelName] = useState(channel?.data?.name || '')
  const [requestUpdateChannel, setRequestUpdateChannel] = useState(false)
  const [searchStr, setSearchStr] = useState('')
  const [membersToAdd, setMembersToAdd] = useState<UserResponse[]>([])
  const [memberIdsToDelete, setMemberIdsToDelete] = useState<string[]>([])
  const [members, setMembers] = useState<UserResponse[]>([])
  const [loading, setLoading] = useState(false)
  const [openNewMembers, setOpenNewMembers] = useState(false)
  const [errorMessage, setErrorMessage] = useState('Test')
  const isLightTheme = theme.palette.mode === THEME_MODE_ENUM.light
  const canUpdateChnnelMembers =
    Array.isArray(channel.data?.own_capabilities) && channel.data.own_capabilities.includes('update-channel-members')

  const findMembers = async (searchStr: string) => {
    if (!streamChatClient?.userID || loading) return
    setMembers([])
    setLoading(true)
    const filterConditions: any = [
      { id: { $nin: Object.keys(channelMembersRef.current || {}) } },
      {
        status: {
          $nin: [
            USER_STATUS_ENUM.banned,
            USER_STATUS_ENUM.deleted,
            USER_STATUS_ENUM.inactive,
            USER_STATUS_ENUM.invited,
          ],
        },
      },
    ]
    if (searchStr) {
      filterConditions.push({ name: { $autocomplete: searchStr } })
    }
    streamChatClient
      .queryUsers(
        {
          $and: filterConditions,
        },
        { id: 1 },
        { limit: 100 },
      )
      .then((res) => {
        if (mountedRef.current) {
          setMembers(res.users as unknown as UserResponse[])
        }
      })
      .catch((err) => {})
      .finally(() => {
        if (mountedRef.current) {
          setLoading(false)
        }
      })
  }

  const debouncedFindMembers = useCallback(
    _debounce(findMembers, 300, {
      trailing: true,
    }),
    [],
  )

  useEffect(() => {
    if (!mountedRef.current) return
    channelMembersRef.current = channel.state.members
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [channel.state.members])

  useEffect(() => {
    if (canUpdateChnnelMembers) {
      debouncedFindMembers(searchStr)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [canUpdateChnnelMembers, searchStr])

  const dispMembers = useMemo(() => {
    const memberIdsToAdd = membersToAdd.map((item) => item.id)
    return members.filter((item) => !memberIdsToAdd.includes(item.id))
  }, [membersToAdd, members])

  const dispChannelMembers = useMemo(() => {
    const temp = Object.values(channel.state.members || {})
    return temp.filter((item) => item.user_id !== streamChatClient?.userID && !memberIdsToDelete.includes(item.user_id))
  }, [memberIdsToDelete, channel.state.members])

  useEffect(() => {
    if (!mountedRef.current) return
    if (!dispChannelMembers.length && !membersToAdd.length) {
      setErrorMessage('The chat should have one or more members.')
    } else {
      setErrorMessage('')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [membersToAdd, dispChannelMembers])

  const handleUpdateChannel = async () => {
    if (!dispChannelMembers.length && !membersToAdd.length) return
    setRequestUpdateChannel(true)
    const promises = []

    promises.push(
      new Promise((resolve) => {
        channel
          .updatePartial({
            set: {
              name: inputChannelName,
            },
          })
          .then((res) => {
            if (mountedRef.current) {
              resolve(true)
            }
          })
          .catch((err) => {
            console.error(err)
            if (mountedRef.current) {
              resolve(true)
            }
          })
      }),
    )

    if (membersToAdd.length) {
      promises.push(
        new Promise((resolve) => {
          channel
            .addMembers(
              membersToAdd.map((item) => ({ user_id: item.id, channel_role: 'channel_member' })),
              { text: `${membersToAdd.map((item) => item.name).join(', ')} joined the chat` },
            )
            .then((res) => {
              if (mountedRef.current) {
                resolve(true)
              }
            })
            .catch((err) => {
              if (mountedRef.current) {
                resolve(true)
              }
            })
        }),
      )
    }
    if (memberIdsToDelete.length) {
      promises.push(
        new Promise((resolve) => {
          channel
            .removeMembers(memberIdsToDelete, {
              text: `${memberIdsToDelete.map((id) => channel.state.members[id].user.name).join(', ')} left the channel`,
            })
            .then((res) => {
              if (mountedRef.current) {
                resolve(true)
              }
            })
            .catch((err) => {
              console.error(err)
              if (mountedRef.current) {
                resolve(true)
              }
            })
        }),
      )
    }

    Promise.all(promises)
      .then(() => {
        if (mountedRef.current) {
          dispatch(requestRefreshChannels())
          onClose()
        }
      })
      .catch((err) => {
        console.error(err.message)
        if (mountedRef.current) {
          setRequestUpdateChannel(false)
        }
      })
  }

  const handleClickUser = (streamUser) => {
    if (streamUser?.id) {
      const { id, name, image, image_crop_area, role } = streamUser
      const temp = (name || '').split(' ')
      dispatch(
        setActiveMember({
          id: id as string,
          first_name: temp[0] || '',
          last_name: temp[1] || '',
          email: '',
          image,
          image_crop_area,
          role,
        }),
      )
      dispatch(openDialog({ dialog: 'UserProfile', open: true, props: {} }))
    }
  }

  const isAdminOrOwner = (role: string) => {
    return [USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner, 'global_admin'].includes(role as any)
  }

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      open={open}
      onClose={onClose}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: '12px',
          maxWidth: 456,
          overflow: 'visible',
        },
      }}
      aria-labelledby="create-channel-dialog-title"
    >
      <DialogTitle
        id="create-channel-dialog-title"
        sx={{
          borderBottom: 'none',
          p: '24px',
        }}
      >
        Chat Settings
      </DialogTitle>
      <DialogContent
        sx={{
          overflow: 'visible',
          p: '24px',
          '& .MuiOutlinedInput-root': {
            '& .MuiOutlinedInput-input': {
              py: '14px',
            },
            '& .MuiOutlinedInput-notchedOutline': {
              ...theme.components.MuiCssBaseline.styleOverrides['body']['& .background-color18'],
              borderColor: 'transparent!important',
              borderRadius: '12px',
            },
          },
        }}
        onClick={() => setOpenNewMembers(false)}
      >
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Grid container>
              <Grid item xs>
                <Typography sx={{ fontWeight: 700 }} variant="subtitle1" color="text.secondary" gutterBottom>
                  Chat Name
                </Typography>
              </Grid>
              <Grid item>
                <Typography
                  gutterBottom
                  sx={{
                    color: theme.palette.mode === 'dark' ? theme.palette.action.disabled : '#000',
                  }}
                >
                  {40 - inputChannelName.length}
                </Typography>
              </Grid>
            </Grid>
            <TextField
              placeholder="Enter a new name for the chat"
              variant="outlined"
              size="small"
              fullWidth
              value={inputChannelName}
              onChange={(e) => {
                if (e.target.value.length < 41) {
                  setInputChannelName(e.target.value)
                }
              }}
              InputProps={{
                style: {
                  color: isLightTheme ? '#000' : '#dce8ff',
                },
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <Typography sx={{ fontWeight: 700 }} variant="subtitle1" color="text.secondary" gutterBottom>
              Members
            </Typography>
            <TextField
              placeholder="Start typing to add member..."
              variant="outlined"
              size="small"
              fullWidth
              value={searchStr}
              onChange={(e) => {
                setSearchStr(e.target.value)
              }}
              onClick={(e) => {
                if (canUpdateChnnelMembers) {
                  e.stopPropagation()
                  setOpenNewMembers(true)
                }
              }}
              InputProps={{
                style: {
                  color: isLightTheme ? '#000' : '#dce8ff',
                },
              }}
            />
            <Box sx={{ position: 'relative' }}>
              {openNewMembers && dispMembers.length > 0 && (
                <List
                  sx={{
                    ...theme.components.MuiCssBaseline.styleOverrides['body']['& .border01'],
                    backgroundColor: isLightTheme ? 'white' : 'rgb(23, 23, 26)',
                    borderRadius: '12px',
                    maxHeight: 298,
                    overflowY: 'auto',
                    mt: '12px',
                    p: '2px!important',
                    position: 'absolute',
                    left: 12,
                    width: 312,
                    minWidth: 120,
                    '& .MuiListItem-root': {
                      borderRadius: '12px',
                      '&:hover': {
                        backgroundColor: !isLightTheme && 'rgba(141, 148, 163, 0.04)',
                      },
                    },
                    zIndex: 1,
                  }}
                >
                  {dispMembers.map((item, index) => (
                    <ListItemButton
                      key={item.id}
                      sx={{ p: 2, borderRadius: 3 }}
                      onClick={() => {
                        setMembersToAdd([item, ...membersToAdd])
                      }}
                    >
                      <Grid container alignItems="center" spacing={2}>
                        <Grid item>
                          <AppProfileImage
                            key={item.id}
                            imageUrl={item.profile?.['image'] || item.image}
                            cropArea={item.profile?.['image_crop_area'] || item.image_crop_area}
                            name={item.name || ''}
                            size={40}
                          />
                        </Grid>
                        <Grid item>
                          <Typography className="font-family-graphik-medium text-ellipsis">
                            {item.name || ''}
                          </Typography>
                        </Grid>
                        <Grid item xs sx={{ display: 'flex' }}>
                          {isAdminOrOwner(item.role) && (
                            <SVGVerified width={16} height={16} styles={{ marginTop: -2, marginLeft: -5 }} />
                          )}
                        </Grid>
                      </Grid>
                    </ListItemButton>
                  ))}
                  {memberIdsToDelete.map((id, index) => (
                    <ListItemButton
                      key={channel.state.members[id].user_id}
                      sx={{ p: 2, borderRadius: 3 }}
                      onClick={() => {
                        setMemberIdsToDelete(memberIdsToDelete.filter((m) => m !== id))
                      }}
                    >
                      <Grid container alignItems="center" spacing={2}>
                        <Grid item>
                          <AppProfileImage
                            key={channel.state.members[id].user_id}
                            imageUrl={
                              channel.state.members[id]?.user?.profile?.['image'] ||
                              channel.state.members[id]?.user?.image
                            }
                            cropArea={
                              channel.state.members[id]?.user?.profile?.['image_crop_area'] ||
                              channel.state.members[id]?.user?.image_crop_area
                            }
                            name={channel.state.members[id]?.user?.name || ''}
                            size={40}
                          />
                        </Grid>
                        <Grid item>
                          <Typography className="font-family-graphik-medium text-ellipsis">
                            {channel.state.members[id]?.user?.name || ''}
                          </Typography>
                        </Grid>
                        <Grid item xs sx={{ display: 'flex' }}>
                          {isAdminOrOwner(channel.state.members[id]?.user?.role) && (
                            <SVGVerified width={16} height={16} styles={{ marginTop: -2, marginLeft: -5 }} />
                          )}
                        </Grid>
                      </Grid>
                    </ListItemButton>
                  ))}
                </List>
              )}
            </Box>
          </Grid>
          {(membersToAdd.length > 0 || dispChannelMembers.length > 0) && (
            <Grid item xs={12}>
              <List
                sx={{
                  py: 0,
                  maxHeight: 296,
                  overflowY: 'auto',
                  '& .MuiListItem-root:hover': {
                    backgroundColor: 'transparent',
                  },
                }}
              >
                {membersToAdd.map((item, index) => (
                  <ListItem key={item.id || index} sx={{ pl: 0, borderRadius: '12px' }}>
                    <Grid container alignItems="center" spacing={2}>
                      <Grid item>
                        <AppProfileImage
                          key={item.id || index}
                          imageUrl={item.profile?.['image'] || item.image}
                          cropArea={item.profile?.['image_crop_area'] || item.image_crop_area}
                          name={item.name || ''}
                          size={48}
                        />
                      </Grid>
                      <Grid item>
                        <Typography
                          className="font-family-graphik-medium cursor-pointer text-ellipsis"
                          onClick={() => handleClickUser(item)}
                        >
                          {item.name || ''}
                        </Typography>
                      </Grid>
                      <Grid item xs sx={{ display: 'flex' }}>
                        {isAdminOrOwner(item.role) && (
                          <SVGVerified width={16} height={16} styles={{ marginTop: -2, marginLeft: -5 }} />
                        )}
                      </Grid>
                      <Grid item>
                        {canUpdateChnnelMembers && (
                          <IconButton
                            edge="end"
                            aria-label="delete"
                            sx={{ color: theme.palette.secondary.main }}
                            onClick={() => setMembersToAdd(membersToAdd.filter((m) => m.id !== item.id))}
                          >
                            <SVGTrash width={15} height={15} />
                          </IconButton>
                        )}
                      </Grid>
                    </Grid>
                  </ListItem>
                ))}
                {dispChannelMembers.map((item, index) => (
                  <ListItem key={item.user_id || index} sx={{ pl: 0, borderRadius: '12px' }}>
                    <Grid container alignItems="center" spacing={2}>
                      <Grid item>
                        <AppProfileImage
                          key={item.user_id || index}
                          imageUrl={item.user?.profile?.['image'] || item.user?.image}
                          cropArea={item.user?.profile?.['image_crop_area'] || item.user?.image_crop_area}
                          name={item.user?.name || ''}
                          size={48}
                          onClick={() => handleClickUser(item.user)}
                        />
                      </Grid>
                      <Grid item>
                        <Typography
                          className="font-family-graphik-medium cursor-pointer text-ellipsis"
                          onClick={() => handleClickUser(item.user)}
                        >
                          {item.user.name || ''}
                        </Typography>
                      </Grid>
                      <Grid item xs sx={{ display: 'flex' }}>
                        {isAdminOrOwner(item.user.role) && (
                          <SVGVerified width={16} height={16} styles={{ marginTop: -2, marginLeft: -5 }} />
                        )}
                      </Grid>
                      <Grid item>
                        {canUpdateChnnelMembers && (
                          <IconButton
                            edge="end"
                            aria-label="delete"
                            sx={{ color: theme.palette.secondary.main }}
                            onClick={() => setMemberIdsToDelete([...memberIdsToDelete, item.user?.id || item.user_id])}
                          >
                            <SVGTrash width={15} height={15} />
                          </IconButton>
                        )}
                      </Grid>
                    </Grid>
                  </ListItem>
                ))}
              </List>
            </Grid>
          )}
          {Boolean(errorMessage) && (
            <Grid item xs={12}>
              <FormHelperText error>{errorMessage}</FormHelperText>
            </Grid>
          )}
          <Grid item xs={12}></Grid>
          <Grid item xs={12}>
            <Button
              className="round-small"
              sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
              variant="contained"
              color="primary"
              fullWidth
              disabled={
                requestUpdateChannel ||
                Boolean(errorMessage) ||
                (!membersToAdd.length && !memberIdsToDelete.length && inputChannelName === channel.data.name)
              }
              onClick={() => handleUpdateChannel()}
            >
              {requestUpdateChannel ? <CircularProgress size={16} /> : 'Save'}
            </Button>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default AppMessagingEditChannel
