import Modal from '@mui/material/Modal'
import * as React from 'react'
import { useState } from 'react'

import { Button } from '@/components/ui'
import { joinMembershipApi } from '@/shared-services/apis/membership.api'
import DynamicForm from '@/src/components/community/dynamic-form'
import { selectMembership, selectMembershipSetting } from '@/src/store/features/membershipSlice'
import { openDialog } from '@/src/store/features/uiSlice'
import { updateUserMembership } from '@/src/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/src/store/hooks'

export default function MembershipPendingModal() {
  const dispatch = useAppDispatch()
  const membership = useAppSelector((state) => selectMembership(state))

  const handleOnClose = () => {
    dispatch(openDialog({ dialog: 'MembershipPending', open: false }))
  }

  if (!membership) {
    return
  }

  return (
    <div>
      <Modal
        open={true}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
        data-test={'verify-email-modal'}
        onClose={() => handleOnClose()}
      >
        <div className="absolute left-1/2 top-1/2 flex w-[440px] translate-x-[-50%] translate-y-[-50%] flex-col items-center justify-center rounded-2xl bg-neutral-800 p-12">
          <div className="w-full flex-col items-start justify-start gap-6">
            <div className="flex flex-col items-center justify-start gap-1.5">
              <div className="text-white text-center font-['Graphik'] text-lg font-semibold leading-normal">
                Membership Pending
              </div>
              <p className={'text-sm'}>
                {membership?.name} Community admins are reviewing your request. You’ll get an email when you're
                approved.
              </p>
            </div>
            <Button
              className={'mt-4 w-full'}
              type="submit"
              variant="default"
              onClick={() => handleOnClose()}
              data-cy="signin-button"
            >
              Got It
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}
