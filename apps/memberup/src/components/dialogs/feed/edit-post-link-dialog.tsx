import { DialogTitle } from '@mui/material'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import FormControl from '@mui/material/FormControl'
import Modal from '@mui/material/Modal'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import useTheme from '@mui/styles/useTheme'
import { useEffect, useRef, useState } from 'react'

import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { isUrlValid } from '@/memberup/libs/utils'

export default function AddLinkDialog({
  open,
  text,
  url,
  onCancel,
  onSave,
  subtitle,
  public_id,
}: {
  open: boolean
  text?: string
  url?: string
  onCancel?: () => void
  onSave: (link: { text: string; url: string; public_id?: string }) => void
  subtitle?: string
  public_id?: string
}) {
  const theme = useTheme()
  const textRef = useRef(null)
  const urlRef = useRef(null)
  const [error, setError] = useState(null)
  const editMode = Boolean(public_id)
  const characterLimit = 28
  const [titleText, setTitleText] = useState(text)
  const remainingCharacters = characterLimit - (titleText ? titleText.length : 0)

  useEffect(() => {
    if (open === false) {
      setError(null)
    }
  }, [open])

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    }
  }

  const handleSave = () => {
    setError(null)
    const text = textRef.current.value || ''
    let url = (urlRef.current.value || '').trim()
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = `https://${url}`
    }
    if (!isUrlValid(url)) {
      setError('Invalid URL')
      return
    }
    onSave({ text, url, public_id })
  }

  return open ? (
    <div>
      <Modal
        open={open}
        onClose={handleCancel}
        aria-labelledby="add-new-link-modal"
        aria-describedby="add-new-link-form"
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Box
          sx={{
            backgroundColor: theme.palette.background.paper,
            boxShadow: theme.shadows[5],
            padding: theme.spacing(2, 4, 3),
            borderRadius: '12px',
            width: '500px',
          }}
        >
          <DialogTitle
            sx={{
              borderBottom: 'none',
              padding: '12px 0px',
              fontFamily: 'Graphik SemiBold',
              fontSize: '18px',
              fontHeight: '24px',
            }}
          >
            {editMode ? 'Edit ' : 'Add '}Link
          </DialogTitle>
          {Boolean(subtitle) && (
            <Typography
              sx={{
                fontFamily: 'Graphik Regular',
                fontSize: '14px',
                fontHeight: '16px',
                color: theme.palette.text.disabled,
              }}
            >
              {subtitle}
            </Typography>
          )}
          <Box sx={{ mt: '12px' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'end' }}>
              <Typography
                paragraph={true}
                sx={{
                  color: '#8D94A3',
                  fontSize: '12px',
                  fontFamily: 'Graphik Regular',
                  fontWeight: '500',
                  marginBottom: '5px',
                  marginRight: '3px',
                }}
              >
                {remainingCharacters}
              </Typography>
              <FormControl sx={{ marginTop: '8px' }} className="form-control" fullWidth>
                <TextField
                  inputRef={textRef}
                  autoFocus
                  InputProps={{
                    style: {
                      top: '-2px',
                      backgroundColor: theme.palette.mode === 'dark' ? '#2e2f34' : '#ebefef',
                      borderRadius: '12px',
                      height: '48px',
                    },
                  }}
                  inputProps={{
                    maxLength: characterLimit,
                  }}
                  placeholder="Enter title"
                  id="text"
                  variant="outlined"
                  defaultValue={text}
                  onChange={(e) => setTitleText(e.target.value)}
                  sx={{
                    backgroundColor: theme.palette.action.disabledBackground,
                    height: '48px',
                    borderRadius: '12px',
                    '& .MuiOutlinedInput-notchedOutline': {
                      border: 'none',
                    },
                  }}
                />
              </FormControl>
            </Box>
            <Box sx={{ mt: '12px' }}>
              <FormControl sx={{ marginTop: '8px', marginBottom: '16px' }} className="form-control" fullWidth>
                <TextField
                  inputRef={urlRef}
                  defaultValue={url}
                  placeholder="https://www.google.com"
                  id="link"
                  variant="outlined"
                  InputProps={{
                    style: {
                      top: '-2px',
                      backgroundColor: theme.palette.mode === 'dark' ? '#2e2f34' : '#ebefef',
                      borderRadius: '12px',
                      height: '48px',
                    },
                  }}
                  sx={{
                    backgroundColor: theme.palette.action.disabledBackground,
                    height: '48px',
                    borderRadius: '12px',
                    '& .MuiOutlinedInput-notchedOutline': {
                      border: 'none',
                    },
                  }}
                />
              </FormControl>
            </Box>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'end',
                alignItems: 'center',
                marginTop: '2px',
                marginBottom: '5px',
              }}
            >
              {error && (
                <Box sx={{ marginRight: '16px' }}>
                  <Typography
                    color="error"
                    sx={{
                      fontFamily: 'Graphik Regular',
                      fontSize: '12px',
                      fontHeight: '14px',
                    }}
                  >
                    {error}
                  </Typography>
                </Box>
              )}
              <Button
                onClick={handleCancel}
                className="round-small"
                variant="outlined"
                color="inherit"
                sx={{
                  color: theme.palette.mode === 'dark' ? 'rgb(141, 148, 163)' : '#000000',
                  height: '48px',
                  width: '128px',
                  borderRadius: '10px !important',
                  marginRight: '1px',
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="round-small"
                variant="contained"
                color="primary"
                sx={{
                  color: '#fff',
                  height: '48px',
                  width: '128px',
                  borderRadius: '10px !important',
                  backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
                }}
              >
                Save
              </Button>
            </Box>
          </Box>
        </Box>
      </Modal>
    </div>
  ) : null
}
