import Box from '@mui/material/Box'
import ListItemText from '@mui/material/ListItemText'
import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'
import { makeStyles } from '@mui/styles'
import React from 'react'

import { SpaceMenuProps } from '@/src/components/dialogs/feed/space-selector'
import useAppTheme from '@/src/components/hooks/use-app-theme'

const useStyles = makeStyles((theme) => ({
  menu: {
    zIndex: 1400, // NOTE: Must be greater than 1300 which is the modal zIndex.
  },
  space: {
    opacity: 1,
    color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.87)' : '#000000',
    backgroundColor: theme.palette.mode === 'dark' ? '#202125' : '#f2f2f3',
    padding: '8px 10px',
    borderRadius: '10px',
    fontFamily: 'Graphik Semibold',
    fontSize: 13,
    fontWeight: 500,
    fontStyle: 'normal',
    letterSpacing: 0,
    textAlign: 'left',
  },
  spaceSelector: {
    borderRadius: 12,
  },
}))
export default function SpaceMenu(props: SpaceMenuProps) {
  const { theme, isDarkTheme, isMobile } = useAppTheme()
  const classes = useStyles()

  const { onClose, selectedValue, open, anchorEl, spaces } = props

  const handleClose = () => {
    if (onClose) {
      onClose()
    }
  }

  const handleSpaceSelectionChange = (value: any) => {
    onClose(value)
  }

  function stripHtmlAndTruncate(input: string, length: number = 35) {
    const strippedString = input.replace(/(<([^>]+)>)/gi, '')
    return strippedString.length > length ? strippedString.substring(0, length) + '...' : strippedString
  }

  return (
    <Menu
      className={classes.menu}
      anchorEl={anchorEl}
      open={open}
      onClose={handleClose}
      onClick={handleClose}
      sx={{
        '& .MuiBackdrop-root': {
          backgroundColor: 'transparent',
        },
      }}
      PaperProps={{
        sx: {
          mt: '6px',
          width: 265,
          maxHeight: isMobile ? '296px' : 'calc(100% - 32px)',
          borderRadius: '12px',
          boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
          border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
          backgroundColor: isDarkTheme ? '#000000' : '#f7f7f8',
          '& .MuiList-root': {
            paddingTop: 0,
            paddingBottom: 0,
          },
          '& .MuiDivider-root': {
            marginTop: 1,
            marginBottom: 1,
          },
          '& .MuiMenuItem-root': {
            p: '12px',
            alignItems: 'flex-start',
            color: isDarkTheme ? '#8e94a2' : '#595d65',
            '&:hover': {
              color: isDarkTheme ? '#ffffff !important' : '#000000 !important',
            },
          },
          '& .MuiListItemIcon-root': {
            color: 'inherit',
            fontSize: 18,
            minWidth: 28,
          },
          '& .MuiSvgIcon-root': {
            fontSize: 18,
          },
          '& .MuiTypography-body2': {
            color: isDarkTheme ? '#8e94a2' : '#595d65',
            fontFamily: 'Graphik Regular',
            fontSize: '12px',
            lineHeight: '16px',
          },
          '& .MuiListItemText-primary': {
            fontFamily: 'Graphik Semibold',
            fontSize: '13px',
            color: theme.palette.mode === 'dark' ? '#e1e1e1' : '#1c1c1c',
            lineHeight: '18px',
          },
          '& .MuiButtonBase-root': {
            '&:hover': {
              borderRadius: '8px',
            },
          },
        },
      }}
      transformOrigin={{ horizontal: 160, vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
    >
      <Box sx={{ backgroundColor: isDarkTheme ? '#17171a' : '#ffffff', padding: '6px 6px 6px 4px' }}>
        {spaces.map((space) => (
          <MenuItem
            sx={{
              '&:hover': {
                backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
              },
            }}
            key={space.id}
            onClick={() => handleSpaceSelectionChange(space)}
            data-cy="space-selector-item"
          >
            <ListItemText
              primary={space.name}
              secondary={stripHtmlAndTruncate(space.description || '-')}
              secondaryTypographyProps={{ title: space.description }}
            />
          </MenuItem>
        ))}
      </Box>
    </Menu>
  )
}
