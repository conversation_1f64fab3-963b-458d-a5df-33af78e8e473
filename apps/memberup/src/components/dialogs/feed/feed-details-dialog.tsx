import Box from '@mui/material/Box'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import IconButton from '@mui/material/IconButton'
import { makeStyles } from '@mui/styles'
import React, { useEffect } from 'react'
import { Channel, StreamMessage, useChatContext } from 'stream-chat-react'

import FeedCardNew from '../../feed/feed-card-new'
import SVGCloseNew from '../../svgs/close-new'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { DialogHeadingTitle } from '@/components/ui/dialog/DialogHeadingTitle'
import { selectEditedCommentTracks } from '@/memberup/store/features/feedSlice'
import { openDialog } from '@/memberup/store/features/uiSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { useAppDispatch } from '@/memberup/store/store'
import { IMembership } from '@/shared-types/interfaces'

const useStyles = makeStyles(() => ({
  closeButton: {
    position: 'fixed',
    right: 40,
    top: 20,
    zIndex: 1000,
  },
  paper: {
    background: 'transparent',
    boxShadow: 'none', // Removes default shadow
  },
  backdrop: {
    backgroundColor: 'rgba(0, 0, 0, 0.0)', // Transparent background
  },
}))

const FeedDetailsDialogNew: React.FC<{
  membership: IMembership
  feed: StreamMessage
  attachment?: any
  open: boolean
  onClose: () => void
  setOpenFeedDetails: (open: boolean) => void
  onPinMessage: (message: any) => void
  onUnpinMessage: (message: any) => void
}> = ({ membership, feed, open, onClose, setOpenFeedDetails, onPinMessage, onUnpinMessage }) => {
  const classes = useStyles()
  const membershipSetting = membership?.membership_setting
  const editedCommentTracks = useAppSelector(selectEditedCommentTracks)
  const dispatch = useAppDispatch()
  const [isSubmittingComment, setIsSubmittingComment] = React.useState(false)

  const { client } = useChatContext()
  const [channelType, channelId] = feed.cid.split(':')

  const channel = client.channel(channelType, channelId)

  useEffect(() => {
    const pathAfterDomain = window.location.pathname

    if (membership) {
      if (open && feed.permalink) {
        /* this avoids a page reload in contrast to react router replace */
        window.history.replaceState(null, '', `${membership.slug}/post/${feed.permalink || feed.id}`)
      } else {
        window.history.replaceState(null, '', `${membership.slug}/post/${feed.id}`)
      }
    }

    return () => {
      window.history.replaceState(null, '', pathAfterDomain)
    }
  }, [open, feed])

  const descriptionElementRef = React.useRef<HTMLElement>(null)
  React.useEffect(() => {
    if (open) {
      const { current: descriptionElement } = descriptionElementRef
      if (descriptionElement !== null) {
        descriptionElement.focus()
      }
    }
  }, [open])

  return (
    <Dialog
      open={open}
      onClick={(e) => {
        e.stopPropagation()
        if (isSubmittingComment) return

        const isUserEditingComments = Object.keys(editedCommentTracks).length > 0

        if (isUserEditingComments) {
          dispatch(
            openDialog({
              dialog: 'WarningEditingContent',
              open: true,
              props: { editingComments: true, setOpenFeedDetails },
            }),
          )
          return
        }
        /* check when clicking outside if the post is being edited */
        onClose()
      }}
      data-cy="feed-details-dialog"
      PaperProps={{ className: classes.paper }}
      fullScreen={true}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      style={{ zIndex: 1000 }}
      sx={{
        '& .MuiDialog-paper': {
          backgroundColor: 'transparent',
          height: '100%',
          width: '100%',
          maxHeight: '100%',
          maxWidth: '100%',
          margin: 0,
          padding: 0,
        },
      }}
      aria-labelledby="scroll-dialog-title"
      aria-describedby="scroll-dialog-description"
    >
      <div id="portal-suggestions-root" />
      <DialogHeadingTitle className="md:hidden" title="Post" backAction={onClose} />
      <DialogContent
        className="bg-white-500 dark:bg-dark-background-3 md:h-full md:bg-transparent md:dark:bg-transparent"
        id="feed-details-dialog-content"
        sx={{
          width: '100vw',
          padding: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          margin: 'auto',
          overflowY: 'hidden',
          '& .MuiPaper-root': {
            padding: 0,
          },
        }}
      >
        <Box
          id="feed-details-dialog-content-inner"
          className="w-full max-w-[720px] md:mx-[var(--page-container-padding)]"
          onClick={(e) => {
            e.stopPropagation()
          }}
        >
          <Box
            id="scroll-dialog-description"
            ref={descriptionElementRef}
            sx={{
              '&:focusVisible': {
                outline: 'none',
              },
            }}
          >
            <Box
              className="feed-card-container d-flex flex-col justify-center md:max-h-[90vh]"
              sx={{
                borderRadius: 5,
                '& .str-chat.messaging': { backgroundColor: 'transparent' },
                '& .str-chat *': { fontFamily: '' },
                '& > div': {
                  maxHeight: '90vh',
                },
              }}
            >
              <Channel channel={channel}>
                <FeedCardNew
                  membership={membership}
                  feed={feed}
                  styleOverride={{
                    maxHeight: '90vh',
                    maxWidth: '720px',
                    margin: 'auto',
                  }}
                  setIsSubmittingComment={setIsSubmittingComment}
                  onPinMessage={onPinMessage}
                  onUnpinMessage={onUnpinMessage}
                />
              </Channel>
            </Box>
          </Box>
        </Box>
      </DialogContent>
      <IconButton
        sx={{
          background: membershipSetting?.theme_mode === THEME_MODE_ENUM.dark ? '#17171a' : '#ffffff',
          color: '#8D94A3',
          p: '14px',
          m: '6px',
          width: '40px',
          height: '40px',
        }}
        size="medium"
        aria-label="close"
        className="close large hidden md:flex"
        onClick={onClose}
      >
        <SVGCloseNew fontSize={16} />
      </IconButton>
    </Dialog>
  )
}
export default FeedDetailsDialogNew
