// TODO: I would rename this file to community-rules.tsx
import <PERSON><PERSON> from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Divider from '@mui/material/Divider'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { selectMembershipAssetsPath } from '@/memberup/store/features/membershipSlice'
import { updateUserProfile } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
      backgroundColor: theme.palette.text.primary,
      color: theme.palette.background.default,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    color: theme.palette.background.default,
    fontSize: 24,
    paddingTop: 40,
    paddingBottom: 16,
    textAlign: 'center',
  },
  dialogContent: {
    lineHeight: 1,
    position: 'relative',
    padding: 32,
  },
}))

const CreatePostReminderDialog: React.FC<{
  open: boolean
  onProceed: () => void
  onClose: () => void
}> = ({ open, onProceed, onClose }) => {
  const classes = useStyles()
  const dispatch = useAppDispatch()
  const membershipAssetsPath = useAppSelector((state) => selectMembershipAssetsPath(state))

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="reminder-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="create-post-dialog-title">
        Just a friendly reminder
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <br />
        <br />
        <div className="text-center">
          <AppImg
            src={`${membershipAssetsPath}/svgs/space-guideline.svg`}
            height={100}
            width={100}
            alt="Space Guidelines Icon"
          />
        </div>
        <br />
        <br />
        <Typography className="text-center" variant="body1" color="inherit">
          We are a community who love Violet Benson, her antics and relationship advice. Although we do not
          discriminate, we are asking you to commit to the folowing:
        </Typography>
        <br />
        <br />
        <Divider />
        <br />
        <br />
        <Typography variant="subtitle1" color="inherit" gutterBottom>
          <b>Be nice and respect other people</b>
        </Typography>
        <Typography variant="body1" color="inherit" gutterBottom>
          We don&apos;t have to agree on everything but we choose to be kind and polite to one another.
        </Typography>
        <br />
        <br />
        <Typography variant="subtitle1" color="inherit" gutterBottom>
          <b>Cultivate positivity</b>
        </Typography>
        <Typography variant="body1" color="inherit" gutterBottom>
          We don&apos;t have to agree on everything but we choose to be kind and polite to one another.
        </Typography>
        <br />
        <Typography variant="subtitle1" color="inherit" gutterBottom>
          <b>Want to know more? Visit our Community Guidelines</b>
        </Typography>
        <br />
        <br />
        <Button
          className="round-small"
          variant="outlined"
          fullWidth
          onClick={() => {
            dispatch(updateUserProfile({ data: { create_post_reminder: true } }))
            onProceed()
          }}
        >
          Got It
        </Button>
      </DialogContent>
    </Dialog>
  )
}

export default CreatePostReminderDialog
