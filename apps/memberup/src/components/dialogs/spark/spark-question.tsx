//@ts-nocheck
import { joiResolver } from '@hookform/resolvers/joi'
import CloseIcon from '@mui/icons-material/Close'
import InsertEmoticonIcon from '@mui/icons-material/InsertEmoticon'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Slide from '@mui/material/Slide'
import useTheme from '@mui/material/styles/useTheme'
import TextField from '@mui/material/TextField'
import { TransitionProps } from '@mui/material/transitions'
import Typography from '@mui/material/Typography'
import useMediaQuery from '@mui/material/useMediaQuery'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Joi from 'joi'
import dynamic from 'next/dynamic'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { insertStr } from '@memberup/shared/src/libs/string-utils'
import { showToast } from '@memberup/shared/src/libs/toast'
import { ISparkMembershipQuestion, ISparkQuestion } from '@memberup/shared/src/types/interfaces'
import {
  createSparkQuestionApi,
  deleteSparkQuestionApi,
  updateSparkQuestionApi,
} from '@/shared-services/apis/spark.api'

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="up" ref={ref} {...props} />
})

const EmojiPicker = dynamic(() => import('@/memberup/components/common/pickers/emoji-picker'), {
  ssr: false,
})

type FormDataType = {
  content: string
  answer: string
}

const FormValue: FormDataType = {
  content: '',
  answer: '',
}

const FormSchema = Joi.object({
  content: Joi.string().required().messages({
    'string.empty': `Question cannot be an empty field`,
    'any.required': `Question is required.`,
  }),
}).options({ allowUnknown: true })

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
      maxWidth: 600,
    },
    '& .MuiOutlinedInput-root': {
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
      },
    },
    '& .MuiOutlinedInput-multiline': {
      padding: 0,
    },
    '& .MuiInputBase-formControl': {
      backgroundColor:
        theme.components.MuiCssBaseline.styleOverrides['body']['& .background-color18']['backgroundColor'],
      borderRadius: 12,
      paddingBottom: 36,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    fontSize: 20,
    padding: 24,
    paddingTop: 32,
    paddingBottom: 16,
  },
  dialogContent: {
    lineHeight: 1,
    position: 'relative',
  },
  emojiPickerWrapper: {
    position: 'absolute',
    left: 8,
    top: 132,
    height: 120,
    zIndex: 1003,
    '& emoji-picker': {
      height: '100%',
      '--background': theme.palette.background.paper,
      '--border-color': theme.palette.action.hover,
      '--button-active-background': theme.palette.action.hover,
      '--button-hover-background': theme.palette.action.hover,
      '--input-font-color': theme.palette.text.primary,
      '--skintone-border-radius': 12,
      '& .picker': {
        borderRadius: 12,
      },
    },
  },
}))

const SparkQuestionDialog: React.FC<{
  open: boolean
  onClose: (e: ISparkMembershipQuestion | ISparkQuestion | null) => void
  onReset: (e: ISparkMembershipQuestion) => void
  categoryId: string
  question?: ISparkQuestion
}> = ({ categoryId, question, open, onClose, onReset }) => {
  const mQuestion = question?.['spark_m_questions']?.[0]
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const selectionRef = useRef({
    start: 1,
    end: -1,
  })
  const theme = useTheme()
  const isSmDown = useMediaQuery(theme.breakpoints.down('sm'))
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [requestUpsertQuestion, setRequestUpsertQuestion] = useState(false)
  const [requestResetQuestion, setRequestResetQuestion] = useState(false)

  const { control, reset, formState, getValues, setValue, watch, handleSubmit } = useForm<FormDataType>({
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues: FormValue,
    resolver: joiResolver(FormSchema),
  })

  const handleFormSubmit = async (payload: FormDataType) => {
    if (!payload.content) return
    setRequestUpsertQuestion(true)
    const api = mQuestion?.id
      ? updateSparkQuestionApi(mQuestion?.id, payload)
      : createSparkQuestionApi({
          category_id: categoryId,
          question_id: question?.id,
          content: payload.content,
          answer: payload.answer,
        })
    api
      .then((res) => {
        showToast(`Question ${question?.id || mQuestion?.id ? 'updated' : 'created'}.`, 'success')
        onClose(res.data.data)
      })
      .catch((err) => {
        showToast(err.response?.data?.message || 'Server Error.', 'error')
      })
      .finally(() => {
        if (mountedRef.current) {
          setRequestUpsertQuestion(false)
        }
      })
  }

  const handleReset = async () => {
    if (!mQuestion) {
      reset({
        content: question?.content || '',
        answer: question?.answer || '',
      })
      return
    }

    setRequestResetQuestion(true)
    deleteSparkQuestionApi(mQuestion.id)
      .then((res) => {
        showToast(`Question reset`, 'success')
        onReset(res.data.data)
      })
      .catch((err) => {
        showToast(err.response?.data?.message || 'Server Error.', 'error')
      })
      .finally(() => {
        if (mountedRef.current) {
          setRequestResetQuestion(false)
        }
      })
  }

  const handleClickEmoji = (emoji) => {
    setShowEmojiPicker(false)
    const temp = getValues('answer')
    setValue('answer', insertStr(temp, emoji, selectionRef.current.start, selectionRef.current.end))
  }

  useEffect(() => {
    if (mountedRef) {
      reset({
        content: mQuestion?.content || question?.content || '',
        answer: mQuestion?.answer || question?.answer || '',
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [question])

  const renderEmojiPicker = useMemo(() => {
    if (typeof window === 'undefined') return null
    return (
      <Box className={classes.emojiPickerWrapper}>
        <EmojiPicker onClickEmoji={handleClickEmoji} />
      </Box>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <Dialog
      maxWidth="sm"
      fullWidth={true}
      className={clsx(classes.root, { mobile: isSmDown })}
      open={open}
      onClose={() => onClose(null)}
      TransitionComponent={isSmDown ? Transition : undefined}
      keepMounted
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="spark-share-answer"
    >
      <DialogTitle className={classes.dialogTitle} id="spark-share-answer">
        {question?.id ? 'Edit Question' : 'Create Question'}
        <IconButton size="small" aria-label="close" className="close large" onClick={() => onClose(null)}>
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Box sx={{ padding: 2, width: '100%' }}>
          <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs>
                <Typography
                  variant="body1"
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                  }}
                >
                  Question
                </Typography>
              </Grid>
              {Boolean(mQuestion) && (
                <Grid item>
                  <Button
                    className="app-button color03 no-padding min-w-0"
                    variant="text"
                    color="inherit"
                    fullWidth
                    sx={{ fontSize: 12 }}
                    onClick={handleReset}
                  >
                    {requestResetQuestion ? <CircularProgress size={12} color="inherit" /> : 'Reset to Default'}
                  </Button>
                </Grid>
              )}
              <Grid item xs={12} sx={{ position: 'relative' }}>
                <Controller
                  render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                    <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                      <TextField
                        placeholder="Enter question"
                        variant="outlined"
                        error={Boolean(error)}
                        helperText={error?.message}
                        value={value}
                        InputProps={{
                          multiline: true,
                          minRows: 1,
                          maxRows: 4,
                        }}
                        onChange={(e) => {
                          onChange(e.target.value)
                        }}
                        onBlur={onBlur}
                      />
                    </FormControl>
                  )}
                  control={control}
                  name="content"
                />
              </Grid>
              <Grid item xs={12}></Grid>
              <Grid item xs={12}>
                <Typography
                  variant="body1"
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                  }}
                >
                  Your answer
                </Typography>
              </Grid>
              <Grid item xs={12} sx={{ position: 'relative' }}>
                <Controller
                  render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                    <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                      <TextField
                        placeholder="Enter answer"
                        variant="outlined"
                        error={Boolean(error)}
                        helperText={error?.message}
                        value={value}
                        InputProps={{
                          multiline: true,
                          minRows: 4,
                          maxRows: 8,
                        }}
                        onChange={(e) => {
                          onChange(e.target.value)
                        }}
                        onBlur={(e) => {
                          selectionRef.current = {
                            start: e.target.selectionStart,
                            end: e.target.selectionEnd,
                          }
                          onBlur()
                        }}
                      />
                      <IconButton
                        onClick={() => setShowEmojiPicker((prevValue) => !prevValue)}
                        color="inherit"
                        size="small"
                        aria-label="show emoji picker"
                        sx={{
                          position: 'absolute',
                          bottom: Boolean(error) ? '26px' : '6px',
                          left: '8px',
                        }}
                      >
                        <InsertEmoticonIcon color="disabled" />
                      </IconButton>
                    </FormControl>
                  )}
                  control={control}
                  name="answer"
                />
                {showEmojiPicker && (
                  <ClickAwayListener onClickAway={(e) => setShowEmojiPicker(false)}>
                    {renderEmojiPicker}
                  </ClickAwayListener>
                )}
              </Grid>
              <Grid item xs={12}></Grid>
              <Grid item xs={12}></Grid>
              <Grid item xs={12}></Grid>
              <Grid item xs={6}>
                <Button
                  className="round-small"
                  variant="outlined"
                  fullWidth
                  style={{ height: 40 }}
                  onClick={() => onClose(null)}
                >
                  Cancel
                </Button>
              </Grid>
              <Grid item xs={6}>
                <Button
                  className="round-small"
                  sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
                  variant="contained"
                  fullWidth
                  // disabled={(formState.isDirty && !formState.isValid) || requestUpsertQuestion}
                  disabled={requestUpsertQuestion}
                  type="submit"
                  style={{ height: 40 }}
                >
                  {requestUpsertQuestion ? (
                    <CircularProgress size={16} color="inherit" />
                  ) : question?.id ? (
                    'Save Changes'
                  ) : (
                    'Save'
                  )}
                </Button>
              </Grid>
            </Grid>
          </form>
        </Box>
      </DialogContent>
    </Dialog>
  )
}

export default SparkQuestionDialog
