import List from '@mui/material/List'
import React from 'react'

import ExternalLinkItemEditor from '@/memberup/components/settings/external-link-item-editor'

const MAX_LINKS = 3

const ExternalLinksEditor = ({ links, onSave, onDelete }) => {
  return (
    <List sx={{ marginTop: '5px !important' }} dense style={{ paddingTop: 8, paddingBottom: 0 }}>
      {links.map((link, index) => (
        <ExternalLinkItemEditor
          key={index}
          value={link}
          onSave={(updatedLink) => onSave(updatedLink, index)}
          onDelete={() => onDelete(index)}
        />
      ))}
    </List>
  )
}

export default ExternalLinksEditor
