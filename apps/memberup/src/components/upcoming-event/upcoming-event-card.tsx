// TODO: We arent going to implement this like this anymore so you can remove
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import clsx from 'clsx'
import React from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { selectMembershipAssetsPath } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const UpcomingEventCard: React.FC<{ reward: any }> = ({ reward }) => {
  const theme = useTheme()
  const membershipAssetsPath = useAppSelector((state) => selectMembershipAssetsPath(state))

  return (
    <Card
      sx={{
        borderWidth: 0,
        borderRadius: 8,
        width: '100%',
        lineHeight: 1,
      }}
    >
      <CardContent
        sx={{
          color: theme.palette.text.secondary,
          p: '12px',
          '&:last-child': {
            pb: '12px',
          },
        }}
      >
        <Grid container spacing={2}>
          <Grid className="app-image-wrapper" item>
            <AppImg
              height={80}
              width={80}
              src={`${membershipAssetsPath}/images/upcoming_event_1.png`}
              alt="Upcoming Event"
            />
          </Grid>
          <Grid className={clsx('d-flex', 'direction-column', 'justify-center')} item xs>
            <Typography variant="body2">
              <b>Mindset Coaching Call</b>
            </Typography>
            <Typography variant="body2">11:00AM - 12:00PM PDT</Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default UpcomingEventCard
