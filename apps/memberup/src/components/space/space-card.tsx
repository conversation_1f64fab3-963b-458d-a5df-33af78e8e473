import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import Card from '@mui/material/Card'
import CardActions from '@mui/material/CardActions'
import CardContent from '@mui/material/CardContent'
import Collapse from '@mui/material/Collapse'
import Divider from '@mui/material/Divider'
import IconButton from '@mui/material/IconButton'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import React, { useState } from 'react'

const useStyles = makeStyles((theme) => ({
  root: {
    borderWidth: 0,
    borderRadius: 8,
    width: '100%',
  },
  cardContent: {
    position: 'relative',
    background: `linear-gradient(to left, #869aa7 0%, #938ca2 33%, #93669b 100%)`,
    color: theme.palette.text.secondary,
    fontSize: 14,
    padding: 12,
    paddingLeft: 28,
    '&.MuiCardContent-root:last-child': {
      paddingBottom: 12,
    },
  },
  cardAction: {
    color: theme.palette.text.primary,
    fontSize: 16,
    fontWeight: 600,
    padding: 8,
  },
  divider: {
    position: 'absolute',
    top: 12,
    left: 12,
    height: `calc(100% - 24px)`,
    backgroundColor: theme.palette.background.default,
    borderRadius: 4,
    width: 4,
  },
  expand: {
    transform: 'rotate(0deg)',
    marginLeft: 'auto',
    transition: theme.transitions.create('transform', {
      duration: theme.transitions.duration.shortest,
    }),
  },
  expandOpen: {
    transform: 'rotate(180deg)',
  },
}))

const SpaceCard: React.FC<{ space: any }> = ({ space }) => {
  const classes = useStyles()
  const [expanded, setExpanded] = useState(false)

  return (
    <Card className={classes.root}>
      <CardActions className={classes.cardAction} disableSpacing>
        <span>{space.name}</span>
        <IconButton
          className={clsx(classes.expand, {
            [classes.expandOpen]: expanded,
          })}
          onClick={() => setExpanded((prevExpanded) => !prevExpanded)}
          color="inherit"
          size="small"
          aria-expanded={expanded}
          aria-label="show more"
        >
          <ExpandMoreIcon />
        </IconButton>
      </CardActions>
      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <CardContent className={classes.cardContent}>
          <Divider className={classes.divider} orientation="vertical" />
          <div>{space.description}</div>
        </CardContent>
      </Collapse>
    </Card>
  )
}

export default SpaceCard
