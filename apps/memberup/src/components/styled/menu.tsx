import { ListItem, Menu, MenuProps } from '@mui/material'
import { DefaultComponentProps, OverridableTypeMap } from '@mui/material/OverridableComponent'

import useAppTheme from '../hooks/use-app-theme'

export const StyledMenu = (props: MenuProps) => {
  const { isDarkTheme } = useAppTheme()
  return (
    <Menu
      {...props}
      sx={{
        '& .MuiPaper-root': {
          borderRadius: '12px',
          boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
          border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
          backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
          backgroundImage: 'unset',
          padding: '6px',
        },
        '& .MuiList-root': {
          padding: '0px',
        },
        ...props.sx,
      }}
    >
      {props.children}
    </Menu>
  )
}

export const StyledListItem = (props: DefaultComponentProps<OverridableTypeMap>) => {
  const { isDarkTheme } = useAppTheme()
  return (
    <ListItem
      {...props}
      sx={{
        justifyContent: 'left',
        fontFamily: 'Graphik Medium',
        fontSize: '13px',
        height: '40px',
        margin: '0px',
        cursor: 'pointer',
        '&:hover': {
          backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
          borderRadius: '12px',
        },
        ...props.sx,
      }}
    >
      {props.children}
    </ListItem>
  )
}
