import React from 'react'

import MemberCard from '@/src/components/member/member-card'

export default function MembersListing({ members, onDeleteMemberClick }) {
  return (
    <div className="mt-5">
      {members.map((item: any) => (
        <MemberCard key={item.id} member={item} onDeleteMemberClick={onDeleteMemberClick} />
      ))}
      {members.length === 0 && (
        <div className="inline-flex w-full flex-col items-center justify-center gap-2.5 rounded-lg bg-black-500 p-4">
          <div className="text-[14px] font-normal leading-loose text-black-100">No members.</div>
        </div>
      )}
    </div>
  )
}
