import Box from '@mui/material/Box'
import Modal from '@mui/material/Modal'
import * as React from 'react'
import { useState } from 'react'

import CreateCommunityCheckoutFormContainer from '@/memberup/components/memberup/create-community-checkout-form'
import { getCommunityBaseURL } from '@/memberup/libs/utils'
import { openDialog, selectOpenDialogs } from '@/memberup/store/features/uiSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import CreateCommunityPaymentSuccessful from '@/src/components/memberup/create-community-payment-successful'

const style = {
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
}

export default function CreateCommunityModal() {
  const dispatch = useAppDispatch()

  const [paymentSuccess, setPaymentSuccess] = useState(false)
  const [newCommunityUrl, setNewCommunityUrl] = useState(null)

  const handleClose = () => {
    dispatch(openDialog({ dialog: 'CreateCommunity', open: false }))
  }

  const handleSuccess = (newMembership) => {
    //setPaymentSuccess(true)
    //setNewCommunityUrl(getCommunityBaseURL(newMembership))
  }

  const openDialogs = useAppSelector((state) => selectOpenDialogs(state))

  const handleContinue = () => {
    //handleClose()
  }

  // @ts-ignore
  return (
    <div>
      <Modal
        open={openDialogs['CreateCommunity']}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={style}>
          {!paymentSuccess ? (
            <CreateCommunityCheckoutFormContainer onSuccess={() => console.log('success')} />
          ) : (
            <CreateCommunityPaymentSuccessful community={null} />
          )}
        </Box>
      </Modal>
    </div>
  )
}
