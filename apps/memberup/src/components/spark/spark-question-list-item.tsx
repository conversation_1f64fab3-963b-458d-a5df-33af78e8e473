// @ts-nocheck
import { joiResolver } from '@hookform/resolvers/joi'
import DragIndicatorIcon from '@mui/icons-material/DragIndicator'
import InsertEmoticonIcon from '@mui/icons-material/InsertEmoticon'
import VisibilityIcon from '@mui/icons-material/Visibility'
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import useTheme from '@mui/material/styles/useTheme'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import Joi from 'joi'
import dynamic from 'next/dynamic'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { insertStr } from '@memberup/shared/src/libs/string-utils'
import { showToast } from '@memberup/shared/src/libs/toast'
import { upsertActiveSparkQuestionApi } from '@memberup/shared/src/services/apis/spark.api'
import {
  ISparkCompletedQuestionListingItem,
  ISparkMembershipQuestion,
  ISparkQuestion,
} from '@memberup/shared/src/types/interfaces'
import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { createSparkQuestionSettingsApi, updateSparkQuestionSettingsApi } from '@/shared-services/apis/spark.api'

const EmojiPicker = dynamic(() => import('@/memberup/components/common/pickers/emoji-picker'), {
  ssr: false,
})

type FormDataType = {
  content: string
}

const FormValue: FormDataType = {
  content: '',
}

const FormSchema = Joi.object({
  content: Joi.string().required().messages({
    'string.empty': `Question cannot be an empty field`,
    'any.required': `Question is required.`,
  }),
}).options({ allowUnknown: true })

const SparkQuestionContentInlineEditForm: React.FC<{
  onClose: (e: ISparkMembershipQuestion | null) => void
  question?: ISparkQuestion
}> = ({ question, onClose }) => {
  const theme = useTheme()
  const mountedRef = useMounted(true)
  const selectionRef = useRef({
    start: 1,
    end: -1,
  })
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [requestUpsertQuestion, setRequestUpsertQuestion] = useState(false)
  const content = question?.spark_m_questions?.[0]?.content || question?.content || ''

  const { control, formState, getValues, setValue, watch, handleSubmit } = useForm<FormDataType>({
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues: FormValue,
    resolver: joiResolver(FormSchema),
  })

  const handleFormSubmit = async (payload: FormDataType) => {
    if (!payload.content) return
    setRequestUpsertQuestion(true)
    if (question.question_settings_id) {
      try {
        const res = await updateSparkQuestionSettingsApi(question.question_settings_id, payload)
        onClose(res.data.data)
      } catch (err) {
        console.log(err)
        showToast(err.response?.data?.message || 'Server Error.', 'error')
      } finally {
        if (mountedRef.current) {
          setRequestUpsertQuestion(false)
        }
      }
    } else {
      try {
        const res = await createSparkQuestionSettingsApi({
          category_id: question.category_id,
          question_id: question.id,
          content: payload.content,
        })
        onClose(res.data.data)
      } catch (err) {
        showToast(err.response?.data?.message || 'Server Error.', 'error')
      } finally {
        if (mountedRef.current) {
          setRequestUpsertQuestion(false)
        }
      }
    }
  }

  const handleClickEmoji = (emoji) => {
    setShowEmojiPicker(false)
    const temp = getValues('content')
    setValue('content', insertStr(temp, emoji, selectionRef.current.start, selectionRef.current.end))
  }

  useEffect(() => {
    if (mountedRef) {
      setValue('content', content)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [content])

  const renderEmojiPicker = useMemo(() => {
    if (typeof window === 'undefined') return null
    return (
      <Box
        sx={{
          position: 'absolute',
          right: 8,
          top: 52,
          height: 120,
          zIndex: 1003,
          '& emoji-picker': {
            height: '100%',
            '--background': theme.palette.background.paper,
            '--border-color': theme.palette.action.hover,
            '--button-active-background': theme.palette.action.hover,
            '--button-hover-background': theme.palette.action.hover,
            '--input-font-color': theme.palette.text.primary,
            '--skintone-border-radius': 12,
            '& .picker': {
              borderRadius: 12,
            },
          },
        }}
      >
        <EmojiPicker onClickEmoji={handleClickEmoji} />
      </Box>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <Box sx={{ padding: 2, width: '100%' }}>
      <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="body1">Edit Question</Typography>
          </Grid>
          <Grid item xs={12} sx={{ position: 'relative' }}>
            <Controller
              render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                <FormControl error={Boolean(error)} className="form-control" fullWidth size="small">
                  <TextField
                    placeholder="Enter answer"
                    variant="outlined"
                    error={Boolean(error)}
                    helperText={error?.message}
                    size="small"
                    value={value}
                    InputProps={{
                      multiline: true,
                      minRows: 1,
                      maxRows: 4,
                    }}
                    onChange={(e) => {
                      onChange(e.target.value)
                    }}
                    onBlur={(e) => {
                      selectionRef.current = {
                        start: e.target.selectionStart,
                        end: e.target.selectionEnd,
                      }
                      onBlur()
                    }}
                  />
                  <IconButton
                    onClick={() => setShowEmojiPicker((prevValue) => !prevValue)}
                    color="inherit"
                    size="small"
                    aria-label="show emoji picker"
                    sx={{
                      position: 'absolute',
                      bottom: Boolean(error) ? '26px' : '6px',
                      right: 8,
                    }}
                  >
                    <InsertEmoticonIcon color="disabled" />
                  </IconButton>
                </FormControl>
              )}
              control={control}
              name="content"
            />
            {showEmojiPicker && (
              <ClickAwayListener onClickAway={(e) => setShowEmojiPicker(false)}>{renderEmojiPicker}</ClickAwayListener>
            )}
          </Grid>
          <Grid item xs={12}>
            <Button
              className="round-small"
              sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
              variant="contained"
              disabled={requestUpsertQuestion}
              type="submit"
              style={{ height: 32, minHeight: 32, marginTop: 8, width: 140 }}
            >
              {requestUpsertQuestion ? <CircularProgress size={16} color="inherit" /> : 'Save Changes'}
            </Button>
            <Button
              className="round-small"
              variant="outlined"
              style={{ height: 32, minHeight: 32, marginTop: 8 }}
              onClick={() => onClose(null)}
            >
              Cancel
            </Button>
          </Grid>
        </Grid>
      </form>
    </Box>
  )
}

const SparkQuestionListItem: React.FC<{
  question: ISparkCompletedQuestionListingItem
  draggable?: boolean
  editable?: boolean
  isDragging?: boolean
  isPrimary?: boolean
  onOpenAnswer?: () => void
  callbackUpdate?: (question: any) => void
  activeSparkResponses?: any
  expiresIn?: string
}> = ({
  question,
  draggable,
  editable,
  isDragging,
  isPrimary = false,
  onOpenAnswer,
  callbackUpdate,
  activeSparkResponses,
  expiresIn,
}) => {
  const content = question.content || ''
  const answer = question.answer
  const theme = useTheme()
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const [isEdit, setIsEdit] = useState(false)
  const [requestUpsertQuestion, setRequestUpsertQuestion] = useState(false)
  const [isHovered, setIsHovered] = useState<boolean>(false)

  const handleVisibilityChange = async (active: boolean) => {
    if (!editable) return
    setRequestUpsertQuestion(true)

    try {
      // TODO: Replace the /active endpoint for /question-settings
      const res = await upsertActiveSparkQuestionApi(question.id, active)
      if (res.data?.success) {
        callbackUpdate(res.data.data)
      }
    } catch (err) {
      console.log(err)
      showToast(err.response?.data?.message || 'Server Error.', 'error')
    } finally {
      setRequestUpsertQuestion(false)
    }
  }

  return (
    <Box
      sx={{
        padding: 3,
        width: '100%',
        position: 'relative',
        borderRadius: '12px',
        background: draggable && (isHovered || isDragging) ? theme.palette.divider : 'initial',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {isEdit ? (
        <SparkQuestionContentInlineEditForm
          question={question}
          onClose={(e) => {
            if (e) {
              callbackUpdate(e)
            }
            setIsEdit(false)
          }}
        />
      ) : (
        <Grid container spacing={2} alignItems="center" pl={3}>
          {draggable && (isHovered || isDragging) && (
            <Box
              sx={{
                position: 'absolute',
                left: 5,
                top: 0,
                bottom: 0,
                margin: 'auto',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <DragIndicatorIcon color="inherit" />
            </Box>
          )}

          <Grid className="min-w-0" item xs>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Grid container spacing={4} wrap="nowrap">
                  <Grid className="min-w-0" item xs>
                    <Typography
                      className={isPrimary ? 'text-ellipsis--3' : 'text-ellipsis'}
                      variant="body1"
                      onClick={() => {
                        if (question.active && editable) setIsEdit(true)
                      }}
                      sx={{
                        cursor: editable ? 'pointer' : 'default',
                        color: isPrimary ? '#FFFFFF' : question.active ? 'inherit' : 'gray',
                      }}
                    >
                      {content}
                    </Typography>
                  </Grid>
                  {!editable && (
                    <>
                      <Grid item>
                        <span className="color03">Expired: </span>
                        <span>
                          {formatDate({
                            date: question.completed_at || new Date(),
                            format: 'MM/dd/yyyy',
                          })}
                        </span>
                      </Grid>
                      <Grid item>
                        <span className="color03">Reponses: </span>
                        <span>{question.responses_count}</span>
                      </Grid>
                    </>
                  )}
                  {isPrimary && (
                    <Grid item sx={{ display: 'flex' }}>
                      <Box
                        sx={{
                          display: 'flex',
                          backgroundColor: 'rgba(255, 255, 255, 0.08)',
                          borderRadius: '12px',
                          color: '#FFFFFF',
                          px: 2,
                          py: 1,
                          mr: 1,
                          height: 20,
                        }}
                      >
                        <Typography fontWeight={600} lineHeight={1} fontSize={12} sx={{ color: 'lightgray' }}>
                          Expires In:&nbsp;
                        </Typography>
                        <Typography color="inherit" fontWeight={'bold'} lineHeight={1} fontSize={12}>
                          <b data-cy="spark-expires-settings">{expiresIn}</b>
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          display: 'flex',
                          backgroundColor: 'rgba(255, 255, 255, 0.08)',
                          borderRadius: '12px',
                          color: '#FFFFFF',
                          px: 2,
                          py: 1,
                          height: 20,
                        }}
                      >
                        <Typography fontWeight={600} lineHeight={1} fontSize={12} sx={{ color: 'lightgray' }}>
                          Responses:&nbsp;
                        </Typography>
                        <Typography color="inherit" fontWeight={'bold'} ml={2} lineHeight={1} fontSize={12}>
                          {activeSparkResponses?.length}
                        </Typography>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </Grid>
              {Boolean(answer) && (
                <Grid item xs={12}>
                  <Typography className="color02" variant="body2">
                    {answer}
                  </Typography>
                </Grid>
              )}
              {editable && (
                <Grid item xs={12}>
                  <Button
                    className="color03 no-padding min-w-0"
                    variant="text"
                    disabled={!question.active}
                    sx={{
                      '&.Mui-disabled': {
                        color: isPrimary ? 'lightgray' : 'gray',
                      },
                    }}
                    onClick={() => onOpenAnswer?.()}
                  >
                    {answer ? 'Edit' : 'Answer'}
                  </Button>
                </Grid>
              )}
            </Grid>
          </Grid>

          {!isPrimary && editable && (
            <Grid item>
              <IconButton
                aria-controls="edit-question"
                aria-haspopup="true"
                size="small"
                onClick={() => handleVisibilityChange(!question.active)}
              >
                {question.active ? (
                  <VisibilityIcon fontSize="small" />
                ) : (
                  <VisibilityOffIcon color="disabled" fontSize="small" />
                )}
              </IconButton>
            </Grid>
          )}
        </Grid>
      )}
    </Box>
  )
}

export default SparkQuestionListItem
