import React, { useEffect, useState } from 'react'

import { useStore } from '@/hooks/useStore'
import HomeSparkLoader from '@/memberup/components/common/loaders/home-spark-loader'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import Spark<PERSON><PERSON><PERSON> from '@/memberup/components/spark/spark-hidden'
import SparkNotConfigured from '@/memberup/components/spark/spark-not-configured'
import SparkQuestionCard from '@/memberup/components/spark/spark-question-card'
import { setHideSparkWarning } from '@/memberup/store/features/feedSlice'
import { mergeUserProfile, resetUser, selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { useMounted } from '@/shared-components/hooks/use-mounted'
import { isUserActiveAndAcceptedInCommunity } from '@/shared-libs/profile'
import { getCurrentSparkMembershipQuestionInstanceApi } from '@/shared-services/apis/spark.api'
import { hideSparkApi } from '@/shared-services/apis/user.api'

const SparkContainer: React.FC = () => {
  const dispatch = useAppDispatch()
  const { isCurrentUserAdmin } = useCheckUserRole()
  const user = useStore((state) => state.auth.user)
  const membership = useStore((state) => state.community.membership)
  const mountedRef = useMounted(true)
  const userProfile = useAppSelector((state) => selectUserProfile(state))
  const sparkResponses = useAppSelector((state) => state.spark.responses)
  const [status, setStatus] = useState<
    'not-configured' | 'collapsed' | 'hidden' | 'hidden-not-configured' | 'loading' | 'loaded'
  >('hidden')
  const [question, setQuestion] = useState<any>(null)

  const isSparkReady =
    membership?.membership_setting?.spark_enabled &&
    membership?.membership_setting?.spark_current_membership_question_instance_id

  useEffect(() => {
    if (!mountedRef.current) return

    if (!user || !isUserActiveAndAcceptedInCommunity(user, membership)) {
      setStatus('hidden')
    }

    if (isCurrentUserAdmin && isSparkReady) {
      if (userProfile.spark_hidden_at) {
        setStatus('hidden-not-configured')
      } else {
        setStatus('not-configured')
      }
      return
    }

    const load = async () => {
      if (!isSparkReady) {
        return
      }

      setStatus('loading')
      try {
        const result = await getCurrentSparkMembershipQuestionInstanceApi(membership.id)
        // Update the user with the current sparkStreak
        dispatch(resetUser({ sparkStreak: result.data?.data?.streak || 0 }))
        const questionRes = result.data?.data
        console.log('QUESTION RES', questionRes)
        if (!questionRes) {
          setStatus('collapsed')
          return
        }

        if (userProfile.spark_hidden_at && userProfile.spark_hidden_at > questionRes.createdAt) {
          setStatus('collapsed')
          if (JSON.stringify(question) !== JSON.stringify(questionRes)) {
            setQuestion(questionRes)
          }
          return
        }

        if (JSON.stringify(question) !== JSON.stringify(questionRes)) {
          setQuestion(questionRes)
        }
        setStatus('loaded')
      } catch (err) {
        console.log('err =====', err)
        if (!mountedRef.current) return
        setStatus('hidden')
      }
    }
    load()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    membership?.membership_setting.spark_enabled,
    membership?.membership_setting.spark_current_membership_question_instance_id,
    userProfile?.spark_hidden_at,
  ])

  const handleHideClick = async () => {
    if (isCurrentUserAdmin && !membership?.membership_setting.spark_enabled) {
      dispatch(setHideSparkWarning(true))
    } else {
      try {
        setStatus('collapsed')
        const result = await hideSparkApi({ hide: true })
        if (result.data) {
          dispatch(mergeUserProfile({ spark_hidden_at: result.data.data }))
        }
      } catch (err) {
        console.log(err)
      }
    }
  }

  const handleViewClick = async () => {
    try {
      setStatus('loaded')
      const result = await hideSparkApi({ hide: false })
      if (result.data) {
        dispatch(mergeUserProfile({ spark_hidden_at: null }))
      }
    } catch (err) {
      console.log(err)
    }
  }

  const isSparkNotConfigured = isCurrentUserAdmin && status === 'not-configured'

  if (status === 'hidden') {
    return
  }

  if (status === 'collapsed') {
    /* merge sparkResponses and question.spark_respones, both are arrays of objects which has an id, only add the sparkResponses ids that are not in spark_respones */
    if (question?.spark_responses) {
      const currentSessionSparkResponses =
        sparkResponses?.filter((addedRes) => !question.spark_responses.find((res) => res.id === addedRes.id)) || []
      question.spark_responses = [...(question?.spark_responses || []), ...currentSessionSparkResponses]
    }
    return <SparkHidden question={question} onViewClick={handleViewClick} />
  }

  if (isSparkNotConfigured) {
    return <SparkNotConfigured onHideClick={handleHideClick} />
  }

  if (status === 'loading') {
    return <HomeSparkLoader />
  }

  if (status === 'loaded') {
    return (
      <SparkQuestionCard
        question={question.question}
        sparkStreak={question.spark_streak}
        onHideClick={handleHideClick}
      />
    )
  }
}

export default SparkContainer
