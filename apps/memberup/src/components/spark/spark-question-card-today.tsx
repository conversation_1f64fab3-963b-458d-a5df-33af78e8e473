import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import clsx from 'clsx'
import React, { useMemo, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import SVGClock from '@/memberup/components/svgs/clock'

type FormDataType = {
  text: string
}

const FormValue: FormDataType = {
  text: '',
}

const SparkQuestionCardToday: React.FC = () => {
  const [openShare, setOpenShare] = useState(false)
  const hookFormMethods = useForm<FormDataType>({
    reValidateMode: 'onChange',
    defaultValues: FormValue,
  })
  const { control, register, reset, formState, getValues, setValue, watch, handleSubmit } = hookFormMethods

  const handleFormSubmit = async (payload: FormDataType) => {}

  const renderUsers = useMemo(() => {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'row' }}>
        <AppProfileImage
          imageUrl="/assets/default/images/profile.png"
          name=""
          size={32}
          style={{ borderStyle: 'solid', borderColor: '#F0C5B4', borderWidth: 2 }}
        />
        <AppProfileImage
          imageUrl="/assets/default/images/profile.png"
          name=""
          size={32}
          style={{
            marginLeft: -12,
            borderStyle: 'solid',
            borderColor: '#F0C5B4',
            borderWidth: 2,
          }}
        />
        <AppProfileImage
          imageUrl="/assets/default/images/profile.png"
          name=""
          size={32}
          style={{
            marginLeft: -12,
            borderStyle: 'solid',
            borderColor: '#F0C5B4',
            borderWidth: 2,
          }}
        />
      </Box>
    )
  }, [])

  return (
    <Box className="background-gradient01" sx={{ padding: 4, width: '100%' }}>
      <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="body1" gutterBottom>
              Today
            </Typography>
            <Typography variant="h6">What personally trait has gotten you in the most trouble?</Typography>
          </Grid>
          <Grid item xs={12}>
            <Grid container alignItems="center" spacing={2}>
              <Grid item xs>
                <Typography
                  variant="body2"
                  style={{
                    background: `linear-gradient(90deg, #358848 0%, #C05BDB 100%)`,
                    borderRadius: 25,
                    display: 'inline-flex',
                    alignItems: 'center',
                    padding: 6,
                    paddingRight: 12,
                    paddingLeft: 12,
                  }}
                >
                  <SVGClock />
                  <span style={{ marginLeft: 6 }}>Expires in 5 hours 23 min</span>
                </Typography>
              </Grid>
              {!openShare && (
                <Grid item>
                  <Typography variant="body2">View 43 responses</Typography>
                </Grid>
              )}
              {!openShare && <Grid item>{renderUsers}</Grid>}
            </Grid>
          </Grid>
          {openShare && (
            <Grid item xs={12}>
              <Divider style={{ borderColor: '#FFFFFF' }} />
            </Grid>
          )}
          {openShare && (
            <Grid item xs={12}>
              <Grid container alignItems="center" spacing={2}>
                <Grid item>
                  <AppProfileImage imageUrl="/assets/default/images/profile.png" name="" size={40} />
                </Grid>
                <Grid item xs>
                  <Typography variant="body2">Anne Copeland</Typography>
                </Grid>
              </Grid>
            </Grid>
          )}
          {openShare && (
            <Grid item xs={12}>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                    <TextField
                      placeholder="What's your answer, anna?"
                      variant="outlined"
                      error={Boolean(error)}
                      helperText={error?.message}
                      value={value}
                      InputProps={{
                        multiline: true,
                        minRows: 6,
                        maxRows: 12,
                      }}
                      onChange={(e) => {
                        onChange(e.target.value)
                      }}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="text"
              />
            </Grid>
          )}
          {openShare && (
            <Grid item xs={12}>
              <Box sx={{ padding: 2, borderRadius: 3, backgroundColor: '#FFFFFF' }}>
                <Grid container alignItems="center" spacing={2}>
                  <Grid item></Grid>
                  <Grid item xs>
                    <Typography variant="body2" style={{ color: '#000000' }}>
                      View 43 responses
                    </Typography>
                  </Grid>
                  <Grid item>{renderUsers}</Grid>
                </Grid>
              </Box>
            </Grid>
          )}
          <Grid item xs={12}>
            <Button
              className="round-small background-color01"
              variant="contained"
              fullWidth
              onClick={() => setOpenShare(true)}
            >
              Share your answer
            </Button>
          </Grid>
        </Grid>
      </form>
    </Box>
  )
}

export default SparkQuestionCardToday
