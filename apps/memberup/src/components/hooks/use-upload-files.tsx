import { IGif } from '@giphy/js-types'
import { UpChunk } from '@mux/upchunk'
import _orderBy from 'lodash/orderBy'
import { useCallback, useState } from 'react'
import { v4 as uuidv4 } from 'uuid'

import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { getFileType } from '@memberup/shared/src/libs/file'
import { uploadFileToCloudinaryApi } from '@memberup/shared/src/services/apis/cloudinary.api'
import { TAttachment } from '@memberup/shared/src/types/types'
import { getMuxUploadDataApi } from '@/shared-services/apis/mux.api'

const useUploadFiles = (prefix: string, membershipId: string) => {
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadFiles, setUploadFiles] = useState<File[]>([])
  const [uploadedFiles, setUploadedFiles] = useState<TAttachment[]>([])
  const [cancelTokenSources, setCancelTokenSources] = useState([])

  const initUploadFiles = () => {
    setUploadFiles([])
    setUploadedFiles([])
    setUploadProgress(0)
  }

  const cancelUploads = useCallback(() => {
    cancelTokenSources.forEach((source) => source.cancel('Upload cancelled by the user.'))
    setCancelTokenSources([]) // Clear the tokens after cancelling
  }, [cancelTokenSources])

  const resetUploadProgress = () => {
    setUploadProgress(0)
  }
  const handleUploadFiles = useCallback(
    (
      files: (File & Partial<IGif> & { is_gif?: boolean })[],
      uploadTo: 'Mux' | 'Cloudinary',
      createWithTranscriptions: boolean = false,
      providedPassthrough: string = null,
    ) => {
      const promises = []
      const tempProgress = []
      const tempUploadedFiles = []
      const progressPerFile = 100 / files.length
      for (let i = 0; i < files.length; i++) {
        try {
          const file = files[i]
          const fileType = getFileType(file)

          const passthrough = providedPassthrough || `${prefix}:${uuidv4()}`
          tempProgress[i] = 0
          promises.push(
            new Promise((resolve) => {
              if (file.is_gif) {
                tempUploadedFiles.push({
                  index: i,
                  id: file.id,
                  is_gif: true,
                  title: file.title,
                  mimetype: file.type,
                  url: file.url,
                  embed_url: file.embed_url,
                })
                tempProgress[i] = progressPerFile
                setUploadProgress(tempProgress.reduce((prevValue, currentValue) => prevValue + currentValue, 0))
                resolve(true)
              } else if (uploadTo === 'Mux' && fileType === 'video') {
                getMuxUploadDataApi(passthrough, createWithTranscriptions, membershipId, true)
                  .then((res) => {
                    const upload = UpChunk.createUpload({
                      endpoint: res.data.data.mux_upload_url,
                      file,
                      attempts: 3,
                      chunkSize: 102400, // Uploads the file in ~100mb chunks
                    })

                    // subscribe to events
                    upload.on('attempt', (temp) => {
                      /* console.log('attempt', temp) */
                    })
                    upload.on('attemptFailure', (temp) => {
                      /* console.log('attemptFailure', temp) */
                    })
                    upload.on('chunkSuccess', (temp) => {
                      /* console.log('chunkSuccess', temp) */
                    })
                    upload.on('offline', (temp) => {
                      console.log('offline', temp)
                    })
                    upload.on('online', (temp) => {
                      console.log('online', temp)
                    })
                    upload.on('error', (temp) => {
                      console.log('error', temp)
                    })
                    upload.on('progress', (temp) => {
                      tempProgress[i] = (parseInt(temp.detail) * progressPerFile) / 100
                      setUploadProgress(tempProgress.reduce((prevValue, currentValue) => prevValue + currentValue, 0))
                    })
                    upload.on('success', (temp) => {
                      tempUploadedFiles.push({
                        index: i,
                        mimetype: fileType,
                        filename: file.name,
                        passthrough,
                        mux_upload_id: res.data.data.mux_upload_id,
                        uploaded_date: formatDate({
                          date: new Date(),
                          format: 'EEE, LLL d, yyyy K:mm aa',
                        }),
                        public_id: res?.data.public_id || (file as any).public_id,
                      })
                      resolve(true)
                    })
                    upload.on('error', (err) => {
                      console.error('💥 🙀', err.detail)
                    })
                  })
                  .catch((err) => {
                    console.log('err ======', err)
                  })
              } else {
                uploadFileToCloudinaryApi(
                  file,
                  fileType,
                  (progress: number) => {
                    tempProgress[i] = progress * progressPerFile
                    setUploadProgress(tempProgress.reduce((prevValue, currentValue) => prevValue + currentValue, 0))
                  },
                  (source) => {
                    setCancelTokenSources([...cancelTokenSources, source])
                  },
                )
                  .then((res) => {
                    if (res?.data) {
                      tempUploadedFiles.push({
                        index: i,
                        filename: file.name,
                        mimetype: fileType,
                        passthrough,
                        url: res.data.secure_url as string,
                        uploaded_date: formatDate({
                          date: new Date(),
                          format: 'EEE, LLL d, yyyy K:mm aa',
                        }),
                        thumbnail: res?.data.thumbnail || (file as any).thumbnail,
                        size_in_bytes: res?.data.size_in_bytes || (file as any).size_in_bytes || file.size,
                        public_id: res?.data.public_id || (file as any).public_id,
                      })
                    }
                  })
                  .catch((err) => {
                    console.log('err1 =====', err)
                  })
                  .finally(() => {
                    resolve(true)
                  })
              }
            }),
          )
          setUploadFiles(files)
        } catch (err: any) {
          console.log('err2 ======', err.message)
        }
      }

      return Promise.all(promises)
        .then(() => {
          const temp = _orderBy(tempUploadedFiles, ['index'], ['asc']).map((item) => {
            const { index, ...rest } = item
            return rest
          })
          setUploadedFiles(temp)
          return temp
        })
        .catch((err) => {
          console.error(err.message)
          return []
        })
    },
    [],
  )

  return {
    uploadFiles,
    uploadProgress,
    uploadedFiles,
    initUploadFiles,
    handleUploadFiles,
    resetUploadProgress,
    cancelUploads,
  }
}

export default useUploadFiles
