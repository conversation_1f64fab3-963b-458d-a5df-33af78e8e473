import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'

import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import {
  boot as bootIntercom,
  load as loadIntercom,
  show as showIntercom,
  update as updateIntercom,
} from '@/memberup/libs/intercom'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { getFullName } from '@/shared-libs/profile'

const useIntercom = () => {
  const router = useRouter()
  const user = useAppSelector((state) => selectUser(state))

  const [shouldOpenChat, setShouldOpenChat] = useState(false)

  useEffect(() => {
    if (
      typeof window !== 'undefined' &&
      [USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner].includes(user?.role || (USER_ROLE_ENUM.member as any))
    ) {
      const userName = `${getFullName(user.first_name, user.last_name, '')}`.trim()
      loadIntercom()
      bootIntercom({
        name: userName,
        email: user.email,
        user_id: user.id,
        hide_default_launcher: !router.pathname.includes('/settings'),
      })
    }
  }, [user])

  useEffect(() => {
    const handleRouteChange = (url: string) => {
      if (
        typeof window !== 'undefined' &&
        [USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner].includes(user?.role || (USER_ROLE_ENUM.member as any))
      ) {
        updateIntercom({
          hide_default_launcher: !url.includes('/settings'),
        })
      }
    }

    router.events.on('routeChangeStart', handleRouteChange)

    // If the component is unmounted, unsubscribe
    // from the event with the `off` method:
    return () => {
      router.events.off('routeChangeStart', handleRouteChange)
    }
  }, [router.events, user])

  useEffect(() => {
    const openIntercomHandler = () => {
      showIntercom()
    }

    window.addEventListener('openIntercom', openIntercomHandler)

    // Clean up the listener when the component unmounts
    return () => {
      window.removeEventListener('openIntercom', openIntercomHandler)
    }
  }, [])
}

useIntercom.displayName = 'useIntercom'

export default useIntercom
