import { useEffect, useRef } from 'react'
import { useChatContext } from 'stream-chat-react'
import { usePathname } from 'next/navigation'

import { useStore } from '@/hooks/useStore'
import { canAccessCommunityPath } from '@/lib/authorization'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import {
  resetState,
  selectSpacesList,
  setMessages,
  setPinnedMessages,
} from '@/memberup/store/features/feedAggregationSlice'
import { FEED_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { transformMessage } from '@/memberup/libs/utils'
import { selectRequestGetChannels } from '@/memberup/store/features/spaceSlice'

const SEARCH_MESSAGE_LIMIT = 10

const useFeed = ({ spaceConfig, limit = SEARCH_MESSAGE_LIMIT }) => {
  const user = useStore((state) => state.auth.user)
  const membership = useStore((state) => state.community.membership)
  const streamChatUserToken = useStore((state) => state.auth.streamChatUserToken)
  const pathname = usePathname()
  const userRef = useRef(user)
  const dispatch = useAppDispatch()
  const { client: chatClient } = useChatContext()
  const messages = useAppSelector(
    (state) => {
      if (!spaceConfig) return []
      return state.feedAggregation?.spaces[spaceConfig.id]?.messages || []
    }
  )

  const feedAggregation = useAppSelector (state => state.feedAggregation)

  console.log('CHECK feedAggregation', feedAggregation)

  const communitySpaces = useStore(state => state.community.spaces)
  const community = useStore(state => state.community)

  // CHECK: This looks like duplicated as the useChatContext should provide this through the client.
  const requestGetChannels = useAppSelector(selectRequestGetChannels)
  const streamChatConnecting = useStore((state) => state.streamChat.connecting)

  const streamChatConnected = useStore((state) => state.streamChat.connected)

  const pinnedMessages = useAppSelector(
    (state) => state.feedAggregation.spaces['community']?.pinnedMessages || []
  )

  const next = useAppSelector((state) => {
    if (!spaceConfig) return null

    return state.feedAggregation.spaces[spaceConfig.id]?.next
  })

  const sortBy = useAppSelector((state) => {
    if (!spaceConfig) return null

    return state.feedAggregation.spaces[spaceConfig.id]?.orderBy
  })

  const spaces = useAppSelector((state) => selectSpacesList(state))
  const setLoadingMessages = useStore((state) => state.community.setLoadingMessages)


  console.log('CHECK spaces', spaces)
  console.log('CHECK communitySpaces', communitySpaces)
  console.log('CHECK community', community)

  const filteredSpaces =
    spaces?.filter((s: any) => {
      // if (s.channelType === 'team') {
      //   return user && user.status != 'unverified'
      // }
      return s
    }) || []



  const selectedSpaceIds =
    spaceConfig?.slug === 'community' ? filteredSpaces.map((s: any) => s.id) : spaceConfig ? [spaceConfig.id] : []

  useEffect(() => {
    if (selectedSpaceIds.length === 0 || !streamChatConnected) {
      return
    }

    if (!requestGetChannels && !streamChatConnecting && streamChatConnected) {
      loadMessages(spaceConfig, sortBy, null, 'replace')
    }
  }, [requestGetChannels, spaceConfig, JSON.stringify(selectedSpaceIds), streamChatConnected, chatClient])

  useEffect(() => {
    if (user && streamChatUserToken && !userRef.current || !user && userRef.current) {
      userRef.current = user
      setLoadingMessages(true)
      dispatch(resetState())
    }
  }, [streamChatUserToken, user])

  const loadMessages = async (
    spaceConfig: any,
    newOrderBy?: string,
    next?: string,
    mode?: 'replace' | 'append'
  ) => {
    if (!chatClient || !canAccessCommunityPath(user, membership, pathname)) return

    setLoadingMessages(true)
    const channelFilters = { id: { $in: selectedSpaceIds } }
    const messageFilters = {
      deleted_at: { $exists: false },
      parent_id: { $exists: false },
      pinned: spaceConfig.slug === 'community' ? { $eq: false } : undefined,
      feed_status: { $nin: [FEED_STATUS_ENUM.rejected, FEED_STATUS_ENUM.reported] },
    }

    const orderByClause: any[] = []
    const currentOrderBy = newOrderBy
    if (currentOrderBy === 'newest') {
      orderByClause.push({ created_at: -1 })
    } else if (currentOrderBy === 'activity') {
      orderByClause.push({ last_activity_at: -1 })
    }

    const messagesResponse = await chatClient.search(channelFilters, messageFilters, {
      sort: orderByClause,
      limit: limit,
      next: next,
    })

    const loadedMessages = JSON.parse(JSON.stringify(messagesResponse)).results.map(
      (result: any) => transformMessage(result.message)
    )

    // Always load pinned messages for community space.
    const filters = {
      deleted_at: { $exists: false },
      parent_id: { $exists: false },
      pinned: { $eq: true },
      feed_status: { $nin: [FEED_STATUS_ENUM.rejected, FEED_STATUS_ENUM.reported] },
    }
    const channelFiltersForPinnedMessages = { id: { $in: filteredSpaces.map((s: any) => s.id) } }
    const response = await chatClient.search(channelFiltersForPinnedMessages, filters, {
      sort: orderByClause,
      limit: 10,
    })

    const pinnedMessages = JSON.parse(
      JSON.stringify(response.results.map((r) => transformMessage(r.message)))
    )

    dispatch(
      setMessages({
        id: spaceConfig.id,
        messages: loadedMessages,
        next: messagesResponse.next,
        mode,
      })
    )
    dispatch(setPinnedMessages({ id: 'community', messages: pinnedMessages }))
    setLoadingMessages(false)
  }

  const fetchMoreMessages = () => {
    loadMessages(spaceConfig, null, next, 'append')
  }

  const fetchMessages = (sortBy: 'newest' | 'activity') => {
    loadMessages(spaceConfig, sortBy, null, 'replace')
  }

  return {
    messages: {
      results: messages,
      hasMore: next,
    },
    isCommunitySpace: spaceConfig?.slug === 'community',
    sortBy: sortBy,
    fetchMoreMessages,
    fetchMessages,
  }
}

export default useFeed
