import { useStore } from '@/hooks/useStore'
import { checkAdminOrCreatorRole, getUserRole } from '@/shared-libs/profile'

const useCheckUserRole: () => { isCurrentUserAdmin: boolean } = () => {
  const membership = useStore((state) => state.community.membership)
  const user = useStore((state) => state.auth.user)

  const isAdminOrCreator = checkAdminOrCreatorRole(getUserRole(user, membership?.id))

  return { isCurrentUserAdmin: isAdminOrCreator }
}

export default useCheckUserRole
