import { useAuthenticatedKnockClient, useNotifications } from '@knocklabs/react'
import { useStore as zustandUseStore } from 'zustand'

import { useStore } from '@/hooks/useStore'

const KNOCK_PUBLIC_API_KEY = process.env.NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY
const KNOCK_NOTIFICATION_FEED_ID = process.env.NEXT_PUBLIC_KNOCK_NOTIFICATION_FEED_ID

const useKnockNotification = () => {
  const user = useStore((state) => state.auth.user)
  const knockUserToken = useStore((state) => state.auth.knockToken)
  const knock = useAuthenticatedKnockClient(KNOCK_PUBLIC_API_KEY, user?.id, knockUserToken)

  // TODO 3023: Check the useNotifications, as at the time that we try to use the useNotifications the membership could be null, not yet loaded.
  const notificationFeed = useNotifications(knock, KNOCK_NOTIFICATION_FEED_ID)
  const useNotificationStore = zustandUseStore(notificationFeed.store as any)

  return {
    notificationFeed,
    useNotificationStore,
  }
}

export default useKnockNotification
