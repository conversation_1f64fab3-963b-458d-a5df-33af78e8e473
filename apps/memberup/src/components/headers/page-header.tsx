'use client'

import SettingsIcon from '@mui/icons-material/Settings'
import Badge from '@mui/material/Badge'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import List from '@mui/material/List'
import ListItemAvatar from '@mui/material/ListItemAvatar'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemText from '@mui/material/ListItemText'
import Popover from '@mui/material/Popover'
import Stack from '@mui/material/Stack'
import useTheme from '@mui/material/styles/useTheme'
import Tooltip from '@mui/material/Tooltip'
import Typography from '@mui/material/Typography'
import useMediaQuery from '@mui/material/useMediaQuery'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import React, { useEffect, useMemo, useState } from 'react'

import useAppTheme from '../hooks/use-app-theme'
import SVGLeftArrow from '../svgs/arrow-left'
import SVGHashtag from '../svgs/hashtag'
import SVGMenuItem from '../svgs/menu-item'
import SVGSettingsNew from '../svgs/settings-new'
import HeaderTabs from './page-header-tabs'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { getDateTimeFromNow } from '@memberup/shared/src/libs/date-utils'
import { getCapitalized, slugify } from '@memberup/shared/src/libs/string-utils'
import { KNOCK_WORKFLOW_ENUM, THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { ChevronLeft24Icon } from '@/components/icons'
import AppLogo from '@/memberup/components/common/app-logo'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import useKnockNotification from '@/memberup/components/hooks/use-knock-notification'
import useSpaceName from '@/memberup/components/hooks/use-space-name'
import ProfileMenu from '@/memberup/components/menu/profile-menu'
import SVGMessage from '@/memberup/components/svgs/message'
import SVGSearch from '@/memberup/components/svgs/search'
import SVGTopNotification from '@/memberup/components/svgs/top-notification'
import { getCommunityBaseURL } from '@/memberup/libs/utils'
import { ROUTES } from '@/memberup/settings/router'
import { selectTotalUnreadCount } from '@/memberup/store/features/inboxSlice'
import { selectMembership, selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import {
  openDialog,
  selectMarkReadNotification,
  selectOptimisticKnockUnseen,
  setOptimisticKnockUnseen,
} from '@/memberup/store/features/uiSlice'
import { selectKnockToken, selectUser, selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  tooltip: {
    backgroundColor: theme.palette.mode === 'dark' ? '#333336' : '#fff',
    boxShadow: theme.palette.mode === 'light' ? '0px 4px 4px rgba(0, 0, 0, 0.1)' : undefined,
    color: theme.palette.mode === 'dark' ? '#fff' : '#000',
  },
}))

const ContentSettingsBtn = () => {
  const { isCurrentUserAdmin } = useCheckUserRole()
  const { theme, isDarkTheme } = useAppTheme()
  const dispatch = useAppDispatch()

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        borderRadius: '24px',
        height: '48px',
        padding: '6px 10px 6px 0px',
        backdropFilter: 'blur(10px)',
        webkitBackdropFilter: 'blur(10px)',
        backgroundColor: isDarkTheme ? 'rgba(23, 23, 26, 0.6)' : '#dadddc',
      }}
    >
      <Typography
        variant="h5"
        noWrap
        sx={{
          pl: '16px',
          fontSize: '16px',
          fontFamily: 'Graphik SemiBold',
          fontWeight: '500',
          mr: '5px',
          color: theme.palette.text.primary,
        }}
      >
        Content
      </Typography>
      {isCurrentUserAdmin && (
        <IconButton
          className="color03"
          sx={{
            ml: 1,
            backgroundColor: isDarkTheme ? '#000' : '#FFFFFF',
          }}
          size="medium"
          onClick={() => dispatch(openDialog({ dialog: 'CustomizeContentHeader', open: true, props: {} }))}
          aria-controls="settings"
          aria-haspopup="true"
        >
          <SVGSettingsNew />
        </IconButton>
      )}
    </Box>
  )
}

const AppPageTitle = () => {
  const pathname = usePathname()
  const router = useRouter()
  const { isCurrentUserAdmin } = useCheckUserRole()
  const theme = useTheme()
  const { isMobile } = useAppTheme()
  const postChannelName = useAppSelector((state) => state.header.postChannelName)
  const sluggedChannel = slugify(postChannelName || '')
  const dispatch = useAppDispatch()
  const membership = useAppSelector((state) => selectMembership(state))
  const member = useAppSelector((state) => selectUser(state))

  if (pathname === '/content') {
    return <ContentSettingsBtn />
  }

  const handleBackButtonClick = () => {
    router.replace(
      sluggedChannel === 'home'
        ? `${getCommunityBaseURL(membership)}`
        : `${getCommunityBaseURL(membership)}/space/${sluggedChannel}`,
    )
  }

  if (pathname === '/post/[id]' && sluggedChannel) {
    return (
      <Grid item xs={12} container alignItems="center">
        <Grid item>
          {!isMobile ? (
            <IconButton
              sx={{
                width: '32px',
                height: '32px',
                marginRight: '10px',
                color: theme.palette.mode === 'dark' ? '#000000' : '#585D66',
                backgroundColor: theme.palette.mode === 'dark' ? '#d7d9da' : '#ffffff',
                transition: 'background-color 0.3s ease-in-out',
                '&:hover': {
                  backgroundColor: theme.palette.mode === 'dark' ? '#8e94a2' : '',
                },
              }}
              onClick={handleBackButtonClick}
              aria-label="back to posts"
              data-cy="back-to-posts"
            >
              <ChevronLeft24Icon />
            </IconButton>
          ) : (
            <IconButton
              sx={{
                width: '32px',
                height: '32px',
                marginRight: '4px',
                color: theme.palette.mode === 'dark' ? '#8e94a2' : '#585D66',
                backgroundColor: 'none',
                transition: 'background-color 0.3s ease-in-out',
                '&:hover': {
                  //backgroundColor: theme.palette.mode === 'dark' ? '#8e94a2' : '',
                },
              }}
              onClick={handleBackButtonClick}
              aria-label="back to posts"
              data-cy="back-to-posts"
            >
              <SVGLeftArrow />
            </IconButton>
          )}
        </Grid>

        {!isMobile && (
          <Grid item>
            <Typography
              sx={{
                fontFamily: 'Graphik SemiBold',
                fontWeight: 600,
                letterSpacing: '0px',
                fontSize: '16px',
                lineHeight: '32px',
                //pl: '16px'
              }}
              variant="h2"
            >
              <SVGHashtag styles={{ verticalAlign: 'unset' }} />
              &nbsp;
              {getCapitalized(sluggedChannel)}
            </Typography>
          </Grid>
        )}
      </Grid>
    )
  }

  const simpleHeader = ROUTES[pathname]?.layout?.attributes?.simpleHeader

  return (
    <>
      <Typography
        variant="h5"
        noWrap
        className={clsx(simpleHeader ? 'leading-none' : 'leading-8')}
        sx={{
          fontFamily: 'Graphik SemiBold',
          fontWeight: 400,
          fontSize: '16px',
        }}
      >
        {ROUTES[pathname]?.layout?.attributes?.visibleTitle && (ROUTES[pathname]?.title || 'Community')}
        {isCurrentUserAdmin && pathname === '/library' && (
          <IconButton
            className="color03"
            sx={{ ml: 1 }}
            size="small"
            onClick={() => router.push('/settings/library')}
            aria-controls="settings"
            aria-haspopup="true"
          >
            <SettingsIcon />
          </IconButton>
        )}
      </Typography>
      {Boolean(ROUTES[pathname]?.subtitle) && (
        <Typography color="text.disabled" variant="body1" sx={{ mt: '8px', pl: '16px' }}>
          {ROUTES[pathname]?.subtitle}
        </Typography>
      )}
    </>
  )
}

const AppNotification = ({ detailed = false }) => {
  const mountedRef = useMounted(true)
  const { theme, isMobile } = useAppTheme()
  const router = useRouter()
  const markReadNotification = useAppSelector((state) => selectMarkReadNotification(state))
  const { notificationFeed: notificationFeedAll, useNotificationStore: useNotificationStoreAll } =
    useKnockNotification()
  const { notificationFeed, useNotificationStore } = useKnockNotification()
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null)
  const [status, setStatus] = useState<'all' | 'unread' | 'read' | 'unseen' | 'seen'>('all')
  const { metadata: metadataAll, items: itemsAll } = useNotificationStoreAll as any
  const { metadata, items, pageInfo, loading } = useNotificationStore as any
  const dispatch = useAppDispatch()
  const classes = useStyles()
  const open = Boolean(anchorEl)
  const optimisticKnockUnseen = useAppSelector((state) => selectOptimisticKnockUnseen(state))
  const totalUnreadInboxMessagesCount = useAppSelector((state) => selectTotalUnreadCount(state))
  const members = useAppSelector((state) => selectMembersMap(state))
  const pathname = usePathname()

  useEffect(() => {
    notificationFeedAll?.fetch({
      status: 'all',
    })
  }, [])

  useEffect(() => {
    if (pathname.includes('/post')) {
      if (optimisticKnockUnseen) {
        notificationFeedAll?.markAllAsSeen()
        setOptimisticKnockUnseen(false)
      }
    }
  }, [pathname])

  useEffect(() => {
    notificationFeed?.fetch({
      status,
    })
  }, [status])

  useEffect(() => {
    if (open && metadataAll?.unseen_count) {
      notificationFeedAll?.markAllAsSeen()
      notificationFeedAll?.markAllAsRead()
    }
  }, [open, metadataAll?.unseen_count])

  useEffect(() => {
    try {
      if (itemsAll?.length && markReadNotification) {
        const temp = itemsAll.find((item) => item.data.id === markReadNotification)
        if (temp && !temp.seen_at) {
          notificationFeedAll?.markAsSeen(temp)
          notificationFeedAll?.markAsRead(temp)
        }
      }
    } catch (err) {}
  }, [itemsAll, markReadNotification])

  const handlePopoverOpen = (event: React.MouseEvent<HTMLElement>) => {
    if (anchorEl) {
      handlePopoverClose()
    } else {
      setAnchorEl(event.currentTarget)
    }
  }

  const handlePopoverClose = () => {
    setAnchorEl(null)
  }

  const handleMarkAllRead = () => {
    notificationFeed?.markAllAsSeen()
    notificationFeed?.markAllAsRead()
    notificationFeedAll?.fetch()
  }

  const handleReadNotification = async (data) => {
    let event_url_modified = data.event_url ? new URL(data.event_url) : null
    if (event_url_modified) {
      event_url_modified.host = window.location.host
      // If the new host is localhost, switch the protocol to http
      if (window.location.host.includes('localhost')) {
        event_url_modified.protocol = 'http:'
      }
    }

    let redirect_url = data.community_url || (event_url_modified ? event_url_modified.toString() : null)

    if (data?.url) {
      redirect_url = data.url
    }
    setAnchorEl(null)
    if (redirect_url) {
      dispatch(setOptimisticKnockUnseen(true))
      setTimeout(() => {
        router.push(redirect_url)
      }, 1000)
    }
  }

  let isShort = true
  return (
    <>
      <Tooltip classes={classes} title="Messages" placement="bottom">
        <IconButton
          color="inherit"
          className="color03"
          sx={{
            p: '16px',
          }}
          size="small"
          onClick={() => {
            router.push('/inbox')
          }}
          data-cy="inbox-button"
        >
          <Badge
            badgeContent={totalUnreadInboxMessagesCount || undefined}
            color="error"
            data-cy="notifications-unseen-count-inbox"
            sx={{ '& .MuiBadge-badge': { right: -2, pt: '2.2px' } }}
          >
            <SVGMessage width={16} height={16} />
          </Badge>
        </IconButton>
      </Tooltip>
      <Tooltip classes={classes} title="Notifications" placement="bottom">
        <IconButton
          color="inherit"
          size="small"
          sx={{
            p: '16px',
            marginRight: { xs: '10px !important', sm: '0px' },
          }}
          onClick={handlePopoverOpen}
          aria-label="notifications"
          data-cy="notifications-button"
        >
          <Badge
            badgeContent={metadataAll?.unseen_count || undefined}
            color="error"
            data-cy="notifications-unseen-count"
            sx={{ '& .MuiBadge-badge': { right: -2, pt: '2.2px' } }}
          >
            <SVGTopNotification width={16} height={16} />
          </Badge>
        </IconButton>
      </Tooltip>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        sx={{
          zIndex: 9999,
          '& .MuiPopover-paper': {
            mt: '8px',
            borderRadius: '12px',
            border: theme.palette.mode === 'dark' ? '1px solid #2a2b30' : '1px solid #e0e0e0',
          },
        }}
      >
        <Box
          sx={{
            width: { xs: '100%', sm: 400 },
            maxWidth: '100%',
            backgroundColor: theme.palette.mode === 'dark' ? '#17171a' : '#ffffff',
          }}
        >
          <Box sx={{ p: 3 }}>
            <Grid container alignItems="center" spacing={2} justifyContent="space-between">
              <Grid item>
                <Typography sx={{ fontFamily: 'Graphik Medium', fontSize: '14px' }} variant="body1">
                  Notifications
                </Typography>
              </Grid>
              <Grid item>
                {Boolean(metadata?.unread_count) && (
                  <Button
                    variant="text"
                    className="no-padding"
                    size="small"
                    style={{
                      color: theme.palette.mode === 'dark' ? '#8d94a3' : 'secondary',
                      fontSize: '14px',
                      fontFamily: 'Graphik Semibold',
                    }}
                    //endIcon={<CheckCircleOutlineIcon />}
                    onClick={handleMarkAllRead}
                    sx={{ '& .MuiButton-endIcon': { ml: 1 } }}
                  >
                    Mark all as read
                  </Button>
                )}
              </Grid>
            </Grid>
          </Box>
          <Box
            sx={{
              height: 352,
              overflowY: 'auto',
              minWidth: '260px',
            }}
          >
            {items.length ? (
              <List dense data-cy="notifications">
                {items.map((item) => {
                  const member = members[item.actors?.[0]?.id]
                  return (
                    <ListItemButton
                      sx={{ minHeight: '64px' }}
                      key={item.id}
                      dense
                      onClick={() => {
                        notificationFeed?.markAsSeen(item)
                        notificationFeed?.markAsRead(item)
                        notificationFeedAll?.fetch()
                        handleReadNotification(item.data)
                      }}
                      data-cy="notification-item"
                    >
                      <ListItemAvatar sx={{ minWidth: 40 }}>
                        <AppProfileImage
                          imageUrl={member?.profile?.image || item.actors[0]?.['image']}
                          cropArea={member?.profile?.image_crop_area || item.actors[0]?.['image_crop_area']}
                          name={member?.name || item.actors[0]?.['name']}
                        />
                      </ListItemAvatar>
                      <ListItemText
                        disableTypography
                        primary={
                          <Grid container justifyContent="flex-start" alignItems="flex-start" spacing={2}>
                            <Grid item xs={9}>
                              <Typography
                                gutterBottom
                                fontFamily="Graphik Semibold"
                                fontSize="14px"
                                style={{ paddingTop: '5px' }}
                              >
                                {item.blocks[0]?.rendered
                                  .replace(/<\/?p>/g, '')
                                  .split('<br />')
                                  .map((part, index) =>
                                    index === 0 ? (
                                      part
                                    ) : (
                                      <div
                                        key={index}
                                        style={{
                                          fontFamily: 'Graphik Regular',
                                          fontSize: '13px',
                                          color: '#8D94A3',
                                          display: '-webkit-box',
                                          WebkitLineClamp: 2,
                                          WebkitBoxOrient: 'vertical',
                                          overflow: 'hidden',
                                        }}
                                      >
                                        {part}
                                      </div>
                                    ),
                                  )}
                              </Typography>
                            </Grid>
                            <Grid item xs={3} container alignItems="flex-start" justifyContent="flex-end">
                              <Typography
                                style={{
                                  color: '#8D94A3',
                                  fontSize: '12px',
                                  fontFamily: 'Graphik Regular',
                                }}
                              >
                                {getDateTimeFromNow(item.inserted_at)}
                              </Typography>
                              <Badge
                                variant="dot"
                                color="primary"
                                sx={{
                                  position: 'absolute',
                                  bottom: '40%',
                                  right: '25px',
                                  '& .MuiBadge-dot': {
                                    height: '10px',
                                    width: '10px',
                                    borderRadius: '50%',
                                  },
                                }}
                                invisible={Boolean(item.seen_at)}
                              />
                            </Grid>
                          </Grid>
                        }
                      />
                    </ListItemButton>
                  )
                })}
              </List>
            ) : (
              <Typography align="center" color="text.disabled" variant="body1" sx={{ mt: '150px' }}>
                No notifications yet
              </Typography>
            )}
          </Box>
          <Box className="text-center">
            <Button
              variant="text"
              sx={{
                color: theme.palette.mode === 'dark' ? '#8d94a3' : 'secondary',
                fontSize: '12px',
                fontFamily: 'Graphik Semibold',
                padding: '14px',
              }}
              size="small"
              onClick={() => router.push('/notifications')}
              fullWidth
            >
              View all
            </Button>
          </Box>
        </Box>
      </Popover>
    </>
  )
}

const AppPageHeader: React.FC<{
  onClickHamburger: () => void
}> = ({ onClickHamburger }) => {
  const pathname = usePathname()
  const { theme, isDarkTheme } = useAppTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  const router = useRouter()
  const dispatch = useAppDispatch()
  const user = useAppSelector((state) => selectUser(state))
  const userProfile = useAppSelector((state) => selectUserProfile(state))
  const knockUserToken = useAppSelector((state) => selectKnockToken(state))
  const [anchorProfileEl, setAnchorProfileEl] = useState(null)
  const isCreatorRoute =
    ROUTES[pathname]?.isCreatorRoute || (pathname.startsWith('/settings') && !pathname.startsWith('/settings/account/'))
  const classes = useStyles()

  const handleClickProfile = (event) => {
    setAnchorProfileEl(event.currentTarget)
  }

  const handleClickMenuItem = (menuItem: string) => {
    setAnchorProfileEl(null)
    switch (menuItem) {
      case 'Search':
        dispatch(openDialog({ dialog: 'Search', open: true, props: {} }))
        break
      case 'Invite':
        break
    }
  }

  return (
    <div>
      <div
        id="page-header"
        className="page-header page-inner-py flex h-[58px] w-full overflow-hidden bg-[#16181c]"
        data-cy="page-header"
      >
        <Box
          className="app-page-title-container"
          sx={{ display: { xs: pathname === '/post/[id]' ? 'block' : 'none', sm: 'block' } }}
        >
          <AppPageTitle />
        </Box>
        <Box
          className="page-header-menu-container-mobile flex-item grow"
          sx={{
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
          }}
        >
          <Box
            sx={{
              display: { xs: 'flex', sm: 'none' },
              maxWidth: isMobile ? '190px' : '208px',
            }}
            onClick={() => router.push('/community')}
          >
            {(pathname === '/library' || pathname.startsWith('/course')) && isMobile && (
              <AppLogo
                height={32}
                width={166}
                style={{ top: 0, left: '0px', position: 'absolute' }}
                textStyle={{ fontSize: '20px', top: 0 }}
              />
            )}
            {pathname === '/library' && !isMobile && <ContentSettingsBtn />}
            {pathname !== '/library' && !(pathname.startsWith('/course') && isMobile) && (
              <AppLogo height={32} width={166} textStyle={{ fontSize: '20px', top: 0 }} />
            )}
          </Box>
        </Box>
        {(!isCreatorRoute || pathname === '/getting-started') &&
          (pathname !== '/library' || isMobile ? (
            <Box
              sx={{
                display: 'block',
                '& .rnf-feed-provider': {
                  ml: '14px!important',
                  '& .rnf-notification-icon-button': {
                    color: theme.palette.text.disabled,
                    width: 22,
                    height: 28,
                    '& svg': {
                      color: 'inherit',
                      width: 20,
                    },
                  },
                  '& .rnf-notification-feed-popover__inner': {
                    backgroundColor: theme.palette.background.paper,
                  },
                  '& .rnf-notification-feed': {
                    backgroundColor: theme.palette.background.default,
                  },
                  '& .rnf-notification-cell': {
                    '&:hover': {
                      backgroundColor: theme.palette.action.hover,
                    },
                  },
                  '& .rnf-notification-feed__type, & .rnf-notification-cell__content': {
                    color: theme.palette.text.primary,
                  },
                  '& .rnf-notification-cell__timestamp, & .rnf-empty-feed__header': {
                    color: theme.palette.text.disabled,
                  },
                  '& .rnf-notification-feed__knock-branding': {
                    display: 'none',
                  },
                },
              }}
            >
              <div className="flex gap-1">
                <Tooltip classes={classes} title="Search" placement="bottom">
                  <IconButton
                    aria-label="search"
                    sx={{
                      position: 'relative',
                      display: { xs: 'none', sm: 'inherit' },
                      '& svg': {
                        maxHeight: '16px',
                        maxWidth: '16px',
                        position: 'relative !important',
                        display: 'inline-block',
                      },
                    }}
                    className="color03 align-center flex h-8 w-8 justify-center p-0"
                    size="small"
                    onClick={() => handleClickMenuItem('Search')}
                  >
                    <SVGSearch styles={{ position: 'absolute' }} />
                  </IconButton>
                </Tooltip>

                {Boolean(knockUserToken) && <AppNotification />}
                {user && (
                  <IconButton
                    edge="end"
                    aria-label="account of current user"
                    aria-haspopup="true"
                    color="inherit"
                    size="small"
                    onClick={handleClickProfile}
                    sx={{
                      width: '32px',
                      height: '32px',
                      p: 0,
                      display: {
                        xs: 'none',
                        sm: 'inherit',
                      },
                    }}
                    data-cy="profile-menu-button"
                  >
                    <AppProfileImage
                      imageUrl={userProfile?.image || user?.image}
                      cropArea={userProfile?.image_crop_area || user?.image_crop_area}
                      name={user?.first_name || user?.email}
                    />
                  </IconButton>
                )}
              </div>
            </Box>
          ) : (
            <Box
              className="page-header-menu-container"
              sx={{
                display: 'flex',
                backgroundColor: isDarkTheme ? 'rgba(23, 23, 26, 0.6)' : '#dadddc',
                zIndex: 99,
                borderRadius: '24px',
                height: '48px',
                alignItems: 'center',
                backdropFilter: 'blur(10px)',
                padding: '0 8px',
                WebkitBackdropFilter: 'blur(10px)',
                '& .rnf-feed-provider': {
                  ml: '14px!important',
                  '& .rnf-notification-icon-button': {
                    color: theme.palette.text.disabled,
                    width: 22,
                    height: 28,
                    '& svg': {
                      color: 'inherit',
                      width: 20,
                    },
                  },
                  '& .rnf-notification-feed-popover__inner': {
                    backgroundColor: theme.palette.background.paper,
                  },
                  '& .rnf-notification-feed': {
                    backgroundColor: theme.palette.background.default,
                  },
                  '& .rnf-notification-cell': {
                    '&:hover': {
                      backgroundColor: theme.palette.action.hover,
                    },
                  },
                  '& .rnf-notification-feed__type, & .rnf-notification-cell__content': {
                    color: theme.palette.text.primary,
                  },
                  '& .rnf-notification-cell__timestamp, & .rnf-empty-feed__header': {
                    color: theme.palette.text.disabled,
                  },
                  '& .rnf-notification-feed__knock-branding': {
                    display: 'none',
                  },
                },
              }}
            >
              <Stack direction="row" spacing={{ sm: '4px' }}>
                <Tooltip classes={classes} title="Search" placement="bottom">
                  <IconButton
                    aria-label="search"
                    sx={{
                      p: '16px',
                      position: 'relative',
                      display: { xs: 'none', sm: 'inherit' },
                    }}
                    className="color03"
                    size="small"
                    onClick={() => handleClickMenuItem('Search')}
                  >
                    <SVGSearch styles={{ position: 'absolute' }} />
                  </IconButton>
                </Tooltip>

                {Boolean(knockUserToken) && <AppNotification detailed />}
                <Divider
                  sx={{
                    height: 22,
                    borderRightWidth: 2,
                    backgroundColor: 'rgba(141, 148, 163, 0.12)',
                    borderRadius: 10,
                    mt: '3px !important',
                    ml: '-2px !important',
                    display: { xs: 'none', sm: 'block' },
                  }}
                  orientation="vertical"
                  flexItem
                />
                <IconButton
                  edge="end"
                  aria-label="account of current user"
                  // aria-controls={menuId}
                  aria-haspopup="true"
                  color="inherit"
                  size="small"
                  onClick={handleClickProfile}
                  sx={{
                    ml: '17px !important',
                    display: { xs: 'none', sm: 'inherit' },
                  }}
                  data-cy="profile-menu-button"
                  className="h-8 w-8 p-0"
                >
                  <AppProfileImage
                    imageUrl={userProfile?.image || user?.image}
                    cropArea={userProfile?.image_crop_area || user?.image_crop_area}
                    name={user?.first_name || user?.email}
                  />
                </IconButton>
              </Stack>
            </Box>
          ))}
        <Box
          sx={{
            display: {
              xs: 'block',
              sm: 'none',
              position: 'relative',
            },
          }}
        >
          <IconButton
            id="menu-button"
            color="inherit"
            size="small"
            onClick={onClickHamburger}
            aria-label="show menu"
            sx={{
              color: '#8d94a3',
              width: '32px',
              height: '32px',
              position: 'relative',
              p: '3px',
              '& svg': {
                width: '25px',
                height: '25px',
              },
            }}
          >
            <SVGMenuItem width={50} height={50} />
          </IconButton>
        </Box>
        {/*<ProfileMenu anchorEl={anchorProfileEl} onClose={() => setAnchorProfileEl(null)} />*/}
      </div>
      {isMobile && (
        <Box className="pb-3">
          <HeaderTabs />
        </Box>
      )}
    </div>
  )
}

export default AppPageHeader
