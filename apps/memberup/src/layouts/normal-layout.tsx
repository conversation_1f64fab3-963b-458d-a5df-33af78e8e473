import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import useTheme from '@mui/material/styles/useTheme'
import useMediaQuery from '@mui/material/useMediaQuery'
import { useRouter } from 'next/navigation'
import React from 'react'

import DesktopOnly from '@/memberup/components/common/desktop-only'
import { ROUTES } from '@/memberup/settings/router'

const NormalLayout: React.FC<{
  maxWidth?: number
  visibleHeader?: boolean
  visibleProfileMenu?: boolean
  leftHeader?: React.ReactNode
  children: React.ReactNode
}> = ({ maxWidth, leftHeader, visibleHeader, visibleProfileMenu, children }) => {
  const router = useRouter()
  const theme = useTheme()
  const isSmDown = useMediaQuery(theme.breakpoints.down('sm'))

  return (
    <main id="normal-layout" className="main page-inner-py" style={{ maxWidth: '1440px', margin: 'auto' }}>
      <Box
        sx={{
          overflowY: 'auto',
          height: '100%',
          width: '100%',
        }}
      >
        <Box
          className="background-color02"
          sx={{
            borderRadius: isSmDown ? 0 : '16px',
            width: '100%',
            maxWidth: maxWidth || '100%',
            minHeight: '100%',
            margin: 'auto',
            position: 'relative',
          }}
        >
          <Box sx={{ padding: '20px', display: visibleHeader ? 'block' : 'none' }}>
            <Grid container>
              <Grid item>{leftHeader || null}</Grid>
              <Grid item xs></Grid>
            </Grid>
          </Box>
          <Box
            id="page-content"
            sx={{
              position: 'relative',
              width: '100%',
            }}
          >
            {children}
          </Box>
        </Box>
      </Box>
    </main>
  )
}

export default NormalLayout
