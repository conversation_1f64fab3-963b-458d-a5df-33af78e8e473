/**
 * <PERSON><PERSON><PERSON> to import Stripe payment data into the PaymentHistory model
 *
 * This script fetches all invoices from Stripe for both memberup and membership accounts
 * and imports them into the PaymentHistory table.
 *
 * Usage:
 *    - For all users (dry run):
 *      doppler run -- node apps/memberup/scripts/ua-import-stripe-payments-to-history.ts --dryRun
 *
 *    - For all users:
 *      doppler run -- node apps/memberup/scripts/ua-import-stripe-payments-to-history.ts
 *
 *    - For a specific user:
 *      doppler run -- node apps/memberup/scripts/ua-import-stripe-payments-to-history.ts --userId=<user_id>
 *
 *    - For a specific membership:
 *      doppler run -- node  apps/memberup/scripts/ua-import-stripe-payments-to-history.ts --membershipId=<membership_id>
 *
 *    - For a specific date range:
 *      doppler run -- node  apps/memberup/scripts/ua-import-stripe-payments-to-history.ts --startDate=2023-01-01 --endDate=2023-12-31
 *
 * Note: This script requires Stripe API access and will use the configured Stripe keys in your environment.
 */

import 'dotenv/config'

import { PAYMENT_STATUS_ENUM, PAYMENT_TYPE_ENUM, PrismaClient } from '@prisma/client'
import { Stripe } from 'stripe'

import { confirmScriptExecution } from './common.js'

const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY
const STRIPE_SECRET_KEY_TEST = process.env.STRIPE_SECRET_KEY_TEST
const NEXT_PUBLIC_STRIPE_LIVE_MODE = process.env.NEXT_PUBLIC_STRIPE_LIVE_MODE
const { parseArgs } = require('node:util')

// Parse command line arguments
const { values } = parseArgs({
  options: {
    userId: { type: 'string' },
    membershipId: { type: 'string' },
    startDate: { type: 'string' },
    endDate: { type: 'string' },
    dryRun: { type: 'boolean', default: false },
  },
})

const prisma = new PrismaClient()
const stripe = new Stripe(NEXT_PUBLIC_STRIPE_LIVE_MODE === 'true' ? STRIPE_SECRET_KEY : STRIPE_SECRET_KEY_TEST, {
  apiVersion: '2023-10-16',
  maxNetworkRetries: 2,
})

// Convert Stripe status to PaymentHistory status
function mapStripeStatusToPaymentHistoryStatus(status) {
  switch (status) {
    case 'paid':
      return PAYMENT_STATUS_ENUM.paid
    case 'refunded':
      return PAYMENT_STATUS_ENUM.refunded
    case 'failed':
      return PAYMENT_STATUS_ENUM.failed
    default:
      return PAYMENT_STATUS_ENUM.pending
  }
}

// Fetch all users with Stripe customer IDs
async function fetchUsersWithStripeCustomerIds() {
  let users = []

  if (values.userId) {
    // Fetch specific user
    const user = await prisma.user.findUnique({
      where: { id: values.userId },
      include: {
        profile: true,
        user_memberships: {
          include: {
            membership: {
              include: {
                membership_setting: true,
              },
            },
          },
        },
      },
    })

    if (user) users.push(user)
  } else if (values.membershipId) {
    // Fetch users for a specific membership
    const userMemberships = await prisma.userMembership.findMany({
      where: { membership_id: values.membershipId },
      include: {
        user: {
          include: {
            profile: true,
          },
        },
        membership: true,
      },
    })

    users = userMemberships.map((um) => ({
      ...um.user,
      user_memberships: [um],
    }))
  } else {
    // Fetch all users with Stripe customer IDs
    users = await prisma.user.findMany({
      where: {
        OR: [
          { profile: { stripe_customer_id: { not: null } } },
          { user_memberships: { some: { stripe_customer_id: { not: null } } } },
        ],
      },
      include: {
        profile: true,
        user_memberships: {
          include: {
            membership: true,
          },
        },
      },
    })
  }

  return users
}

// Fetch invoices for a customer from Stripe
async function fetchStripeInvoices(customerId, connectedAccountId) {
  const params = {
    customer: customerId,
    limit: 100,
    expand: ['data.charge'],
  }

  // Add date filters if provided
  if (values.startDate || values.endDate) {
    params.created = {}

    if (values.startDate) {
      params.created.gte = Math.floor(new Date(values.startDate).getTime() / 1000)
    }

    if (values.endDate) {
      params.created.lte = Math.floor(new Date(values.endDate).getTime() / 1000)
    }
  }

  const options = connectedAccountId ? { stripeAccount: connectedAccountId } : undefined

  let allInvoices = []
  let hasMore = true
  let startingAfter = undefined

  while (hasMore) {
    if (startingAfter) {
      params.starting_after = startingAfter
    }

    const invoices = await stripe.invoices.list(params, options)
    allInvoices = [...allInvoices, ...invoices.data]

    hasMore = invoices.has_more
    if (invoices.data.length > 0) {
      startingAfter = invoices.data[invoices.data.length - 1].id
    } else {
      hasMore = false
    }
  }

  return allInvoices
}

// Create PaymentHistory record from Stripe invoice
async function createPaymentHistoryFromInvoice(invoice, userId, membershipId, paymentType) {
  // Skip if amount is 0
  if (invoice.amount_paid === 0) {
    console.log(`Skipping invoice ${invoice.id} with amount 0`)
    return
  }

  // Check if this invoice is already in the PaymentHistory
  const existingPayment = await prisma.payment.findFirst({
    where: { stripe_invoice_id: invoice.id },
  })

  if (existingPayment) {
    console.log(`Invoice ${invoice.id} already exists in PaymentHistory`)
    return
  }

  const paymentData = {
    amount: invoice.amount_paid / 100, // Convert from cents to dollars
    currency: invoice.currency,
    description: invoice.description || `Payment for ${paymentType}`,
    payment_date: invoice.status === 'paid' ? new Date(invoice.status_transitions.paid_at * 1000) : null,
    status: mapStripeStatusToPaymentHistoryStatus(invoice.status),
    payment_type: paymentType,
    stripe_invoice_id: invoice.id,
    stripe_payment_intent_id: invoice.payment_intent,
    stripe_charge_id: invoice.charge?.id,
    stripe_receipt_url: invoice.charge?.receipt_url,
    stripe_customer_id: invoice.customer,
    user_id: userId,
    membership_id: membershipId,
  }

  if (values.dryRun) {
    console.log('DRY RUN - Would create payment history:', paymentData)
    return
  }

  try {
    const paymentHistory = await prisma.payment.create({
      data: paymentData,
    })

    console.log(`Created PaymentHistory record for invoice ${invoice.id}`)
    return paymentHistory
  } catch (error) {
    console.error(`Error creating PaymentHistory for invoice ${invoice.id}:`, error)
  }
}

// Main function to import all Stripe payments
async function uaImportStripePaymentsToHistory() {
  const shouldProceed = await confirmScriptExecution()

  if (!shouldProceed) return

  console.log('Starting import of Stripe payments to PaymentHistory...')

  const users = await fetchUsersWithStripeCustomerIds()
  console.log(`Found ${users.length} users with Stripe customer IDs`)

  let totalImported = 0

  for (const user of users) {
    console.log(`Processing user: ${user.first_name} ${user.last_name} (${user.id})`)

    // Process MemberUp payments (from user profile)
    if (user.profile?.stripe_customer_id) {
      console.log(`Fetching MemberUp invoices for customer: ${user.profile.stripe_customer_id}`)

      try {
        const invoices = await fetchStripeInvoices(user.profile.stripe_customer_id)
        console.log(`Found ${invoices.length} MemberUp invoices`)

        for (const invoice of invoices) {
          await createPaymentHistoryFromInvoice(
            invoice,
            user.id,
            null, // No membership for MemberUp payments
            PAYMENT_TYPE_ENUM.memberup,
          )
          totalImported++
        }
      } catch (error) {
        console.error(`Error fetching MemberUp invoices for user ${user.id}:`, error)
      }
    }

    // Process Membership payments (from user_memberships)
    console.log('user.user_memberships', user.user_memberships)
    for (const userMembership of user.user_memberships) {
      if (userMembership.stripe_customer_id && userMembership.membership?.membership_setting?.stripe_connect_account) {
        const connectedAccountId = userMembership.membership.membership_setting.stripe_connect_account.stripe_user_id

        console.log(
          `Fetching Membership invoices for customer: ${userMembership.stripe_customer_id} on account: ${connectedAccountId}`,
        )

        try {
          const invoices = await fetchStripeInvoices(userMembership.stripe_customer_id, connectedAccountId)
          console.log(`Found ${invoices.length} Membership invoices`)

          for (const invoice of invoices) {
            await createPaymentHistoryFromInvoice(
              invoice,
              user.id,
              userMembership.membership_id,
              PAYMENT_TYPE_ENUM.membership,
            )
            totalImported++
          }
        } catch (error) {
          console.error(
            `Error fetching Membership invoices for user ${user.id} and membership ${userMembership.membership_id}:`,
            error,
          )
        }
      }
    }
  }

  console.log(`Import completed. Processed ${totalImported} invoices.`)
}

// Run the import
uaImportStripePaymentsToHistory()
  .then(() => {
    console.log('Script completed successfully')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Script failed:', error)
    process.exit(1)
  })
