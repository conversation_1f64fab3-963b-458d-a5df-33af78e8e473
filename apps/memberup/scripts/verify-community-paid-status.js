require('dotenv').config()

const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const main = async () => {
  try {
    console.log('Verification script started - Connecting to database...')

    // Find all memberships (communities) with their settings
    const memberships = await prisma.membership.findMany({
      include: {
        membership_setting: true,
      },
    })

    console.log(`Found ${memberships.length} memberships to verify`)

    // Count paid communities
    let paidCount = 0
    let shouldBePaidCount = 0

    for (const membership of memberships) {
      let hasPaidPlan = false
      let currentIsPaid = membership.membership_setting?.is_paid || false

      if (currentIsPaid) {
        paidCount++
      }

      if (membership.membership_setting?.stripe_prices) {
        try {
          // Parse the JSON string if needed
          const prices =
            typeof membership.membership_setting.stripe_prices === 'string'
              ? JSON.parse(membership.membership_setting.stripe_prices)
              : membership.membership_setting.stripe_prices

          // Check if at least one price is active
          hasPaidPlan = Array.isArray(prices) && prices.some((price) => price.active === true)

          if (hasPaidPlan) {
            shouldBePaidCount++

            // If there's a mismatch, report it
            if (!currentIsPaid) {
              console.log(
                `ISSUE: Membership ${membership.id} (${membership.name}) has active prices but is_paid = ${currentIsPaid}`,
              )
            }
          }
        } catch (e) {
          console.error(`Error parsing stripe_prices for membership ${membership.id}:`, e)
        }
      }
    }

    // Summary
    console.log('--- Verification Summary ---')
    console.log(`Total memberships: ${memberships.length}`)
    console.log(`Communities marked as paid (is_paid=true): ${paidCount}`)
    console.log(`Communities with active prices: ${shouldBePaidCount}`)
    console.log(`Status: ${paidCount === shouldBePaidCount ? '✅ MATCHES' : '❌ MISMATCH'}`)
    console.log('--- DONE ---')
  } catch (e) {
    console.error('Error during verification:', e)
    process.exit(1)
  } finally {
    console.log('Closing database connection...')
    await prisma.$disconnect()
  }
}

console.log('Starting verification...')
main()
  .then(() => {
    console.log('Verification completed successfully')
  })
  .catch((error) => {
    console.error('Verification failed:', error)
  })
