import { withSentryConfig } from '@sentry/nextjs'
import { NextConfig } from 'next'

const nextConfig: NextConfig = {
  // React Strict Mode is for checking proper usage of React in our app. see https://nextjs.org/docs/api-reference/next.config.js/react-strict-mode
  reactStrictMode: false,
  transpilePackages: ['@memberup/shared', 'next-auth'],
  outputFileTracingExcludes: {
    '*': ['node_modules/canvas/**'],
  },
  images: {
    remotePatterns: [
      { protocol: 'https', hostname: 'res.cloudinary.com' },
      { protocol: 'https', hostname: 'image.mux.com' },
      { protocol: 'https', hostname: '*.giphy.com' },
      { protocol: 'https', hostname: 'source.unsplash.com' },
      { protocol: 'https', hostname: 'images.unsplash.com' },
    ],
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  async rewrites() {
    return [
      {
        source: '/ingest/static/:path*',
        destination: 'https://us-assets.i.posthog.com/static/:path*',
      },
      {
        source: '/ingest/:path*',
        destination: 'https://us.i.posthog.com/:path*',
      },
      {
        source: '/ingest/decide',
        destination: 'https://us.i.posthog.com/decide',
      },
    ]
  },
  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,
}

// const withBundleAnalyzer = require('@next/bundle-analyzer')({
//   enabled: process.env.ANALYZE === 'true',
// })

// @TODO: Add back bundle analyzer once MEM-4005 is solved
// module.exports = withBundleAnalyzer(nextConfig)
export default withSentryConfig(nextConfig, {
  org: 'memberup-llc',
  project: 'memberup-main',
  authToken: process.env.SENTRY_AUTH_TOKEN,
  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Only print logs for uploading source maps in CI
  // Set to `true` to suppress logs
  silent: !process.env.CI,

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,
})
