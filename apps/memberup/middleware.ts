import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

import { nonCommunityPathnames } from './lib/constants'

export function middleware(request: NextRequest) {
  const pathname = new URL(request.url).pathname
  const response = NextResponse.next()
  response.headers.set('x-pathname', pathname)

  if (pathname && !pathname.startsWith('/@')) {
    const pathnameParts = pathname.split('/')

    if (pathnameParts.length > 1 && !nonCommunityPathnames.includes(`/${pathnameParts[1]}`)) {
      response.headers.set('x-community-slug', pathnameParts[1])
    }
  }

  return response
}

export const config = {
  matcher: '/((?!api/|_next/|assets/|favicon.ico|robots.txt|sitemap.xml).*)',
}
