'use client'

import posthog from 'posthog-js'
import { useEffect, useRef, useState } from 'react'

import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { DarkModeSVG } from '@/components/images/svgs/dark-mode'
import { LightModeSVG } from '@/components/images/svgs/light-mode'
import { Button } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useUpdateUserProfile } from '@/hooks/users/useUpdateUserProfile'
import { useStore } from '@/hooks/useStore'
import { formSubmitError } from '@/lib/error-messages'
import { UserEvents } from '@/lib/posthog'
import { cn } from '@/lib/utils'
import { mergeUserProfile } from '@/memberup/store/store'
import { useAppDispatch } from '@/src/store/hooks'
import { AuthForms } from '@/store/authSlice'

const ThemeModeSelector = ({
  handleThemeModeChange,
  mode,
  selectedThemeMode,
  variant,
  children,
}: {
  children: React.ReactNode
  handleThemeModeChange: (mode: string) => void
  mode: THEME_MODE_ENUM
  selectedThemeMode: THEME_MODE_ENUM
  variant: 'modal' | 'settings'
}) => (
  <div className="group cursor-pointer" onClick={() => handleThemeModeChange(mode)}>
    <div
      className={cn(
        'w-full cursor-pointer rounded-[6%_/_9.252%] border-2 p-0.5 transition-colors',
        mode === selectedThemeMode
          ? 'border-primary-300'
          : 'border-transparent group-hover:border-black-100 dark:group-hover:border-black-200',
      )}
    >
      {children}
    </div>
    <div
      className={cn('text-white ml-1 mt-1 text-xs font-semibold capitalize', variant === 'settings' && 'sm:text-sbase')}
    >
      {mode}
    </div>
  </div>
)

export function CustomizeYourThemeForm({
  buttonText,
  variant,
}: {
  buttonText: string
  variant?: 'modal' | 'settings'
}) {
  const dispatch = useAppDispatch()
  const profile = useStore((state) => state.auth.profile)
  const updateProfile = useStore((state) => state.auth.updateProfile)
  const setShowForm = useStore((state) => state.auth.setShowForm)
  const [selectedThemeMode, setSelectedThemeMode] = useState(profile.theme_mode || THEME_MODE_ENUM.dark)
  const preUpdateThemeMode = useRef(profile.theme_mode)
  const { saving, updateUserProfile } = useUpdateUserProfile()

  const handleNext = async () => {
    try {
      const success = await updateUserProfile({ theme_mode: selectedThemeMode })

      if (success) {
        setShowForm(null)
        preUpdateThemeMode.current = null
        if (variant === 'settings') {
          toast.success('Theme updated successfully')
          posthog.capture(UserEvents.SETTINGS_UPDATED, {
            theme: selectedThemeMode,
          })
        } else {
          posthog.capture(UserEvents.ONBOARDING_SELECTED_THEME, {
            theme: selectedThemeMode,
          })
        }
      } else {
        toast.error(formSubmitError)
      }
    } catch (error) {
      toast.error(formSubmitError)
    }
  }

  const handleThemeModeChange = (mode: THEME_MODE_ENUM) => {
    if (variant === 'modal') setShowForm(AuthForms.customizeTheme)
    setSelectedThemeMode(mode)
    dispatch(mergeUserProfile({ theme_mode: mode }))
    updateProfile({ theme_mode: mode })
  }

  useEffect(() => {
    return () => {
      if (preUpdateThemeMode.current) {
        dispatch(mergeUserProfile({ theme_mode: preUpdateThemeMode.current }))
        updateProfile({ theme_mode: preUpdateThemeMode.current })
      }
    }
  }, [])

  return (
    <div>
      <div className="flex w-full flex-col space-y-5">
        <div
          className={cn(
            'flex flex-col space-y-4 sm:flex-row sm:space-x-5 sm:space-y-0',
            variant === 'modal' ? 'sm:px-5' : 'sm:px-4',
          )}
        >
          <ThemeModeSelector
            handleThemeModeChange={handleThemeModeChange}
            mode="light"
            selectedThemeMode={selectedThemeMode}
            variant={variant}
          >
            <LightModeSVG className="h-auto w-full" />
          </ThemeModeSelector>
          <ThemeModeSelector
            handleThemeModeChange={handleThemeModeChange}
            mode="dark"
            selectedThemeMode={selectedThemeMode}
            variant={variant}
          >
            <DarkModeSVG className="h-auto w-full" />
          </ThemeModeSelector>
        </div>

        <Button
          className={'w-full'}
          type="submit"
          variant="default"
          loading={saving}
          onClick={handleNext}
          disabled={saving}
          data-cy="verify-email-button"
        >
          {buttonText}
        </Button>
      </div>
    </div>
  )
}
