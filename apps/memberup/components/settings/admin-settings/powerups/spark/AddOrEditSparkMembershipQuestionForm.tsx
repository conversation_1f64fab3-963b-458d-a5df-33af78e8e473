import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import React, { useEffect, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Button, Input } from '@/components/ui'
import { Form, FormControl, FormCounter, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { createSparkMembershipQuestionApi, updateSparkMembershipQuestionApi } from '@/shared-services/apis/spark.api'
import { ISparkMembershipQuestion } from '@/shared-types/interfaces'
import { selectMembership } from '@/src/store/features/membershipSlice'
import { useAppDispatch, useAppSelector } from '@/src/store/hooks'

const MAX_CONTENT_LENGTH = 75

export default function AddOrEditSparkMembershipQuestionForm({
  sparkMembershipQuestion,
  categoryId,
  onSaveSuccess,
  onCancel,
}) {
  const sparkMembershipQuestionSchema = z.object({
    content: z.string().max(MAX_CONTENT_LENGTH),
  })
  const [isRequesting, setIsRequesting] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const membership = useStore((state) => state.community.membership)

  type MembershipQuestionSchemaType = z.infer<typeof sparkMembershipQuestionSchema>
  const sparkMembershipQuestionForm = useForm<MembershipQuestionSchemaType>({
    mode: 'onBlur',
    reValidateMode: 'onSubmit',
    defaultValues: {
      content: sparkMembershipQuestion?.content || '',
    },
    resolver: zodResolver(sparkMembershipQuestionSchema),
  })

  useEffect(() => {
    setTimeout(() => {
      inputRef.current?.focus()
    }, 100)
  }, [])

  const onSubmit = async (data: MembershipQuestionSchemaType) => {
    try {
      let result
      setIsRequesting(true)
      if (sparkMembershipQuestion) {
        result = await updateSparkMembershipQuestionApi(
          sparkMembershipQuestion.id,
          {
            content: data.content,
          } as ISparkMembershipQuestion,
          membership.id,
        )
      } else {
        result = await createSparkMembershipQuestionApi(
          {
            content: data.content,
            spark_membership_category_id: categoryId,
          },
          membership.id,
        )
      }
      setIsRequesting(false)
      onSaveSuccess(result.data.data)
    } catch (err) {
      toast.error(err.message)
    }
  }

  const handleCancelButtonClick = () => {
    onCancel?.()
  }

  if (!categoryId && !sparkMembershipQuestion?.id) {
    return null
  }

  return (
    <div className={'text-white w-full items-center gap-2 rounded-[10px] bg-black-300 p-4 text-xs font-semibold'}>
      <Form {...sparkMembershipQuestionForm}>
        <FormField
          className={'w-full'}
          control={sparkMembershipQuestionForm.control}
          name="content"
          render={({ field, fieldState: { error } }) => (
            <FormItem>
              <FormControl className={''}>
                <Input
                  ref={inputRef}
                  className="w-full"
                  autoComplete="name"
                  maxLength={MAX_CONTENT_LENGTH}
                  disabled={isRequesting}
                  type="text"
                  placeholder={!sparkMembershipQuestion && 'Add a question...'}
                  error={Boolean(error)}
                  {...field}
                />
              </FormControl>
              {error && <FormMessage>{error.message}</FormMessage>}
              <FormCounter>
                {sparkMembershipQuestionForm.getValues('content').length}/{MAX_CONTENT_LENGTH}
              </FormCounter>
              <div className={'flex justify-end gap-4'}>
                <Button
                  type="submit"
                  variant="outline"
                  disabled={isRequesting}
                  onClick={handleCancelButtonClick}
                  data-cy="cancel-changes-button"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="default"
                  loading={isRequesting}
                  disabled={!sparkMembershipQuestionForm.formState.isDirty || isRequesting}
                  onClick={sparkMembershipQuestionForm.handleSubmit(onSubmit)}
                  data-cy="save-changes-button"
                >
                  Save
                </Button>
              </div>
            </FormItem>
          )}
        />
      </Form>
    </div>
  )
}
