import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import React from 'react'

import { MoreHorizontal20Icon } from '@/components/icons'
import { Drag20Icon } from '@/components/icons/20px/drag-20-icon'
import { Button } from '@/components/ui'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'

export function SortableQuestionItem({ id, item, index, onEdit, onDelete, getInputTypeString, loading }) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    disabled: loading,
    id,
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 1,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        'flex w-full items-center rounded-xl bg-white-100 pr-5 dark:bg-black-300',
        isDragging && 'opacity-50',
      )}
    >
      <div {...attributes} {...listeners} className="cursor-grab px-2 py-6">
        <Drag20Icon className="h-4 w-4" />
      </div>
      <div className="grow space-y-3 py-5 pr-4">
        <div className="text-sm font-semibold text-white-500">{item.label}</div>
        <div className="text-sm font-normal text-black-100">{getInputTypeString(item.inputType)}</div>
      </div>
      <div className="flex items-center space-x-1">
        <Button size="sm" variant="outline" onClick={() => onEdit(index)}>
          Edit
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button className="flex items-center justify-center px-2.5 py-0" size="sm" variant="outline">
              <MoreHorizontal20Icon />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem className="cursor-pointer" onClick={() => onDelete(index)}>
              <span className="font-semibold text-red-200">Delete</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
