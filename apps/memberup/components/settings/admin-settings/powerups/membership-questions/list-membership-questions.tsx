import { DndContext, Drag<PERSON>nd<PERSON><PERSON>, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { restrictToParentElement } from '@dnd-kit/modifiers'
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import React, { useId } from 'react'

import { SortableQuestionItem } from './sortable-question-item'
import { Button, Switch } from '@/components/ui'

const MAX_MEMBER_QUESTIONS = 3

export default function ListMembershipQuestions({
  formData,
  formEnabled,
  setFormData,
  setFormEnabled,
  onEdit,
  onAddQuestion,
  onBackToPowerUpsClick,
  loading,
}) {
  const questionLimitReached = formData?.fields?.length >= MAX_MEMBER_QUESTIONS

  const handleAddQuestionClick = () => {
    onAddQuestion()
  }

  const handleEnableMembershipQuestion = async () => {
    setFormEnabled(!formEnabled)
  }

  const getInputTypeString = (inputType) => {
    switch (inputType) {
      case 'number':
        return 'Numeric'
      case 'textbox':
        return 'Text Box'
      case 'multiple_choice':
        return 'Multiple Choice'
      case 'email':
        return 'Email'
      default:
        return inputType
    }
  }

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 4,
      },
    }),
  )

  const dndContextId = useId() // Required to prevent hydration error

  const onDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event

    if (!over || active.id === over.id) {
      return
    }

    const activeIndex = parseInt(active.id.toString())
    const overIndex = parseInt(over.id.toString())

    const newForm = JSON.parse(JSON.stringify(formData))
    newForm.fields = arrayMove(newForm.fields, activeIndex, overIndex)
    setFormData(newForm)
  }

  const handleOnEditClick = (index) => {
    onEdit(index)
  }

  const handleOnMoreOptions = (index) => {
    const newForm = JSON.parse(JSON.stringify(formData))
    newForm.fields.splice(index, 1)
    setFormData(newForm)
  }

  return (
    <div>
      <div className="flex items-center gap-4">
        <div className="text-white flex flex-grow flex-col items-start text-base font-semibold">
          Membership Questions
          <Button
            className="inline-block text-sm font-medium leading-normal text-primary-100 hover:underline"
            onClick={onBackToPowerUpsClick}
            variant="inline"
          >
            Back to power-ups
          </Button>
        </div>
        <Switch checked={formEnabled} onClick={handleEnableMembershipQuestion} />
        <Button
          disabled={questionLimitReached}
          type="submit"
          size="sm"
          variant="default"
          onClick={handleAddQuestionClick}
        >
          Add Questions
        </Button>
      </div>
      {(!formData || formData.fields?.length === 0) && (
        <div className="text-sm font-normal text-gray-400">
          Ask members up to three questions when they request to join your group. Only admins and moderators will see
          the answers.
        </div>
      )}
      <div className="mt-8">
        {formData && formData.fields?.length > 0 && (
          <DndContext id={dndContextId} onDragEnd={onDragEnd} modifiers={[restrictToParentElement]} sensors={sensors}>
            <SortableContext
              items={formData.fields.map((_: any, index: number) => index.toString())}
              strategy={verticalListSortingStrategy}
            >
              <div className="flex flex-col gap-4">
                {formData.fields.map((item: object, index: number) => (
                  <SortableQuestionItem
                    key={index}
                    id={index.toString()}
                    item={item}
                    index={index}
                    onEdit={handleOnEditClick}
                    onDelete={handleOnMoreOptions}
                    getInputTypeString={getInputTypeString}
                    loading={loading}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        )}
      </div>
    </div>
  )
}
