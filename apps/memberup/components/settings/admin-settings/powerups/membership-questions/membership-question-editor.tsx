import { zodResolver } from '@hookform/resolvers/zod'
import { cloneDeep } from 'lodash'
import React, { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { uuidv7 } from 'uuidv7'
import { z } from 'zod'

import { Button, Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'
import { Form, FormControl, FormCounter, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'

const MAX_LABEL_LENGTH = 150

export default function MembershipQuestionEditor({
  mode = 'add',
  selectedQuestionIndex,
  onCancel,
  onSaveSuccess,
  onBackToPowerUpsClick,
  formData,
  setFormData,
}) {
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = membership.membership_setting
  const [isSaving, setIsSaving] = useState(false)
  const isEditMode = mode === 'edit'

  const questionSchema = z.object({
    label: z.string().min(1).max(MAX_LABEL_LENGTH),
    input_type: z.string().min(1),
  })
  type QuestionSchemaType = z.infer<typeof questionSchema>

  const questionForm = useForm<QuestionSchemaType>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: {
      label: '',
      input_type: '',
    },
    resolver: zodResolver(questionSchema),
  })

  // Initialize form with existing data if in edit mode
  useEffect(() => {
    if (isEditMode && formData && formData.fields && formData.fields[selectedQuestionIndex]) {
      const question = formData.fields[selectedQuestionIndex]
      questionForm.setValue('label', question.label)
      questionForm.setValue('input_type', question.inputType)
    }
  }, [isEditMode, selectedQuestionIndex, formData])

  const handleOnCancelClick = () => {
    onCancel()
  }

  const onSubmit = async (data: QuestionSchemaType) => {
    setIsSaving(true)
    try {
      const newForm = cloneDeep(formData)

      if (isEditMode) {
        // Update existing question
        newForm.fields[selectedQuestionIndex] = {
          ...newForm.fields[selectedQuestionIndex],
          label: data.label,
          inputType: data.input_type,
        }
      } else {
        // Add new question
        newForm.fields.push({
          id: uuidv7(),
          label: data.label,
          config: { required: 'This field is required' },
          inputType: data.input_type,
          defaultValue: '',
        })
      }

      setFormData(newForm)
      onSaveSuccess()
    } catch (error) {
      toast.error(`Failed to ${isEditMode ? 'update' : 'add'} question`)
    }
    setIsSaving(false)
  }

  return (
    <>
      <div className="flex items-center gap-4">
        <div className="text-white flex-grow font-['Graphik'] text-base font-semibold leading-normal">
          {isEditMode ? 'Edit Question' : 'Add Question'}
        </div>
      </div>

      <div className="mt-6 flex flex-col space-y-4">
        <Form {...questionForm}>
          <FormField
            control={questionForm.control}
            name="label"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <Input
                    className="w-full"
                    placeholder="Question"
                    type="text"
                    name="label"
                    maxLength={MAX_LABEL_LENGTH}
                    disabled={isSaving}
                    error={Boolean(error)}
                    {...field}
                  />
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
                <FormCounter>
                  {questionForm.getValues('label').length}/{MAX_LABEL_LENGTH}
                </FormCounter>
              </FormItem>
            )}
          />
          <FormField
            control={questionForm.control}
            name="input_type"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <Select
                    placeholder="Response Type"
                    disabled={isSaving}
                    error={Boolean(error)}
                    empty={field.value === 'empty'}
                    value={field.value}
                    name={field.name}
                    onValueChange={field.onChange}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="empty"></SelectItem>
                      <SelectItem value="textbox">Text Box</SelectItem>
                      <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
                      <SelectItem value="email">Email Address</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
              </FormItem>
            )}
          />
        </Form>
        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" disabled={isSaving} onClick={handleOnCancelClick}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="default"
            disabled={!questionForm.formState.isValid || isSaving}
            loading={isSaving}
            onClick={questionForm.handleSubmit(onSubmit)}
            data-cy="save-changes-button"
          >
            Save
          </Button>
        </div>
      </div>
    </>
  )
}
