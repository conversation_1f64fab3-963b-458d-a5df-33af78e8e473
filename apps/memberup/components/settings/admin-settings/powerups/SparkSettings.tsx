import { useRouter } from 'next/navigation'
import React, { useEffect, useState } from 'react'

import { LoaderCircle24Icon } from '@/components/icons'
import AddOrEditSparkMembershipQuestionForm from '@/components/settings/admin-settings/powerups/spark/AddOrEditSparkMembershipQuestionForm'
import ManageSparkCategory from '@/components/settings/admin-settings/spark/ManageSparkCategory'
import { Button, Switch } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import {
  enableSparkApi,
  getSparkMembershipCategoriesApi,
  setSelectedSparkMembershipCategoryApi,
} from '@/shared-services/apis/spark.api'
import { getCommunityBaseURL } from '@/src/libs/utils'
import { updateMembershipSettingSuccess } from '@/src/store/features/membershipSlice'
import { useAppDispatch } from '@/src/store/hooks'

export function SparkSettings({ onBackToPowerUpsClick }) {
  const dispatch = useAppDispatch()
  const router = useRouter()
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = membership?.membership_setting
  const setMembership = useStore((state) => state.community.setMembership)
  const [isLoading, setIsLoading] = useState(true)
  const [categories, setCategories] = useState([])
  const [selectedCategoryId, setSelectedCategoryId] = useState(null)
  const [isAddingQuestion, setIsAddingQuestion] = useState(false)
  const [questions, setQuestions] = useState([])
  const [isCategoryLoading, setIsCategoryLoading] = useState(false)

  useEffect(() => {
    if (!membership || !membershipSetting) {
      return
    }
    const init = async () => {
      const res = await getSparkMembershipCategoriesApi(membership.id)
      setCategories(res.data.data)
      setIsLoading(false)
      // @ts-ignore
      setSelectedCategoryId(membershipSetting.spark_current_membership_category_id)
    }
    init()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [membership, membershipSetting])

  const handleClickCategory = (category) => {
    router.push(`${getCommunityBaseURL(membership)}/settings/spark/${category.slug}`)
  }

  const isSparkEnabled = membershipSetting?.spark_enabled && !isLoading
  const canAddQuestion = Boolean(selectedCategoryId) && isSparkEnabled && !isLoading

  const handleSparkEnabledChange = async (e) => {
    try {
      const result = await enableSparkApi(!isSparkEnabled, membership.id)
      setMembership(result.data.data)
      toast.success(result.data.data.spark_enabled ? 'Spark was enabled' : 'Spark was disabled', 'success')
    } catch (e) {}
  }

  const handleAddQuestion = () => {
    setIsAddingQuestion(true)
  }

  const handleAddQuestionSaveSuccess = (createdQuestion) => {
    const updatedQuestions = [...questions, createdQuestion]
    setQuestions(updatedQuestions)
    setIsAddingQuestion(false)
    toast.success('New question successfully added')
  }

  const handleEditQuestionSaveSuccess = (updatedQuestion) => {
    const updatedQuestions = questions.map((q) => {
      if (q.id === updatedQuestion.id) {
        return updatedQuestion
      }
      return q
    })
    setQuestions(updatedQuestions)
    toast.success('Updated Spark question successfully')
  }

  const handleSelectedCategoryChanged = async (category) => {
    setIsCategoryLoading(true)
    setSelectedCategoryId(category.id)
    await setSelectedSparkMembershipCategoryApi(category.id, membership.id)
    toast.success(`Category "${category.name}" has been set to active`, 'success')
    setIsCategoryLoading(false)
  }

  // @ts-ignore
  return (
    <div className={''}>
      <div className={'mb-6 space-y-3'}>
        <div className={'flex items-center gap-4'}>
          <div className="text-white flex flex-grow flex-col items-start text-base font-semibold">
            Spark
            <Button
              className="inline-block text-sm font-medium leading-normal text-primary-100 hover:underline"
              onClick={onBackToPowerUpsClick}
              variant="inline"
            >
              Back to power-ups
            </Button>
          </div>
          <Switch checked={isSparkEnabled} onClick={handleSparkEnabledChange} />

          {!isAddingQuestion && (
            <Button
              type="button"
              variant="default"
              loading={false}
              disabled={!canAddQuestion}
              size={'sm'}
              onClick={handleAddQuestion}
              data-cy="add-question-button"
            >
              Add Question
            </Button>
          )}
          {isAddingQuestion && (
            <Button
              type="button"
              variant="outline"
              size={'sm'}
              onClick={() => setIsAddingQuestion(false)}
              data-cy="cancel-add-question-button"
            >
              Cancel Add Question
            </Button>
          )}
        </div>
        <div className="text-sm font-normal leading-snug text-black-100">
          Spark automatically posts a new question in your community every 24 hours. Answers are only visible to members
          after they’ve responded. Select a question pack to get started.
          <span className={'text-primary-100'}> Learn more.</span>
        </div>
        {isLoading && (
          <div>
            <LoaderCircle24Icon className="mr-2 h-[1.125rem] w-[1.125rem] animate-spin" />
          </div>
        )}
        {isSparkEnabled && (
          <div className={'flex flex-wrap gap-2'}>
            {categories?.map((c) => {
              return (
                <Button
                  key={c.id}
                  variant={selectedCategoryId === c.id ? 'default' : 'outline'}
                  size="xs"
                  onClick={() => handleSelectedCategoryChanged(c)}
                  disabled={isCategoryLoading}
                  loading={isCategoryLoading && selectedCategoryId === c.id}
                >
                  {c.name}
                </Button>
              )
            })}
          </div>
        )}
      </div>

      {isAddingQuestion && (
        <AddOrEditSparkMembershipQuestionForm
          categoryId={selectedCategoryId}
          onSaveSuccess={handleAddQuestionSaveSuccess}
          onCancel={() => setIsAddingQuestion(false)}
        />
      )}

      {isSparkEnabled && selectedCategoryId && !isCategoryLoading && (
        <ManageSparkCategory
          category={selectedCategoryId}
          questions={questions}
          setQuestions={setQuestions}
          onEditQuestionSaveSuccess={handleEditQuestionSaveSuccess}
        />
      )}
    </div>
  )
}
