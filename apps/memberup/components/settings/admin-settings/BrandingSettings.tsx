import React, { useState } from 'react'
import { CopyToClipboard } from 'react-copy-to-clipboard'
import { useForm } from 'react-hook-form'

import { NOTIFICATION_SETTINGS } from '@memberup/shared/src/settings/notifications'
import { Button } from '@/components/ui'
import { selectRequestUpdateProfile, selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { selectMembership } from '@/src/store/features/membershipSlice'

export function BrandingSettings() {
  const userProfile = useAppSelector((state) => selectUserProfile(state))
  const membership = useAppSelector((state) => selectMembership(state))
  const [copied, setCopied] = useState(false)

  const defaultValues: Record<string, string> = NOTIFICATION_SETTINGS.reduce((acc, value) => {
    const emailValue = userProfile?.enable_notifications?.[value.name]?.['email']
    const inAppValue = userProfile?.enable_notifications?.[value.name]?.['in_app_feed']

    acc[`${value.name}`] = {
      email: emailValue !== undefined ? emailValue : value.email,
      in_app_feed: inAppValue !== undefined ? inAppValue : value.in_app_feed,
    }

    return acc
  }, {})

  const form = useForm({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues,
  })

  const handleOnCopy = () => {
    setCopied(true)
    setTimeout(() => {
      setCopied(false)
    }, 3000)
  }
  const communityInviteLink = `https://www.memberup.com/${membership.slug}/about`

  return (
    <div className={'space-y-4'}>
      <h2 className="text-lg font-semibold text-white-500">Branding</h2>
      <div className="font-['Graphik'] text-sm font-normal text-[#8d94a3]">
        Create your vibe and magnetize your tribe.
      </div>
    </div>
  )
}
