import { useSearchParams } from 'next/navigation'
import React, { useEffect, useState } from 'react'

import AddPrice from '@/components/settings/admin-settings/pricing/AddPrice'
import ListPrices from '@/components/settings/admin-settings/pricing/ListPrices'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'

export function PricingSettings() {
  const membership = useStore((state) => state.community.membership)
  const membershipSettings = useStore((state) => state.community.membership.membership_setting)

  const [view, setView] = useState('listing')

  const searchParams = useSearchParams()
  const messageKey = searchParams.get('message_key')

  useEffect(() => {
    if (messageKey === 'EXPIRED') {
      toast.error('The connect to Stripe action expired. Please try again.')
    } else if (messageKey === 'CONNECTED') {
      toast.success('You community has been connected to Stripe.')
    }
  }, [messageKey])

  const handleAddPrice = () => {
    setView('add-or-edit')
  }

  const handleBackToListing = () => {
    setView('listing')
  }

  if (view === 'listing') {
    return <ListPrices membership={membership} membershipSettings={membershipSettings} onPriceAdd={handleAddPrice} />
  } else {
    return (
      <AddPrice
        membership={membership}
        onBack={handleBackToListing}
        onCancel={handleBackToListing}
        onSuccess={handleBackToListing}
      />
    )
  }
}
