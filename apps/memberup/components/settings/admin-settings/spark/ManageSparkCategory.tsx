import React, { useEffect, useState } from 'react'

import { Close24Icon, Drag24Icon } from '@/components/icons'
import AddOrEditSparkMembershipQuestionForm from '@/components/settings/admin-settings/powerups/spark/AddOrEditSparkMembershipQuestionForm'
import { Button } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { toast } from '@/components/ui/sonner'
import { deleteSparkMembershipQuestionApi, getSparkMembershipQuestionsApi } from '@/shared-services/apis/spark.api'
import { selectMembership } from '@/src/store/features/membershipSlice'
import { useAppSelector } from '@/src/store/hooks'

export default function ManageSparkCategory({
  category: categoryId,
  questions,
  setQuestions,
  onEditQuestionSaveSuccess,
}) {
  const [isLoading, setIsLoading] = useState(false)
  const membership = useAppSelector((state) => selectMembership(state))
  const [openWarningDelete, setOpenWarningDelete] = useState(false)
  const [isRequesting, setIsRequesting] = useState(false)
  const [selectedQuestion, setSelectedQuestion] = useState()
  const [selectedEditQuestion, setSelectedEditQuestion] = useState(null)

  useEffect(() => {
    if (!categoryId || !membership) {
      return
    }

    const init = async () => {
      setIsLoading(true)
      const res = await getSparkMembershipQuestionsApi(membership.id, categoryId)
      setQuestions(res.data.data)
      setIsLoading(false)
    }

    init()
  }, [categoryId, membership])

  const handleDeleteConfirm = async () => {
    try {
      setIsRequesting(true)
      // @ts-ignore
      await deleteSparkMembershipQuestionApi(selectedQuestion.id, membership.id)
      // @ts-ignore
      const filteredQuestions = questions.filter((q) => q.id !== selectedQuestion.id)
      setQuestions(filteredQuestions)
      toast.success('Question deleted successfully')
    } catch (e) {
      console.error(e)
      toast.error('Failed to delete the question')
    } finally {
      setIsRequesting(false)
      setOpenWarningDelete(false)
    }
  }
  const handleDeleteQuestion = (question) => {
    setSelectedQuestion(question)
    setOpenWarningDelete(true)
  }

  const handleEditQuestionClick = (question) => {
    setSelectedEditQuestion(question)
  }

  const handleEditQuestionSaveSuccess = (updatedQuestion) => {
    setSelectedEditQuestion(null)
    onEditQuestionSaveSuccess(updatedQuestion)
  }
  return (
    <>
      <ConfirmModal
        title="Are you sure you want to delete the question?"
        onConfirm={handleDeleteConfirm}
        open={openWarningDelete}
        loading={isRequesting}
        onClose={() => setOpenWarningDelete(false)}
      />
      {isLoading && <div className={'text-sm'}>Loading...</div>}
      <div className={'mt-3 flex flex-col space-y-3'}>
        {!isLoading &&
          questions?.map((q) => {
            return q.id === selectedEditQuestion?.id ? (
              <AddOrEditSparkMembershipQuestionForm
                sparkMembershipQuestion={q}
                onCancel={() => setSelectedEditQuestion(null)}
                onSaveSuccess={handleEditQuestionSaveSuccess}
              />
            ) : (
              <div
                key={q.id}
                className={'text-white flex items-center gap-2 rounded-[10px] bg-black-300 p-4 text-xs font-semibold'}
              >
                <div className={'flex-grow cursor-pointer'} onClick={() => handleEditQuestionClick(q)}>
                  {q.content}
                </div>
                <Button variant={'inline'} disabled={isRequesting}>
                  <Close24Icon
                    onClick={() => {
                      handleDeleteQuestion(q)
                    }}
                  />
                </Button>
              </div>
            )
          })}
      </div>
    </>
  )
}
