import { zod<PERSON><PERSON><PERSON>ver } from '@hookform/resolvers/zod'
import React, { useEffect, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { getRandomStr } from '@memberup/shared/src/libs/string-utils'
import { Button, Input, Switch } from '@/components/ui'
import { Form, FormControl, FormCounter, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { createChannelApi, updateChannelApi } from '@/shared-services/apis/channel.api'
import { selectRequestUpsertChannel } from '@/src/store/features/spaceSlice'
import { useAppDispatch, useAppSelector } from '@/src/store/hooks'

const MAX_NAME_LENGTH = 16
const MAX_DESCRIPTION_LENGTH = 24
export default function SpaceSettingsEditSpace({ space, onCancel, onSuccess }) {
  const requestUpsertChannel = useAppSelector((state) => selectRequestUpsertChannel(state))
  const requestUpsertChannelRef = useRef(false)
  const membership = useStore((state) => state.community.membership)
  const setSpaces = useStore((state) => state.community.setSpaces)
  const [isRequesting, setIsRequesting] = useState(false)

  const spaceSchema = z.object({
    name: z.string().max(MAX_NAME_LENGTH).min(1),
    description: z.string().max(MAX_DESCRIPTION_LENGTH),
    is_private: z.boolean(),
  })

  const spaces = membership?.channels

  type SpaceSchemaType = z.infer<typeof spaceSchema>

  const spaceForm = useForm<SpaceSchemaType>({
    mode: 'onBlur',
    reValidateMode: 'onSubmit',
    defaultValues: {
      name: space?.name || '',
      description: space?.description || '',
      is_private: space?.is_private || false,
    },
    resolver: zodResolver(spaceSchema),
  })

  const onSubmit = async (data: SpaceSchemaType) => {
    try {
      setIsRequesting(true)
      requestUpsertChannelRef.current = true
      const payload: any = { ...data }
      payload.id = space?.id
      if (space?.name !== data.name) {
        const slug = data.name
          .toLowerCase()
          .replace(/[^a-z0-9-\s]/g, '')
          .replace(/\s\s*/g, '-')
          .replace(/-+/g, '-')
          .replace(/-$/, '')
        let randomStr = ''

        while ((spaces || []).find((s) => s.id !== space?.id && s.slug === `${slug}${randomStr}`)) {
          randomStr = '-' + getRandomStr(6)
        }
        payload.slug = `${slug}${randomStr}`
      }
      if (payload.id) {
        const result = await updateChannelApi(payload.id, payload)
        const updatedSpace = result.data.data
        const updatedSpaces = spaces.map((m) => {
          if (m.id === payload.id) {
            return updatedSpace
          } else {
            return m
          }
        })
        setSpaces(updatedSpaces)
      } else {
        const result = await createChannelApi(payload, membership.id)
        const newSpace = result.data.data
        const updatedSpaces = [...spaces, newSpace]
        setSpaces(updatedSpaces)
      }
      onSuccess?.()
    } catch (e) {
      toast.error(e.message)
    } finally {
      setIsRequesting(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    }
  }

  return (
    <div>
      <h2 className="mb-8 text-lg font-semibold text-white-500">{space ? 'Edit Space' : 'Add Space'}</h2>
      <Form {...spaceForm}>
        <div className={'flex w-full flex-col space-y-6'}>
          <FormField
            control={spaceForm.control}
            name="name"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <Input
                    className="w-full"
                    autoComplete="name"
                    maxLength={MAX_NAME_LENGTH}
                    disabled={requestUpsertChannel}
                    type="text"
                    placeholder="Name"
                    error={Boolean(error)}
                    {...field}
                  />
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
                <FormCounter>
                  {spaceForm.getValues('name').length}/{MAX_NAME_LENGTH}
                </FormCounter>
              </FormItem>
            )}
          />
          <FormField
            control={spaceForm.control}
            name="description"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <Input
                    className="w-full"
                    autoComplete="description"
                    maxLength={MAX_DESCRIPTION_LENGTH}
                    disabled={requestUpsertChannel}
                    type="text"
                    placeholder="Description"
                    error={Boolean(error)}
                    {...field}
                  />
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
                <FormCounter>
                  {spaceForm.getValues('description').length}/{MAX_DESCRIPTION_LENGTH}
                </FormCounter>
              </FormItem>
            )}
          />

          <div className={'flex'}>
            <div className={'grow'}>
              <div className="text-white text-sm font-medium leading-none">
                <div className={'flex flex-col space-y-2'}>
                  <div>Posting permissions</div>
                  <div className={'flex items-center gap-4'}>
                    <FormField
                      control={spaceForm.control}
                      name="is_private"
                      render={({ field: { value } }) => (
                        <FormItem>
                          <FormControl>
                            <Switch
                              checked={value}
                              onClick={() => spaceForm.setValue('is_private', !value, { shouldDirty: true })}
                            ></Switch>
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    Admin Only
                  </div>
                </div>
              </div>
            </div>
            <div className={'flex flex-row gap-2'}>
              <Button
                type="button"
                variant="outline"
                disabled={isRequesting}
                onClick={() => handleCancel()}
                data-cy="cancel-button"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="default"
                loading={isRequesting}
                disabled={isRequesting || !spaceForm.formState.isValid || !spaceForm.formState.isDirty}
                onClick={spaceForm.handleSubmit(onSubmit)}
                data-cy="save-changes-button"
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      </Form>
    </div>
  )
}
