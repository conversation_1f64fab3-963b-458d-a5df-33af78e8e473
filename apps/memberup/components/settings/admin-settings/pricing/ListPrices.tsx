import LinkIcon from '@mui/icons-material/Link'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import React, { useEffect, useRef, useState } from 'react'

import { Badge, Button } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { SkeletonBox } from '@/components/ui/skeleton'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'
import {
  deleteStripePriceApi,
  getStripeAccountApi,
  getStripeConnectLinkApi,
  getStripePricesApi,
  setStripePriceAsDefaultApi,
} from '@/shared-services/apis/stripe.api'
import { PRICE_ID_FREE_COMMUNITY } from '@/shared-types/consts'
import { formatPrice, getCommunityURL } from '@/src/libs/utils'

export default function ListPrices({ membership, membershipSettings, onPriceAdd }) {
  const isPricingEnabled = Boolean(membershipSettings?.is_pricing_enabled)
  const isFreeCommunity = !isPricingEnabled
  const [isLoading, setIsLoading] = useState(false)
  const [isRequestingNewPrice, setIsRequestingNewPrice] = useState(false)
  const [isRequestingConnectStripe, setIsRequestingConnectStripe] = useState(false)
  const isStripeConnectedButOnPending = false
  const [stripePrices, setStripPrices] = useState([])
  const [openWarningDelete, setOpenWarningDelete] = useState(false)
  const [selectedStripePrice, setSelectedStripePrice] = useState(null)
  const [openWarningSetPriceAsDefault, setOpenWarningSetPriceAsDefault] = useState(false)
  const [stripeAccountInfo, setStripeAccountInfo] = useState(null)
  const [showStripeConnectRedirectConfirm, setShowStripeConnectRedirectConfirm] = useState(false)
  const [connectStripeRedirectionSeconds, setConnectStripeRedirectionSeconds] = useState(4)
  const setMembership = useStore((state) => state.community.setMembership)
  const intervalRef = useRef<ReturnType<typeof setTimeout> | null>(null)

  const isStripeConnected = stripeAccountInfo !== null
  const canStripeAcceptPaymentsAndCharges = stripeAccountInfo?.payouts_enabled && stripeAccountInfo?.charges_enabled

  useEffect(() => {
    if (!membership) {
      return
    }

    const init = async () => {
      setIsLoading(true)

      const res = await getStripeAccountApi(membership.id)
      const resStripeAccountInfo = res.data.data
      setStripeAccountInfo(resStripeAccountInfo)

      if (
        resStripeAccountInfo?.tos_acceptance.date &&
        resStripeAccountInfo?.payouts_enabled &&
        resStripeAccountInfo?.charges_enabled
      ) {
        const res = await getStripePricesApi(true, membership.id)
        setStripPrices(res.data.data)
      }
      setIsLoading(false)
    }

    init()
  }, [membership])

  if (isLoading) {
    return <SkeletonBox />
  }

  const handleConnectStripeClick = async () => {
    setIsRequestingConnectStripe(true)
    const res = await getStripeConnectLinkApi(membership.id)
    const setupAccountLinkUrl = res.data.data.account_link.url
    setShowStripeConnectRedirectConfirm(true)

    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    intervalRef.current = setInterval(() => {
      setConnectStripeRedirectionSeconds((seconds) => {
        if (seconds > 1) {
          return seconds - 1
        } else {
          clearInterval(intervalRef.current)
          setShowStripeConnectRedirectConfirm(false)
          setIsRequestingConnectStripe(false)
          window.open(setupAccountLinkUrl, '_self')
          return seconds
        }
      })
    }, 1000)
  }

  const handleStripeConnectConfirmClose = async () => {
    setShowStripeConnectRedirectConfirm(false)
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }

  const handleSetAsDefaultConfirm = (price) => {
    setSelectedStripePrice(price)
    setOpenWarningSetPriceAsDefault(true)
  }

  const handleDeletePriceConfirm = (price) => {
    // Additional safety check to prevent deletion of default prices
    if (price.product.default_price?.id === price.id) {
      toast.error('Cannot delete a price that is set as default')
      return
    }

    setOpenWarningDelete(true)
    setSelectedStripePrice(price)
  }

  const handleSetPriceAsDefault = async () => {
    try {
      setIsRequestingNewPrice(true)
      // Check if we're setting the community to free before making the API call
      const isMakingCommunityFree = selectedStripePrice === PRICE_ID_FREE_COMMUNITY

      const setAsDefaultRes = await setStripePriceAsDefaultApi(
        true,
        membership.id,
        isMakingCommunityFree ? PRICE_ID_FREE_COMMUNITY : selectedStripePrice.id,
      )

      // Use our pre-check to determine the toast message
      if (isMakingCommunityFree) {
        toast.success('Community set to free successfully', 'success')
      } else if (isFreeCommunity) {
        toast.success('Community set to paid successfully', 'success')
      } else {
        toast.success('Stripe price set as default successfully', 'success')
      }

      setMembership({ ...membership, membership_setting: setAsDefaultRes.data.data.membership_setting })

      const res = await getStripePricesApi(true, membership.id)
      setStripPrices(res.data.data)
    } catch (e) {
      toast.error(e.message)
    } finally {
      setSelectedStripePrice(null)
      setOpenWarningSetPriceAsDefault(false)
      setIsRequestingNewPrice(false)
    }
  }

  const handleDeletePrice = async () => {
    try {
      setIsRequestingNewPrice(true)

      // Double-check that we're not trying to delete a default price
      if (selectedStripePrice.product?.default_price?.id === selectedStripePrice.id) {
        setIsRequestingNewPrice(false)
        toast.error('Cannot delete a price that is set as default')
        return
      }

      await deleteStripePriceApi(true, membership.id, selectedStripePrice.id)
      toast.success('Price deleted successfully', 'success')
      const res = await getStripePricesApi(true, membership.id)
      setStripPrices(res.data.data)
    } catch (e) {
      toast.error(e.message)
    } finally {
      setSelectedStripePrice(null)
      setOpenWarningDelete(false)
      setIsRequestingNewPrice(false)
    }
  }

  const copyToClipboard = async (price) => {
    const url = `${window.location.origin}${getCommunityURL(membership, `/about?promo_code=${price.id}`)}`
    await navigator.clipboard.writeText(url)
    toast.success('URL Copied to clipboard')
  }

  const stripeConnectionStatusErrors = stripeAccountInfo?.requirements.errors.map((e) => e.reason).join(',')

  const connectStripeRedirectionMessage = `You are about to be redirected to Stripe to setup your account in ${connectStripeRedirectionSeconds} seconds...`

  return (
    <div className="flex flex-col space-y-4">
      <ConfirmModal
        title="Connect Stripe redirection"
        open={showStripeConnectRedirectConfirm}
        onClose={() => handleStripeConnectConfirmClose}
        showConfirmButton={false}
      >
        {connectStripeRedirectionMessage}
      </ConfirmModal>
      <ConfirmModal
        title="Are you sure you want to delete this price?"
        onConfirm={handleDeletePrice}
        open={openWarningDelete}
        loading={isRequestingNewPrice}
        onClose={() => setOpenWarningDelete(false)}
      />
      <ConfirmModal
        title={
          selectedStripePrice === PRICE_ID_FREE_COMMUNITY
            ? 'Are you sure you want to make this community free?'
            : isFreeCommunity
              ? 'Are you sure you want to make this community paid?'
              : 'Are you sure you want to set this price as default?'
        }
        onConfirm={handleSetPriceAsDefault}
        loading={isRequestingNewPrice}
        open={openWarningSetPriceAsDefault}
        onClose={() => setOpenWarningSetPriceAsDefault(false)}
      />
      <div className={'flex'}>
        <div className={'grow'}>
          <h2 className="text-lg font-semibold text-white-500">Pricing</h2>
          <div className="text-sm font-normal leading-snug text-[#8d94a3]">View and manage your community prices.</div>
        </div>
        <Button
          type="submit"
          variant="default"
          loading={isRequestingNewPrice}
          disabled={!isStripeConnected}
          onClick={onPriceAdd}
        >
          New Price
        </Button>
      </div>

      {/* Stripe Connect Status Block */}
      <div className={'flex flex-row items-center gap-3 rounded-[10px] bg-black-300 p-5'}>
        <div className="h-12 w-12 rounded-[10px] bg-gradient-to-r from-[#7d52ff] to-[#d452ff]"></div>
        <div className="text-white flex-grow text-sm font-semibold">
          <div>Free</div>
          <div className={'text-sm font-normal text-black-100'}>Free community</div>
        </div>
        {isFreeCommunity && <Badge variant="default">Default</Badge>}

        {!isFreeCommunity && (
          <div className={''}>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <MoreHorizIcon />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => handleSetAsDefaultConfirm(PRICE_ID_FREE_COMMUNITY)}
                >
                  Make community free
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* Stripe Connection Status Block */}
      {isStripeConnected && (
        <div
          className={cn(
            'flex flex-row items-center gap-3 rounded-[10px] p-5',
            canStripeAcceptPaymentsAndCharges ? 'bg-black-300' : 'bg-red-500/50',
          )}
        >
          <div className="h-12 w-12 rounded-[10px] bg-gradient-to-r from-[#7d52ff] to-[#d452ff]"></div>
          <div className="text-white flex-grow space-y-2 text-sm font-semibold">
            <div>
              <div>Stripe Connection Status</div>
              <div>
                <Badge className="text-nowrap">
                  {canStripeAcceptPaymentsAndCharges ? 'Connected / Payments enabled' : 'Limited'}
                </Badge>
              </div>
            </div>
            <div className={'flex flex-row items-center text-sm font-normal text-black-100'}>
              {!canStripeAcceptPaymentsAndCharges && (
                <div className="flex flex-grow">{stripeConnectionStatusErrors}</div>
              )}
            </div>

            {!canStripeAcceptPaymentsAndCharges && (
              <div className={'flex-grow'}>
                <Button
                  className=""
                  variant="default"
                  onClick={handleConnectStripeClick}
                  loading={isRequestingConnectStripe}
                  disabled={isRequestingConnectStripe}
                >
                  Fix Stripe Connection Issues
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {stripePrices.map((price) => (
        <div key={price.id} className={'flex flex-row items-center gap-3 rounded-[10px] bg-black-300 p-5'}>
          <div className="h-12 w-12 rounded-[10px] bg-gradient-to-r from-[#7d52ff] to-[#d452ff]"></div>
          <div className="text-white flex-grow text-sm font-semibold">
            <div>{price.metadata.title}</div>
            <div className={'text-sm font-normal text-black-100'}>{formatPrice(price)}</div>
          </div>
          {!isFreeCommunity && price.product.default_price?.id === price.id && <Badge variant="default">Default</Badge>}
          {(price.product.default_price?.id !== price.id || isFreeCommunity) && (
            <Badge variant="secondary">Private</Badge>
          )}
          <div className={'flex gap-1'}>
            <Button variant="outline" onClick={() => copyToClipboard(price)}>
              <LinkIcon />
            </Button>
            {/* Only show dropdown if there are options to show */}
            {(price.product.default_price?.id !== price.id ||
              (isFreeCommunity && price.product.default_price?.id === price.id)) && (
              <DropdownMenu>
                <DropdownMenuTrigger>
                  <Button variant="outline">
                    <MoreHorizIcon />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  {/* Show Make Default option if either:
                    1. This price is not currently the default, OR
                    2. The community is currently free (isFreeCommunity is true)
                  */}
                  {(price.product.default_price?.id !== price.id || isFreeCommunity) && (
                    <DropdownMenuItem className="cursor-pointer" onClick={() => handleSetAsDefaultConfirm(price)}>
                      Make Default
                    </DropdownMenuItem>
                  )}
                  {/* Only allow deletion for non-default prices */}
                  {price.product.default_price?.id !== price.id && (
                    <DropdownMenuItem
                      className="cursor-pointer text-red-100"
                      onClick={() => handleDeletePriceConfirm(price)}
                    >
                      Delete
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      ))}

      {/* Connect to Stripe Block */}
      {!isStripeConnected && (
        <div className={'flex flex-row gap-3 rounded-[10px] bg-black-300 p-5'}>
          <div className="h-12 w-12 rounded-[10px] bg-gradient-to-r from-[#7d52ff] to-[#d452ff]"></div>
          <div className="text-white flex-grow text-sm font-semibold">
            <div>Connect Stripe Account</div>
            <div className={'text-sm font-normal text-black-100'}>Start Accepting Payments</div>
          </div>
          <div>
            <Button
              type="submit"
              className="w-full sm:w-32"
              variant="default"
              onClick={handleConnectStripeClick}
              disabled={isRequestingConnectStripe}
              loading={isRequestingConnectStripe}
            >
              Connect
            </Button>
          </div>
        </div>
      )}

      {isStripeConnectedButOnPending && (
        <div className={'flex flex-row items-center gap-3 rounded-[10px] bg-black-300 p-5'}>
          <div className="h-12 w-12 rounded-[10px] bg-gradient-to-r from-[#7d52ff] to-[#d452ff]"></div>
          <div className="text-white flex-grow text-sm font-semibold">
            <div>Stripe Connection Status</div>
            <div className={'text-sm font-normal text-black-100'}>Check back in a few hours</div>
          </div>
          <div className="inline-flex h-[21px] items-center justify-center gap-2.5 rounded-[10px] bg-[#ffb153]/10 px-2.5 py-1.5">
            <div className="text-xs font-normal leading-none text-[#ffb153]">Pending</div>
          </div>
          <div className={''}>
            <Button className="w-full sm:w-32" variant="secondary" onClick={handleConnectStripeClick}>
              Disconnect
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
