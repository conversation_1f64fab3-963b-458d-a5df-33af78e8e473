import React, { useState } from 'react'

import AddLink from '@/components/settings/admin-settings/links/AddLink'
import ListLinks from '@/components/settings/admin-settings/links/ListLinks'
import { useStore } from '@/hooks/useStore'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { selectMembership } from '@/src/store/features/membershipSlice'

export function LinksSettings() {
  const dispatch = useAppDispatch()
  const membership = useStore((state) => state.community.membership)
  const [selectedLink, setSelectedLink] = useState(null)

  const [view, setView] = useState('list')

  const handleAddLink = () => {
    setView('update')
  }

  const handleAddLinkCancel = () => {
    setSelectedLink(null)
    setView('list')
  }

  const handleAddLinkSuccess = () => {
    setSelectedLink(null)
    setView('list')
  }

  const handleEditLink = (link) => {
    setSelectedLink(link)
    setView('update')
  }

  return (
    <>
      {view === 'list' && <ListLinks onAddLink={handleAddLink} onEditLink={handleEditLink} />}
      {view === 'update' && (
        <AddLink onCancel={handleAddLinkCancel} link={selectedLink} onSuccess={handleAddLinkSuccess} />
      )}
    </>
  )
}
