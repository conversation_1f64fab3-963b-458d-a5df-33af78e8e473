import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import React, { useState } from 'react'

import { Drag24Icon } from '@/components/icons/24px/Drag24Icon'
import { Badge, Button } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { updateMembershipApi } from '@/shared-services/apis/membership.api'
import { updateMembershipSettingSuccess } from '@/src/store/features/membershipSlice'
import { useAppDispatch } from '@/src/store/hooks'

export default function ListLinks({ onAddLink, onEditLink }) {
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = membership?.membership_setting
  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false)
  const [selectedLink, setSelectedLink] = useState(null)
  const setMembership = useStore((state) => state.community.setMembership)
  const dispatch = useAppDispatch()
  const links = membershipSetting.external_links as { label: string; url: string; id: string; is_private: boolean }[]
  const [requestingDeleteLink, setRequestingDeleteLink] = useState(false)

  const handleAddLink = () => {
    onAddLink?.()
  }

  const handleEditLink = (link) => {
    onEditLink?.(link)
  }

  const handleConfirmDelete = (item) => {
    setSelectedLink(item)
    setOpenDeleteConfirm(true)
  }

  const canAddLink = membershipSetting?.external_links.length < 3

  const handleDelete = async () => {
    setRequestingDeleteLink(true)
    try {
      let updatedExternalLinks = links.filter((l) => l.label !== selectedLink.label)
      const data = { external_links: updatedExternalLinks }
      const result = await updateMembershipApi(data, membership.id)

      if (result.data.success) {
        toast.success('Community links updated')
      }

      setMembership({
        ...membership,
        membership_setting: result.data.data.membership_setting,
      })

      dispatch(
        updateMembershipSettingSuccess({
          data: result.data.data.membership_setting,
          partialChanged: true,
        }),
      )
    } catch (e) {
      toast.error(e.message)
    } finally {
      setRequestingDeleteLink(false)
      setSelectedLink(null)
      setOpenDeleteConfirm(false)
    }
  }

  return (
    <div>
      <ConfirmModal
        title="Are you sure you want to delete the link?"
        onConfirm={handleDelete}
        open={openDeleteConfirm}
        loading={requestingDeleteLink}
        onClose={() => setOpenDeleteConfirm(false)}
      />
      <div className="flex flex-col space-y-4">
        <div className="flex flex-row">
          <div className="grow">
            <h2 className="text-lg font-semibold text-white-500">Links</h2>
            <div className="text-sm font-normal leading-snug text-black-100">
              Share resources with your community using links.
            </div>
          </div>
          <Button
            type="submit"
            variant="default"
            disabled={!canAddLink}
            onClick={handleAddLink}
            data-cy="add-space-button"
          >
            Add Link
          </Button>
        </div>
        {links.map((item) => (
          <div key={item.id} className="flex items-center gap-4 rounded-[10px] bg-black-300 p-4">
            <Drag24Icon />
            <div className="flex grow flex-col">
              <div className="text-white grow text-sm font-semibold leading-snug">{item.label}</div>
              <div className="text-sm font-normal leading-snug text-black-100">{item.url}</div>
            </div>
            <Badge>{item.is_private ? 'Private' : 'Public'}</Badge>
            <div className="flex items-center gap-4">
              <DropdownMenu>
                <DropdownMenuTrigger>
                  <Button type="button" variant="outline" size="xs" data-cy="more-button">
                    <MoreHorizIcon />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuItem className="cursor-pointer" onClick={() => handleEditLink(item)}>
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem className="cursor-pointer text-red-500" onClick={() => handleConfirmDelete(item)}>
                    Delete Link
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
