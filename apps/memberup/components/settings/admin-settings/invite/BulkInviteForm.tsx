import axios from 'axios'
import React, { useEffect, useState } from 'react'

import { AppDropzone } from '@memberup/shared/src/components/common/app-dropzone'
import { showToast } from '@memberup/shared/src/libs/toast'
import { Button } from '@/components/ui'
import { validateEmail } from '@/shared-libs/string-utils'
import { inviteApi } from '@/shared-services/apis/invite.api'
import { selectMembership } from '@/src/store/features/membershipSlice'
import { useAppSelector } from '@/src/store/hooks'

const downloadFile = async () => {
  try {
    const response = await axios.get('/assets/default/bulk-invite.csv', {
      responseType: 'blob',
    })
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', 'bulk-invite.csv')
    document.body.appendChild(link)
    link.click()
    link.parentNode.removeChild(link)
  } catch (error) {
    console.error('Error downloading file:', error)
  }
}

export default function BulkInviteForm({ onSuccess }) {
  const [file, setFile] = useState<File>(null)
  const [error, setError] = useState<string>('')
  const [requestingSendInvites, setRequestingSendInvites] = useState(false)
  const [inviteMembers, setInviteMembers] = useState()
  const membership = useAppSelector((state) => selectMembership(state))

  useEffect(() => {
    if (!file) return
    const reader = new FileReader()
    reader.onload = (e) => {
      const temp = e.target.result as string
      const headers = temp.slice(0, temp.indexOf('\n')).split(',')
      const emailHeaderIndex = headers.findIndex((h) => h.toLowerCase().trim() === 'email')
      if (emailHeaderIndex < 0) {
        setError(`You must have the first row contain the header 'email'. Please try again.`)
        setFile(null)
        return
      }

      const rows = temp
        .slice(temp.indexOf('\n') + 1)
        .split('\n')
        .filter((row) => row.trim() !== '')
      if (rows.length > 1000) {
        setError(`You can send a max number of 1000 invites at a time. Please try again.`)
        setFile(null)
        return
      }
      if (rows.length === 0) {
        setError(`There is no record in the input file. Please try again.`)
        setFile(null)
        return
      }

      const result = []
      const invalidRows = []
      if (rows.length > 0) {
        for (let i = 0; i < rows.length; i++) {
          const temp = rows[i].split(',')
          const email = temp[emailHeaderIndex]?.trim() || ''
          if (validateEmail(email)) {
            if (!result.find((r) => r.email === email)) {
              result.push({
                email,
              })
            }
          } else {
            invalidRows.push(i + 1)
          }
        }
      }

      if (invalidRows.length) {
        setError(
          `${invalidRows.length} Records have invalid or missing data at row(s) ${invalidRows.join(
            ', ',
          )}. Please check the csv file and try again.`,
        )
        setFile(null)
        return
      }
      setInviteMembers(result)
    }
    reader.onerror = (e) => {
      console.error('File reading error', reader.error)
      reader.abort()
    }
    reader.readAsText(file)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [file])

  const handleSendInviteClick = async () => {
    setRequestingSendInvites(true)
    try {
      const res = await inviteApi({ members: inviteMembers, message: 'Some message' }, membership.id)
      showToast('Invitations sent', 'success')
      onSuccess()
    } catch (err) {
      const errorMessage = err?.response?.data?.message || err?.message
      if (errorMessage) {
        showToast(errorMessage, 'error')
      }
    } finally {
      setRequestingSendInvites(false)
    }
  }

  return (
    <div className={'space-y-6'}>
      <div className="text-base font-semibold leading-snug text-[#f1f2f5]">Step 1: upload your .csv file</div>
      <div className="text-sm font-normal leading-snug text-[#e0e1e1]">
        Upload a .csv file of the email addresses you want to invite. You can{' '}
        <span className="cursor-pointer text-sm font-normal leading-snug text-[#7d52ff]" onClick={downloadFile}>
          use this template.
        </span>
      </div>

      <AppDropzone
        file={file}
        accept={{ 'text/csv': ['.csv'], 'application/vnd.ms-excel': ['.csv'] }}
        onDropFile={(f) => {
          setError('')
          setFile(f)
        }}
        placeholderHeight={100}
        placeholder={
          <Button
            type="submit"
            variant="outline"
            loading={requestingSendInvites}
            disabled={requestingSendInvites}
            data-cy="update-file-button"
          >
            Upload file
          </Button>
        }
      />

      <div className="text-base font-semibold leading-snug text-[#f1f2f5]">Step 2: send your invites</div>

      <div>
        <span className="text-sm font-normal leading-snug text-[#e0e1e1]">
          Click “send invites”, and we'll email each member a unique link to instantly join your community, bypassing
          the membership approval process. The email looks
        </span>
        <span className="text-sm font-normal leading-snug text-[#7d52ff]"> like this.</span>
      </div>
      <Button
        type="submit"
        variant="default"
        loading={requestingSendInvites}
        disabled={requestingSendInvites || !inviteMembers}
        onClick={handleSendInviteClick}
        data-cy="send-invites-button"
      >
        Send Invites
      </Button>
      <p>{error}</p>
    </div>
  )
}
