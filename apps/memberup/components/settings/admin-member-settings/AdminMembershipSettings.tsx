import { useState } from 'react'

import { VisibilityOn20Icon } from '@/components/icons'
import { Button, Input } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { formSubmitError } from '@/lib/error-messages'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { banMemberApi, removeMemberApi } from '@/shared-services/apis/membership.api'
import { IUser } from '@/shared-types/interfaces'

interface AdminMembershipSettingsProps {
  member: IUser
}

export function AdminMembershipSettings({ member }: AdminMembershipSettingsProps) {
  const membership = useAppSelector((state) => selectMembership(state))
  const [openConfirmRemoveMember, setOpenConfirmRemoveMember] = useState(false)
  const [openConfirmBanMember, setOpenConfirmBanMember] = useState(false)
  const [isRequesting, setIsRequesting] = useState(false)

  const handleOnRemoveMember = async () => {
    if (!member) return
    setIsRequesting(true)
    try {
      const res = await removeMemberApi(member.id, membership.id)
      if (res.data.success) {
        toast.success('Member removed successfully')
      }
    } catch (err) {
      toast.error(formSubmitError)
    } finally {
      setIsRequesting(false)
      setOpenConfirmRemoveMember(false)
    }
  }

  const handleOnBanMember = async () => {
    if (!member) return
    setIsRequesting(true)
    try {
      const res = await banMemberApi(member.id, membership.id)
      if (res.data.success) {
        toast.success('Member banned successfully')
      }
    } catch (err) {
      toast.error(formSubmitError)
    } finally {
      setIsRequesting(false)
      setOpenConfirmBanMember(false)
    }
  }

  const handleOnCopyClick = async () => {
    if (!member?.email) return
    await navigator.clipboard.writeText(member.email)
    toast.success('Copied to clipboard')
  }

  if (!member) {
    return null
  }

  return (
    <div>
      <ConfirmModal
        title="Are you sure you want to remove the member?"
        onConfirm={handleOnRemoveMember}
        loading={isRequesting}
        open={openConfirmRemoveMember}
        onClose={() => setOpenConfirmRemoveMember(false)}
      />
      <ConfirmModal
        title="Are you sure you want to ban the member?"
        onConfirm={handleOnBanMember}
        loading={isRequesting}
        open={openConfirmBanMember}
        onClose={() => setOpenConfirmBanMember(false)}
      />
      <h2 className="mb-3 text-lg font-semibold text-black-700 dark:text-white-500">Membership</h2>
      <div className="space-y-4">
        <Input
          disabled={true}
          append={
            <Button variant="inline" className="flex flex-col justify-center px-4" onClick={handleOnCopyClick}>
              <div className="relative h-5 w-5">
                <VisibilityOn20Icon className="absolute relative left-0 top-0 z-10 text-grey-500" />
              </div>
            </Button>
          }
          type="text"
          value={member?.email || ''}
        />
        <Input disabled={true} type="text" value={member?.role || ''} />
        <div className="flex justify-end gap-5">
          <Button variant="outline" onClick={() => setOpenConfirmRemoveMember(true)}>
            Remove member
          </Button>
          <Button variant="outline" onClick={() => setOpenConfirmBanMember(true)}>
            Ban member
          </Button>
        </div>
      </div>
    </div>
  )
}
