import { useEffect, useState } from 'react'

import { Progress, SkeletonBox } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import { getMemberCoursesProgressApi } from '@/shared-services/apis/user.api'
import { IUser } from '@/shared-types/interfaces'

interface CoursesSettingsProps {
  member: IUser
}

export function CoursesSettings({ member }: CoursesSettingsProps) {
  const membership = useStore((state) => state.community.membership)
  const [courses, setCourses] = useState([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!membership || !member) {
      return
    }

    const loadCourses = async () => {
      setLoading(true)
      const res = await getMemberCoursesProgressApi(member.username, membership.id)
      setCourses(res.data.data.courses)
      setLoading(false)
    }
    loadCourses()
  }, [membership, member])

  return (
    <div>
      <h2 className="mb-0.5 text-lg font-semibold text-white-500">Courses</h2>
      <p className="mb-4 text-sm font-normal text-black-600 dark:text-white-200"></p>
      {loading && <SkeletonBox className="w-full" />}
      {courses.length === 8 && <div>No courses</div>}
      {courses.map((course) => (
        <div key={course.id} className={'mb-2 flex flex-col rounded-[10px] bg-black-300 p-5'}>
          <div className={'text-[14px] font-semibold text-white-500'}>{course.title}</div>
          <div className="flex flex-row items-center gap-4">
            <Progress value={Math.round(course.ContentLibraryCourseUserProgress[0]?.progress_percentage || 0)} />
            <span className="text-black-100">
              {Math.round(course.ContentLibraryCourseUserProgress[0]?.progress_percentage || 0)}%
            </span>
          </div>
        </div>
      ))}
    </div>
  )
}
