import { useEffect, useState } from 'react'

import { useStore } from '@/hooks/useStore'
import { getMemberRequestResponsesApi } from '@/shared-services/apis/user.api'
import { IUser } from '@/shared-types/interfaces'

interface QuestionsSettingsProps {
  member: IUser
}

export function QuestionsSettings({ member }: QuestionsSettingsProps) {
  const membership = useStore((state) => state.community.membership)
  const [questions, setQuestions] = useState([])
  const [loading, setLoading] = useState<boolean>(false)

  useEffect(() => {
    if (!membership || !member) {
      return
    }

    const initialize = async () => {
      setLoading(true)
      const res = await getMemberRequestResponsesApi(member.username, membership.id)
      setQuestions(res.data.data.user_membership.user_answer.fields)
      setLoading(false)
    }
    initialize()
  }, [membership, member])

  return (
    <div>
      <h2 className="mb-0.5 text-lg font-semibold text-white-500">Responses</h2>
      <p className="mb-4 text-sm font-normal text-black-600 dark:text-white-200"></p>
      <div className="flex-rows flex">
        {questions.map((f) => (
          <div className="flex flex-col rounded-[13px] bg-black-400 p-4">
            <div className="text-white text-[13px] font-bold leading-[30px]">{f.label}</div>
            <div className="text-[13px] font-normal leading-[30px] text-[#8d94a3]">{f.value}</div>
          </div>
        ))}
      </div>
    </div>
  )
}
