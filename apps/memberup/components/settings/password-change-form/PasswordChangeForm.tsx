'use client'

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { usePathname, useRouter } from 'next/navigation'
import { useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { changePasswordApi } from '@memberup/shared/src/services/apis/reset-password.api'
import { Button, Input, PasswordInput } from '@/components/ui'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { formSubmitError, invalidPasswordError } from '@/lib/error-messages'
import { passwordSchema, passwordsMatch, passwordsMatchMessage } from '@/lib/validation/zod'
import useAppCookie from '@/memberup/components/hooks/use-app-cookie'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { useMounted } from '@/shared-components/hooks/use-mounted'

export function PasswordChangeForm() {
  const pathname = usePathname()
  const router = useRouter()
  const user = useAppSelector((state) => selectUser(state))
  const { updateAppCookie } = useAppCookie()
  const [error, setError] = useState(null)
  const [submitting, setSubmitting] = useState(false)
  const mounted = useMounted()
  const autosaveButtonRef = useRef(null)

  const changePasswordSchema = z
    .object({
      currentPassword: passwordSchema,
      password: passwordSchema,
      confirmPassword: passwordSchema,
    })
    .refine(passwordsMatch, passwordsMatchMessage)

  type ChangePasswordSchemaType = z.infer<typeof changePasswordSchema>

  const passwordChangeForm = useForm<ChangePasswordSchemaType>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      currentPassword: '',
      password: '',
      confirmPassword: '',
    },
    resolver: zodResolver(changePasswordSchema),
  })

  const onPasswordChangeSubmit = async (formData: ChangePasswordSchemaType) => {
    setSubmitting(true)
    setError(null)

    try {
      const res = await changePasswordApi(formData.currentPassword, formData.password)

      if (res.data.success && mounted.current) {
        updateAppCookie(res.data.token)
        autosaveButtonRef.current.click()
      }
    } catch (error) {
      if (!mounted.current) return

      if (error.response?.status === 400) {
        passwordChangeForm.setError('currentPassword', {
          type: 'custom',
          message: invalidPasswordError,
        })
      } else {
        setError(formSubmitError)
      }
      setSubmitting(false)
    }
  }

  const redirectToAlternativePage = () => {
    router.push(
      pathname === '/settings/account/password' ? '/settings/account/password/a' : '/settings/account/password',
    )
  }

  return (
    <div className="password-settings tailwind-component page-inner-pb">
      <div className="rounded-box w-full max-w-screen-md p-5">
        <h2 className="-mt-1 mb-5 text-lg font-semibold">Change Password</h2>
        <Form {...passwordChangeForm}>
          <div className="space-y-5">
            <FormField
              control={passwordChangeForm.control}
              name="currentPassword"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <PasswordInput
                      autoComplete="current-password"
                      className="w-full"
                      disabled={submitting}
                      placeholder="Old password"
                      error={Boolean(error)}
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
            <FormField
              control={passwordChangeForm.control}
              name="password"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <PasswordInput
                      autoComplete="new-password"
                      className="w-full"
                      disabled={submitting}
                      placeholder="New password"
                      error={Boolean(error)}
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
            <FormField
              control={passwordChangeForm.control}
              name="confirmPassword"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <PasswordInput
                      autoComplete="new-password"
                      className="w-full"
                      disabled={submitting}
                      placeholder="Confirm new password"
                      error={Boolean(error)}
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
            {error && <FormMessage>{error}</FormMessage>}
            <Button
              className="w-full"
              variant="default"
              loading={submitting}
              disabled={submitting}
              onClick={passwordChangeForm.handleSubmit(onPasswordChangeSubmit)}
            >
              Save
            </Button>
            <form
              action="/settings/account/password"
              onSubmit={(event) => {
                event.preventDefault()
                toast.success('Password changed successfully')
                redirectToAlternativePage()
              }}
            >
              <Input
                className="hidden"
                autoComplete="email"
                name="username"
                type="email"
                placeholder="Email"
                readOnly
                value={user.email}
              />
              <Input
                className="hidden"
                name="password"
                type="password"
                placeholder="Password"
                readOnly
                autoComplete="new-password"
                value={passwordChangeForm.getValues().password}
              />
              <Button ref={autosaveButtonRef} type="submit" className="hidden">
                Submit
              </Button>
            </form>
          </div>
        </Form>
      </div>
    </div>
  )
}
