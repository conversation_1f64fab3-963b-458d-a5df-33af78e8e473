import Fade from 'embla-carousel-fade'
import { useEffect, useRef, useState } from 'react'

import { StaticGalleryItem } from './static-gallery-item'
import { StaticGalleryThumbnail } from './static-gallery-thumbnail'
import { Carousel, CarouselContent, CarouselItem, type CarouselApi } from '@/components/ui/carousel'

export function StaticGallery({ getAssetData, media }: { getAssetData: (item: any) => any; media: any[] }) {
  const [api, setApi] = useState<CarouselApi>()
  const [currentIndex, setCurrentIndex] = useState(0)
  const deletedItemRef = useRef(false)

  useEffect(() => {
    if (!api) {
      return
    }

    api.on('select', () => {
      if (!deletedItemRef.current) {
        setCurrentIndex(api.selectedScrollSnap())
      } else {
        deletedItemRef.current = false
      }
    })
  }, [api])

  return (
    <div className="editable-about-media-gallery">
      <Carousel
        className="h-full w-full"
        opts={{
          active: media.length > 1,
        }}
        plugins={[Fade()]}
        setApi={setApi}
      >
        <CarouselContent>
          {media.map((item, index) => (
            <CarouselItem key={index}>
              <StaticGalleryItem item={item} muxAssetData={getAssetData(item)} />
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
      {media.length > 1 && (
        <div className="mt-4 flex space-x-2 md:space-x-[0.625rem] lg:mt-5 lg:space-x-4">
          {media.map((item, index) => (
            <StaticGalleryThumbnail
              active={currentIndex === index}
              media={item}
              playbackId={getAssetData(item)?.playback_ids?.[0]?.id}
              key={item.id}
              onClick={() => {
                api.scrollTo(index)
                setCurrentIndex(index)
              }}
            />
          ))}
        </div>
      )}
    </div>
  )
}
