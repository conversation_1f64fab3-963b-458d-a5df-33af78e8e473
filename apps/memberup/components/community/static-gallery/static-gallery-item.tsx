import MuxPlayer from '@mux/mux-player-react/lazy'

import { HourglassProgress24Icon } from '@/components/icons/24px/hourglass-progress-24-icon'
import { CroppedImage } from '@/components/images/cropped-image'
import { AspectRatio, SkeletonBox } from '@/components/ui'
import { cn } from '@/lib/utils'

export function StaticGalleryItem({ item, muxAssetData }) {
  return (
    <AspectRatio className="overflow-hidden rounded-base bg-white-100 dark:bg-black-300" ratio={16 / 9}>
      {item?.mux_upload_id ? (
        muxAssetData?.playback_ids?.[0]?.id ? (
          <MuxPlayer
            className="h-full w-full cursor-pointer"
            metadata={{
              video_id: muxAssetData.id,
              video_title: muxAssetData.title,
              viewer_user_id: muxAssetData.viewer_user_id,
            }}
            playbackId={muxAssetData.playback_ids[0].id}
            streamType="on-demand"
          />
        ) : (
          <div className="relative h-full w-full cursor-grab">
            {item?.url ? (
              <CroppedImage
                className="absolute left-0 top-0 z-0 rounded-base"
                src={item.url}
                cropArea={item.cropArea}
                width={800}
                height={450}
                alt=""
                priority
              />
            ) : (
              <SkeletonBox className="absolute left-0 top-0 z-0 h-full w-full" />
            )}
            <div
              className={cn(
                'relative z-10 flex h-full w-full flex-col items-center justify-center',
                item?.url && 'bg-black-500/70',
              )}
            >
              <div
                className={cn(
                  'flex flex-col items-center',
                  item?.url ? 'text-white-500' : 'text-black-500 dark:text-white-500',
                )}
              >
                <HourglassProgress24Icon className="h-24 w-24" />
                <div className="mt-10 select-none font-medium">Please wait while the video is being processed</div>
              </div>
            </div>
          </div>
        )
      ) : (
        <CroppedImage
          className="rounded-base"
          src={item.url}
          cropArea={item.cropArea}
          width={800}
          height={450}
          alt=""
          priority
        />
      )}
    </AspectRatio>
  )
}
