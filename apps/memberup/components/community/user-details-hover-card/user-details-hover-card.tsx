import Link from 'next/link'
import { useRef, useState } from 'react'

import { getDateTimeFromNow } from '@memberup/shared/src/libs/date-utils'
import { Clock16Icon } from '@/components/icons'
import { Location16Icon } from '@/components/icons/16px/location-16-icon'
import { ProfilePicture } from '@/components/images/profile-picture'
import { buttonVariants } from '@/components/ui/button'
import { HoverCard, HoverCardContent, HoverCardPortal, HoverCardTrigger } from '@/components/ui/hover-card'
import { Skeleton } from '@/components/ui/skeleton'
import { useStore } from '@/hooks/useStore'
import { getFullName } from '@/lib/formatting'
import { cn } from '@/lib/utils'

export function UserDetailsHoverCard({ children, username }: { children: React.ReactNode; username: string }) {
  const user = useStore((state) => state.auth.user)
  const [userData, setUserData] = useState(null)
  const fetchingDataRef = useRef(false)

  const fetchUserData = async () => {
    if (fetchingDataRef.current || userData) return
    fetchingDataRef.current = true

    const response = await fetch(`/api/user/user-profile/${username}?summarized=true`)

    fetchingDataRef.current = false

    if (response.status === 200) {
      const data = await response.json()
      setUserData(data.data)
    }
  }

  const onOpenChange = (open: boolean) => {
    if (open) {
      fetchUserData()
    }
  }

  const isUserCard = user?.username === username

  return (
    <HoverCard onOpenChange={onOpenChange}>
      <HoverCardTrigger asChild onMouseEnter={fetchUserData}>
        {children}
      </HoverCardTrigger>
      <HoverCardPortal>
        <HoverCardContent align="start" className="z-[1000] w-[24.625rem] p-5" onClick={(e) => e.stopPropagation()}>
          <div>
            <div className="flex space-x-4">
              <div>
                {userData ? (
                  <ProfilePicture
                    className="h-[6.875rem] w-[6.875rem] shrink-0 rounded-full"
                    src={userData.profile.image}
                    cropArea={userData.profile.image_crop_area}
                    alt={userData.profile.full_name}
                    height={110}
                    width={110}
                  />
                ) : (
                  <Skeleton className="h-[6.875rem] w-[6.875rem] shrink-0 rounded-full" />
                )}
              </div>
              <div className="grow">
                {userData ? (
                  <>
                    <h3 className="mb-1 text-xl font-semibold text-black-700 dark:text-white-500">
                      {getFullName(userData)}
                    </h3>
                    <div className="flex flex-col space-y-1 text-ssm font-normal text-black-200 dark:text-black-100">
                      <div className="flex items-center">
                        <Clock16Icon className="mr-2" /> Last active{' '}
                        {getDateTimeFromNow(userData.profile.last_activity_at)}
                      </div>
                      {userData.profile.location && (
                        <div className="flex items-center">
                          <Location16Icon className="mr-2" /> {userData.profile.location}
                        </div>
                      )}
                    </div>
                    {userData.profile.bio && (
                      <p className="mt-3 text-sm font-normal text-black-600 dark:text-white-200">
                        {userData.profile.bio}
                      </p>
                    )}
                  </>
                ) : (
                  <div className="pt-1">
                    <Skeleton className="mb-3 h-6 w-full" />
                    <Skeleton className="mb-2 h-4 w-full" />
                    <Skeleton className="mb-2 h-4 w-full" />
                  </div>
                )}
              </div>
            </div>
            <div className="mt-4 flex w-full gap-2">
              {userData ? (
                <>
                  <Link
                    href={`/@${userData.username}`}
                    className={cn(buttonVariants({ variant: 'outline' }), isUserCard ? 'w-full' : 'w-1/2')}
                  >
                    Profile
                  </Link>
                  {!isUserCard && (
                    <Link
                      className={cn(buttonVariants({ variant: 'default' }), 'w-1/2')}
                      href={`/chat?member=${userData.id}`}
                    >
                      Message
                    </Link>
                  )}
                </>
              ) : (
                <>
                  <Skeleton className={cn('h-12', isUserCard ? 'w-full' : 'w-1/2')} />
                  {!isUserCard && <Skeleton className="h-12 w-1/2" />}
                </>
              )}
            </div>
          </div>
        </HoverCardContent>
      </HoverCardPortal>
    </HoverCard>
  )
}
