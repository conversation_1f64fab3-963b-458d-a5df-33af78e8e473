import GroupAddIcon from '@mui/icons-material/GroupAdd'
import Link from 'next/link'
import { useEffect, useRef, useState } from 'react'

import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { useStore } from '@/hooks/useStore'
import { getMemberRequestsApi } from '@/shared-services/apis/membership.api'
import { getCommunityBaseURL } from '@/src/libs/utils'

export function MemberRequestsLink() {
  const [memberRequests, setMemberRequests] = useState([])
  const userMembership = useStore((state) => state.community.userMembership)
  const membership = useStore((state) => state.community.membership)
  const fetchedRef = useRef(false)

  useEffect(() => {
    if (!fetchedRef.current && userMembership?.user_role === USER_ROLE_ENUM.owner) {
      getMemberRequestsApi(membership.id).then((res) => {
        setMemberRequests(res.data.data)
      })
      fetchedRef.current = true
    }
  }, [userMembership])

  if (!memberRequests || memberRequests.length === 0) return null

  return (
    <div className="mt-4 flex rounded-[10px] bg-black-500 p-4">
      <div>
        <GroupAddIcon />
      </div>
      <div className="flex-grow text-center text-sm text-red-500">
        <Link href={`${getCommunityBaseURL(membership)}/members?tab=requests`}>Member Requests</Link>
      </div>
      <div>{memberRequests.length}</div>
    </div>
  )
}
