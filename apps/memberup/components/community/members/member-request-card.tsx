import MemberRequestCardHeader from '@/components/community/members/member-request-card-header'

export default function MemberRequestCard({ memberRequest, onApproveClick, onDeclineClick }) {
  return (
    <div className="rounded-base bg-black-500 p-5">
      <MemberRequestCardHeader
        memberRequest={memberRequest}
        onApproveClick={onApproveClick}
        onDeclineClick={onDeclineClick}
      />
      <div className="text-white-500">{memberRequest.user.profile.bio}</div>
      <div className="flex-rows flex">
        {memberRequest.user_answer &&
          memberRequest.user_answer.fields.map((f) => (
            <div>
              <span className="text-[13px] font-bold leading-[30px] text-[#8d94a3]">{f.label}:&nbsp;</span>
              <span className="text-[13px] font-normal leading-[30px] text-[#8d94a3]">
                {f.value}
                <br />
              </span>
            </div>
          ))}
      </div>
    </div>
  )
}
