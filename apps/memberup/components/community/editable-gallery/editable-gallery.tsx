import { DndContext, Drag<PERSON>ndEvent, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { restrictToParentElement } from '@dnd-kit/modifiers'
import { arrayMove, horizontalListSortingStrategy, SortableContext } from '@dnd-kit/sortable'
import Fade from 'embla-carousel-fade'
import { useEffect, useId, useRef, useState } from 'react'

import { EditableGalleryItem } from './editable-gallery-item'
import { EditableGalleryThumbnail } from './editable-gallery-thumbnail'
import { Carousel, CarouselContent, CarouselItem, type CarouselApi } from '@/components/ui/carousel'
import { IFileUploadsState } from '@/hooks/useMultipleFileUploads'
import { getVideoScreenshot } from '@/lib/client/get-video-screenshot'
import { CropArea } from '@/shared-types/types'

export type EditableGalleryMediaItem = {
  id: string
  file: File
  url?: string
  cropArea?: CropArea
}

export function EditableGallery({
  getAssetData,
  fileUploads,
  handleDelete,
  media,
  saving,
  setMedia,
}: {
  getAssetData: (item: any) => any
  fileUploads: IFileUploadsState
  handleDelete: (id: string) => void
  media: any[]
  saving: boolean
  setMedia: any
}) {
  const [api, setApi] = useState<CarouselApi>()
  const [currentIndex, setCurrentIndex] = useState(0)
  const currentId = media[currentIndex]?.id
  const dndContextId = useId() // Required to prevent hydration error
  const deletedItemRef = useRef(false)

  useEffect(() => {
    if (!api) {
      return
    }

    api.on('select', () => {
      if (!deletedItemRef.current) {
        setCurrentIndex(api.selectedScrollSnap())
      } else {
        deletedItemRef.current = false
      }
    })
  }, [api])

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 4,
      },
    }),
  )

  function handleDragEnd(event?: DragEndEvent) {
    const { active: eventActive, over } = event

    if (over && eventActive.id !== over.id) {
      setMedia((media: any[]) => {
        const activeIndex = media.findIndex(({ id }) => id === eventActive.id)
        const overIndex = media.findIndex(({ id }) => id === over.id)

        const updatedMedia = arrayMove(media, activeIndex, overIndex)

        const newCurrent = updatedMedia.findIndex((item) => item.id === currentId)
        api.scrollTo(newCurrent, true)
        setCurrentIndex(newCurrent)

        return updatedMedia
      })
    }
  }

  const addMedia = async (items: EditableGalleryMediaItem[]) => {
    const newMedia: any[] = []

    for (const item of items) {
      const data: any = {
        id: item.id,
        file: item.file,
        draft: true,
      }

      if (item.cropArea && item.url) {
        Object.assign(data, {
          cropArea: item.cropArea,
          url: item.url,
        })
      } else {
        // Add temporary video screenshot
        const screenshotDataUrl = await getVideoScreenshot(item.file)
        data.url = screenshotDataUrl
      }

      newMedia.push(data)
    }

    setMedia((currentMedia: any) => {
      const updatedMedia = [
        ...currentMedia.filter((item: EditableGalleryMediaItem) => item.id !== 'uploader'),
        ...newMedia,
      ]

      if (updatedMedia.length < 7) {
        updatedMedia.push({ id: 'uploader' })
      }

      return updatedMedia
    })
  }

  return (
    <div className="editable-about-media-gallery">
      <Carousel
        className="h-full w-full"
        opts={{
          active: media.length > 1,
        }}
        plugins={[Fade()]}
        setApi={setApi}
      >
        <CarouselContent>
          {media.map((item, index) => (
            <CarouselItem key={index}>
              <EditableGalleryItem
                addMedia={addMedia}
                item={item}
                media={media}
                muxAssetData={getAssetData(item)}
                saving={saving}
                isActive={currentIndex === index}
              />
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
      {media.length > 1 && (
        <DndContext id={dndContextId} onDragEnd={handleDragEnd} modifiers={[restrictToParentElement]} sensors={sensors}>
          <SortableContext items={media} strategy={horizontalListSortingStrategy}>
            <div className="mt-4 flex space-x-2 md:space-x-[0.625rem] lg:mt-5 lg:space-x-4">
              {media.map((item, index) => (
                <EditableGalleryThumbnail
                  active={currentIndex === index}
                  handleDelete={handleDelete}
                  media={item}
                  playbackId={getAssetData(item)?.playback_ids?.[0]?.id}
                  saving={saving}
                  uploadState={fileUploads[item.id]}
                  key={item.id}
                  onClick={() => {
                    api.scrollTo(index)
                    setCurrentIndex(index)
                  }}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>
      )}
    </div>
  )
}
