import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { AspectRatio } from '@radix-ui/react-aspect-ratio'
import Image from 'next/image'

import { Plus16Icon } from '@/components/icons'
import { Close24Icon } from '@/components/icons/24px/Close24Icon'
import { HourglassProgress24Icon } from '@/components/icons/24px/hourglass-progress-24-icon'
import { CroppedImage } from '@/components/images/cropped-image'
import { Button, Progress } from '@/components/ui'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'

export function EditableGalleryThumbnail({
  active,
  className,
  handleDelete,
  media,
  playbackId,
  onClick,
  uploadState,
  saving,
}: {
  active?: boolean
  className?: string
  handleDelete: (id: string) => void
  media?: any
  playbackId?: string
  onClick?: () => void
  uploadState?: any
  saving: boolean
}) {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: media.id,
    disabled: saving,
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <div
      style={style}
      ref={setNodeRef}
      className={cn(
        'editable-gallery-thumbnail group relative w-[calc((100%-3rem)/7)] md:w-[calc((100%-3.75rem)/7)] lg:w-[calc((100%-6rem)/7)]',
        className,
      )}
      onClick={onClick}
      {...attributes}
      {...listeners}
    >
      <AspectRatio ratio={1}>
        {media.id === 'uploader' ? (
          <div
            className={cn(
              'flex h-full w-full cursor-pointer items-center justify-center overflow-hidden rounded-base bg-white-100 transition-opacity dark:bg-black-300',
              !active && 'opacity-50 hover:opacity-70',
            )}
          >
            <Plus16Icon />
          </div>
        ) : (
          <div
            className={cn(
              'h-full w-full overflow-hidden rounded-base bg-white-100 transition-opacity dark:bg-black-300',
              !active && 'opacity-50 hover:opacity-70',
            )}
          >
            {playbackId ? (
              <Image
                className="h-full w-full rounded-base bg-white-100 object-cover dark:bg-black-300"
                src={`https://image.mux.com/${playbackId}/thumbnail.jpg?width=200&height=200&fit_mode=crop`}
                alt=""
                width={100}
                height={100}
                unoptimized
              />
            ) : (
              <>
                {media?.url && (
                  <CroppedImage
                    className="object-cover"
                    src={media.url}
                    cropArea={media.cropArea}
                    width={100}
                    height={100}
                    alt=""
                    priority
                  />
                )}
                {media?.mux_upload_id && media?.mux_asset?.status !== 'ready' && (
                  <div
                    className={cn(
                      'absolute left-0 top-0 flex h-full w-full items-center justify-center',
                      media?.url && 'bg-black-500/50',
                    )}
                  >
                    <HourglassProgress24Icon />
                  </div>
                )}
              </>
            )}
          </div>
        )}
        {saving && media?.draft && (
          <Progress
            className="absolute left-1/2 top-1/2 w-3/4 -translate-x-1/2 -translate-y-1/2"
            value={uploadState?.progress ?? 0}
          />
        )}
        {media.id !== 'uploader' && !saving && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  className="absolute -right-2 -top-2 z-10 hidden h-7 w-7 rounded-full border-2 border-white-500 bg-white-100 transition-colors hover:bg-white-200 group-hover:flex dark:border-black-500 dark:bg-black-300 dark:hover:bg-black-500"
                  onClick={() => handleDelete(media.id)}
                  variant="inline"
                >
                  <span className="sr-only">Delete</span>
                  <Close24Icon className="h-3 w-3 text-black-200 dark:text-grey-100" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Delete</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </AspectRatio>
    </div>
  )
}
