'use client'

import { Elements } from '@stripe/react-stripe-js'
import { loadStripe } from '@stripe/stripe-js'
import React, { useEffect, useState } from 'react'

import { CommunityCreationForm } from './community-creation-form'
import { createStripeSetupIntentApi } from '@memberup/shared/src/services/apis/stripe.api'
import { SkeletonBox } from '@/components/ui'
import { TabSwitch } from '@/components/ui/tab-switch'
import { STRIPE_PUBLISH_KEY } from '@/shared-config/envs'
import { MEMBERUP_UNIFIED_ACCOUNT_PLANS } from '@/shared-settings/plans'
import { RECURRING_INTERVAL_ENUM } from '@/shared-types/enum'

const stripePromise = loadStripe(STRIPE_PUBLISH_KEY)

export function CommunityCreationFormContainer({ onSuccess }) {
  const [subscriptionIntentClientSecret, setSubscriptionIntentClientSecret] = useState(null)
  const [couponDetails, setCouponDetails] = useState(null)

  const [recurringInterval, setRecurringInterval] = useState(RECURRING_INTERVAL_ENUM.month)
  const setupPaymentIntent = async () => {
    try {
      setSubscriptionIntentClientSecret(null)
      const res = await createStripeSetupIntentApi(false, {
        payment_method_types: ['card'],
      })
      setSubscriptionIntentClientSecret(res.data.data['client_secret'])
    } catch (err) {
      console.error(err.response?.message)
    }
  }

  useEffect(() => {
    setupPaymentIntent()
  }, [])

  const isMonthly = recurringInterval === RECURRING_INTERVAL_ENUM.month

  const plan = MEMBERUP_UNIFIED_ACCOUNT_PLANS.find((p) => p.recurringInterval == recurringInterval)

  const amountDiscount = (couponDetails?.percent_off / 100) * plan.price

  const period = isMonthly ? 'month' : 'year'

  let priceExplanation = `Free for ${plan.trialDays} days, then $${plan.price}/${period}. Cancel anytime. All features. Unlimited everything. No hidden fees.`

  if (amountDiscount > 0) {
    priceExplanation += ` Using the coupon you save $${amountDiscount}`
  }

  const onPeriodChange = (value: RECURRING_INTERVAL_ENUM) => {
    setRecurringInterval(value)
  }

  return (
    <div>
      <div className="text-white mb-4 text-center text-2xl font-semibold">Create Your Community</div>
      <div className="mb-5 text-center text-sm text-black-100">{priceExplanation}</div>
      <TabSwitch
        className="mb-8"
        onChange={onPeriodChange}
        options={[
          { label: 'Monthly', value: RECURRING_INTERVAL_ENUM.month },
          { label: 'Annually (Save $100)', value: RECURRING_INTERVAL_ENUM.year },
        ]}
        value={recurringInterval}
      />
      <div>
        {Boolean(stripePromise) && Boolean(plan) && !!subscriptionIntentClientSecret ? (
          <Elements
            stripe={stripePromise}
            options={{
              clientSecret: subscriptionIntentClientSecret,
              appearance: {
                theme: 'night',
              },
              loader: 'always',
            }}
          >
            <CommunityCreationForm
              clientSecret={subscriptionIntentClientSecret}
              plan={plan}
              onSuccess={onSuccess}
              couponDetails={couponDetails}
              recurringInterval={recurringInterval}
              setCouponDetails={setCouponDetails}
            />
          </Elements>
        ) : (
          <SkeletonBox />
        )}
      </div>
    </div>
  )
}
