'use client'

import { CardElement, useElements, useStripe } from '@stripe/react-stripe-js'
import moment from 'moment-timezone'
import React, { useEffect, useRef, useState } from 'react'

import { Button, ControlVariants, Input } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { cn } from '@/lib/utils'
import { createMembershipApi, getMembershipApi } from '@/shared-services/apis/membership.api'
import { createStripeMemberUpSubscriptionApi, getStripePromotionCodeApi } from '@/shared-services/apis/stripe.api'
import { RECURRING_INTERVAL_ENUM } from '@/shared-types/enum'

export function CommunityCreationForm(props: {
  clientSecret?: string
  plan: any
  recurringInterval: RECURRING_INTERVAL_ENUM
  onSuccess: any
  couponDetails: any
  setCouponDetails: any
}) {
  const { clientSecret, onSuccess, plan, couponDetails, setCouponDetails, recurringInterval } = props
  const stripe = useStripe()
  const elements = useElements()
  const [loading, setLoading] = useState(false)
  const [cardComplete, setCardComplete] = useState(false)
  const [cardInputEmpty, setCardInputEmpty] = useState(true)
  const [cardError, setCardError] = useState(false)
  const [error, setError] = useState('')
  const [newMembership, setNewMembership] = useState<any>(null)
  const checkPaymentInterval = useRef(null)
  const [promoCodeInput, setPromoCodeInput] = useState(null)
  const [promoCode, setPromoCode] = useState(null) // Store the actual promo code instance from the API
  const [promoCodeInvalid, setPromoCodeInvalid] = useState(false)
  const [loadingPromoCode, setLoadingPromoCode] = useState(false)
  const fetchingRef = useRef(false)

  useEffect(() => {
    if (!newMembership) {
      return
    }

    // Setup an interval to query the membership status
    if (checkPaymentInterval.current) {
      clearInterval(checkPaymentInterval.current)
    }

    checkPaymentInterval.current = setInterval(async () => {
      if (!checkPaymentInterval.current || fetchingRef.current) {
        return
      }
      fetchingRef.current = true

      try {
        const res = await getMembershipApi({ id: newMembership.id, slug: newMembership.slug })
        const membership = res.data.data.membership
        if (membership.active) {
          clearInterval(checkPaymentInterval.current)
          checkPaymentInterval.current = null
          onSuccess(membership)
        }
      } catch (err) {
        console.log(err)
      } finally {
        fetchingRef.current = false
      }
    }, 1000)

    return () => {
      clearInterval(checkPaymentInterval.current)
    }
  }, [newMembership])

  const handleFormSubmit = async (e: React.FormEvent) => {
    try {
      e.preventDefault()
      setLoading(true)

      let membershipId = newMembership?.id
      let membershipSlug = newMembership?.slug

      if (!membershipSlug) {
        try {
          const result = await createMembershipApi()
          setNewMembership(result.data.data.membership)
          membershipId = result.data.data.membership.id
        } catch (err) {
          setLoading(false)
          toast.error(err.message)
          return
        }
      }

      const cardElement = elements.getElement(CardElement)
      const { error, setupIntent } = await stripe.confirmCardSetup(clientSecret, {
        payment_method: {
          card: cardElement,
        },
        return_url: window.location.href,
      })

      if (error) {
        throw new Error(error.message || 'An unexpected error occurred.')
      }

      const subscriptionResult = await createStripeMemberUpSubscriptionApi({
        membership_id: membershipId,
        interval: recurringInterval,
        default_payment_method: setupIntent.payment_method as string,
        promotion_code: promoCode?.id, // Use the ID of the promo code instance
        plan_price: plan.price,
        plan_key: plan.plan_key,
      })

      const latestInvoice = subscriptionResult.data.data?.latest_invoice
      if (latestInvoice) {
        const stripeClientSecret = latestInvoice.payment_intent?.client_secret
        if (stripeClientSecret) {
          const paymentIntentResult = await stripe.confirmCardPayment(stripeClientSecret, {
            payment_method: setupIntent.payment_method as string,
            save_payment_method: true,
          })

          if (paymentIntentResult.error) {
            throw new Error('Your payment method has failed. Please try again or with a different card.')
          }
        }
      } else {
        throw new Error('Your payment method has failed. Please try again or with a different card.')
      }
    } catch (err) {
      setLoading(false)
      console.log(err)
      toast.error(err.message)
    }
  }

  const isApplyPromoCodeDisabled = loadingPromoCode || !promoCodeInput || promoCodeInput.trim() === ''

  // Helper function to clear coupon details
  const clearCouponDetails = () => {
    setCouponDetails({
      id: '',
      name: '',
      percent_off: 0,
      amount_off: 0,
      duration: '',
    })
  }

  const handleOnChangePromoCode = (e) => {
    setPromoCodeInvalid(false)
    setPromoCodeInput(e.target.value)
    // Reset the actual promo code when input changes
    setPromoCode(null)
    // Clear coupon details when input changes
    clearCouponDetails()
  }

  // Handle Enter key press to apply promo code
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !isApplyPromoCodeDisabled) {
      e.preventDefault() // Prevent form submission
      handleApplyPromoCode()
    }
  }

  const handleApplyPromoCode = async () => {
    try {
      if (!promoCodeInput) {
        setPromoCodeInvalid(true)
        setPromoCode(null)
        clearCouponDetails()
        return
      }
      setLoadingPromoCode(true)

      try {
        const res = await getStripePromotionCodeApi(false, promoCodeInput)
        console.log('Promo code response:', res.data)

        // Check if we have data and it has a length (it's an array)
        if (res.data.data && Array.isArray(res.data.data) && res.data.data.length > 0) {
          const promoData = res.data.data[0] // Get the first promotion code
          if (promoData && promoData.coupon) {
            // Store the actual promo code instance
            setPromoCode(promoData)

            const couponData = promoData.coupon
            setCouponDetails({
              id: couponData.id,
              name: couponData.name || promoData.code,
              percent_off: couponData.percent_off,
              amount_off: couponData.amount_off,
              duration: couponData.duration,
            })
            setPromoCodeInvalid(false)
          } else {
            setPromoCodeInvalid(true)
            setPromoCode(null)
            // Clear coupon details when promo code is invalid
            clearCouponDetails()
          }
        } else {
          setPromoCodeInvalid(true)
          setPromoCode(null)
          // Clear coupon details when promo code is invalid
          clearCouponDetails()
        }
      } catch (err) {
        console.error('Error fetching promo code:', err)
        setPromoCodeInvalid(true)
        setPromoCode(null)
        // Clear coupon details when there's an error
        clearCouponDetails()
      }
    } catch (err: any) {
      console.error('Exception in promo code handling:', err)
      setPromoCodeInvalid(true)
      setPromoCode(null)
      // Clear coupon details when there's an exception
      clearCouponDetails()
    } finally {
      setLoadingPromoCode(false)
    }
  }

  const firstChargeDate = moment().add(recurringInterval === 'month' ? 15 : 30, 'days')
  const formattedFirstChageDate = firstChargeDate.format('MMM D, YYYY')

  const amountDiscount = couponDetails ? (couponDetails?.percent_off / 100) * plan.price : 0
  const nextPaymentAmount = plan.price - amountDiscount

  return (
    <form autoComplete="off" onSubmit={handleFormSubmit}>
      <div className="mb-4">
        <CardElement
          className={cn(
            'rounded-base border border-black-700 bg-black-100/[0.08] px-4 text-black-100 [&_iframe]:focus:outline-none',
            cardError ? 'border-red-200' : cardInputEmpty ? 'border-black-100/[0.08]' : 'border-black-100',
          )}
          options={{
            hidePostalCode: false,
            style: {
              base: {
                color: 'white',
                lineHeight: '3.4285rem',
                '::placeholder': {
                  color: '#8D94A3',
                },
              },
              invalid: {
                color: '#F34646',
                iconColor: '#F34646',
              },
            },
          }}
          onChange={(e) => {
            if (e.empty !== cardInputEmpty) {
              setCardInputEmpty(e.empty)
            }
            if (e.complete !== cardComplete) {
              setCardComplete(e.complete)
            }
            if (Boolean(e.error) !== cardError) {
              setCardError(Boolean(e.error))
            }
          }}
        />
        {Boolean(error) && <div>{error}</div>}
      </div>
      <div className="relative mb-4">
        <div className="relative">
          <Input
            placeholder="Promo Code"
            onChange={handleOnChangePromoCode}
            onKeyDown={handleKeyDown}
            variant={ControlVariants.transparent}
            className={`pr-24 ${promoCodeInvalid ? 'border-red-500' : ''} focus:border-black-100 focus:ring-1 focus:ring-black-100`}
            value={promoCodeInput || ''}
          />
          <Button
            className="absolute right-0 top-0 h-full cursor-pointer rounded-r-[0.5625rem] bg-black-700 px-4 hover:bg-black-600"
            onClick={handleApplyPromoCode}
            disabled={isApplyPromoCodeDisabled}
            variant="inline"
            type="button"
          >
            {loadingPromoCode ? (
              <span className="flex items-center justify-center">
                <svg
                  className="text-white -ml-1 mr-2 h-4 w-4 animate-spin"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span>Loading</span>
              </span>
            ) : (
              'Apply'
            )}
          </Button>
        </div>
        {Boolean(promoCodeInvalid) && <div className="mt-2 text-sm text-red-500">Promotional code is not valid</div>}
        {promoCode && !promoCodeInvalid && (
          <div className="mt-2 text-sm text-green-500">
            Promotional code <span className="font-bold">{promoCode.code}</span> applied successfully!
          </div>
        )}
      </div>
      <div className={'mt-5'}>
        <Button
          className={'w-full'}
          type="submit"
          variant="default"
          loading={loading}
          disabled={loading || !cardComplete}
          data-cy="create-community-button"
        >
          Start free trial
        </Button>
      </div>
      <div className="mt-5 text-sm text-black-100">
        Your first charge will be on {formattedFirstChageDate} for ${nextPaymentAmount}. Cancel anytime with 1-click. By
        submitting, you accept our{' '}
        <a className="" href="/terms">
          terms
        </a>
        .
      </div>
    </form>
  )
}
