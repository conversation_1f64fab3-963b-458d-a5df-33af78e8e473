import { useRouter, useSearchParams } from 'next/navigation'
import React, { useState } from 'react'

import { Favicon } from '@/components/community/favicon'
import { Button } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { removeQueryParam } from '@/lib/utils'
import { joinMembershipApi } from '@/shared-services/apis/membership.api'
import DynamicForm from '@/src/components/community/dynamic-form'

export function JoinCommunityQuestions() {
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = useStore((state) => state.community.membership.membership_setting)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const setJoinCommunityId = useStore((state) => state.auth.setJoinCommunityId)
  const updateUserMembership = useStore((state) => state.auth.updateUserMembership)
  const [isMembershipPending, setIsMembershipPending] = useState(false)
  const user = useStore((state) => state.auth.user)
  const inviteToken = useStore((state) => state.auth.inviteToken)
  const setInviteToken = useStore((state) => state.auth.setInviteToken)
  const searchParams = useSearchParams()
  const router = useRouter()

  const handleOnContinueClick = () => {
    setJoinCommunityId(null)
  }
  const handleOnSubmit = async (data) => {
    try {
      setIsSubmitting(true)

      let formData
      if (membershipSetting.form) {
        const outputValues = JSON.parse(JSON.stringify(membershipSetting.form.fields))
        const arrayToObject = (array) => {
          return array.reduce((obj, item) => {
            obj[item.name] = item
            return obj
          }, {})
        }
        const result = arrayToObject(outputValues)
        Object.entries(data).forEach((entry) => {
          const [k, v] = entry
          result[k].value = v
        })
        formData = {
          fields: Object.values(result),
        }
      }
      const res = await joinMembershipApi(membership.id, formData, inviteToken)
      if (inviteToken) {
        removeQueryParam('invite_token', searchParams, router)
        toast.success(`Successfully joined the community!`)
        setInviteToken(null)
      }

      updateUserMembership(res.data.data)
      setIsSubmitting(false)
      setIsMembershipPending(true)
    } catch (e) {
      setIsSubmitting(false)
    }
  }

  const renderFooter = () => {
    if (isMembershipPending) {
      return (
        <Button
          className={'mt-4 w-full'}
          type="submit"
          variant="default"
          loading={isSubmitting}
          disabled={isSubmitting}
          data-cy="signin-button"
          onClick={handleOnContinueClick}
        >
          Continue
        </Button>
      )
    }

    if (membershipSetting.form) {
      return (
        <DynamicForm
          fields={membershipSetting.form.fields}
          isSubmitting={isSubmitting}
          onSubmit={(data) => handleOnSubmit(data)}
        />
      )
    }

    return (
      <Button
        className={'mt-4 w-full'}
        type="submit"
        variant="default"
        loading={isSubmitting}
        disabled={isSubmitting}
        data-cy="signin-button"
        onClick={handleOnSubmit}
      >
        Join Community
      </Button>
    )
  }

  return (
    <div className="flex flex-col justify-center p-2">
      <div className="mb-5 flex w-full justify-center">
        <Favicon
          className="h-16 w-16"
          communityName={membership.name}
          src={membershipSetting.favicon}
          cropArea={membershipSetting.favicon_crop_area}
          width={48}
          height={48}
        />
      </div>
      <div>
        <div className="w-full flex-col items-start justify-start gap-6">
          <div className="flex flex-col items-center justify-start gap-1.5">
            {membershipSetting.form && !isMembershipPending && (
              <div className="text-white text-center text-[18px] font-semibold leading-normal">
                Answer a Few Questions
              </div>
            )}
            {!membershipSetting.form && !isMembershipPending && (
              <>
                <div className="text-white text-center text-[18px] font-semibold leading-normal">Accept the terms</div>
                <p className={'text-sm text-black-100'}>to join {membership?.name} Community</p>
              </>
            )}
            {isMembershipPending && (
              <div>
                <div className="text-white mb-4 text-center text-[18px] font-semibold leading-normal">
                  Your membership is awaiting approval
                </div>
                <div className="text-white text-center text-[14px] font-normal text-black-100">
                  You will get full access to the community after it is approved by an Admin. Stay tuned!
                </div>
              </div>
            )}
          </div>
          {renderFooter()}
        </div>
      </div>
    </div>
  )
}
