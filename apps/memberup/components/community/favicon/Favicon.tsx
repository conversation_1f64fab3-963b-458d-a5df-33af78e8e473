import { CroppedImage } from '@/components/images/cropped-image'
import { cn } from '@/lib/utils'
import { TAppCropArea } from '@/shared-types/types'

export function Favicon({
  className,
  src,
  cropArea,
  communityName,
  width,
  height,
  variant = 'regular',
}: {
  className?: string
  src?: string
  cropArea?: TAppCropArea
  communityName: string
  width: number
  height: number
  variant?: 'regular' | 'inverted'
}) {
  return (
    <div
      className={cn(
        'favicon relative flex shrink-0 select-none items-center justify-center overflow-hidden rounded-xl text-base font-semibold',
        variant === 'regular'
          ? 'bg-grey-200 text-black-200 dark:bg-black-500 dark:text-black-100'
          : 'bg-white-200 text-black-700 dark:bg-grey-300 dark:text-black-700',
        className,
      )}
    >
      {src ? (
        <CroppedImage
          cropArea={cropArea || { x: 0, y: 0, width: 100, height: 100 }}
          className="absolute"
          src={src}
          alt={communityName}
          width={width}
          height={height}
          priority
        />
      ) : (
        <div>{communityName.replace(/\s+/g, '').slice(0, 2).toUpperCase()}</div>
      )}
    </div>
  )
}
