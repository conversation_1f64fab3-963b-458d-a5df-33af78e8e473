import Link from 'next/link'
import { useRouter } from 'next/navigation'

import { Favicon } from '@/components/community/favicon'
import { CommunityTypeAndPricingScheme } from '@/components/community/formatting/community-type-and-pricing-scheme'
import { buttonVariants } from '@/components/ui/button'
import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'
import { IMembership } from '@/shared-types/interfaces'

export function CommunityListingCard({ community }: { community: Partial<IMembership> }) {
  const router = useRouter()
  const setMembership = useStore((state) => state.community.setMembership)

  return (
    <div className="rounded-base bg-white-500 px-5 py-3 text-sm text-black-700 dark:bg-black-500 dark:text-white-500">
      <div className="mb-4 flex">
        <Favicon
          className="mr-4 h-12 w-12"
          communityName={community.name}
          src={community.membership_setting.favicon}
          cropArea={community.membership_setting.favicon_crop_area}
          width={48}
          height={48}
          variant="inverted"
        />
        <div className="flex flex-col justify-center">
          <div className="font-semibold">{community.name}</div>
          <div className="text-xs text-black-200 dark:text-black-100">
            <CommunityTypeAndPricingScheme community={community} />
          </div>
        </div>
      </div>
      {community.membership_setting.description && (
        <div className="mb-4 text-xs text-black-600 dark:text-white-200">
          {community.membership_setting.description}
        </div>
      )}
      <Link
        href={`${community.slug}`}
        className={cn(buttonVariants(), 'w-full')}
        onClick={(e) => {
          e.preventDefault()
          setMembership(community)
          router.push(`/${community.slug}`)
        }}
      >
        Visit
      </Link>
    </div>
  )
}
