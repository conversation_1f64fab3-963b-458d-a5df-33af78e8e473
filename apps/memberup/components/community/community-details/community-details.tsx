'use client'

import Image from 'next/image'
import React, { memo, useState } from 'react'

import { CommunityStats } from './community-stats'
import { JoinCommunityButton } from '@/components/community/join-community-button'
import { Link12Icon } from '@/components/icons/12px/Link12Icon'
import { PublicCommunity12Icon } from '@/components/icons/12px/PublicCommunity12Icon'
import { Lock16Icon } from '@/components/icons/16px/Lock16Icon'
import { CroppedImage } from '@/components/images/cropped-image'
import { PoweredBy } from '@/components/layout'
import { AspectRatio, Separator } from '@/components/ui'
import { StickyContainer } from '@/components/ui/sticky-container/StickyContainer'
import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import { VISIBILITY_ENUM } from '@/shared-types/enum'
import { ILink } from '@/shared-types/interfaces'
import { TApp<PERSON>ropArea } from '@/shared-types/types'

function CommunityDetailsComponent({ children, className }: { children?: React.ReactNode; className?: string }) {
  const { isCurrentUserAdmin } = useCheckUserRole()
  const membership = useStore((state) => state.community.membership)
  const communityAdminSettingsOpen = useStore((state) => state.community.communityAdminSettingsOpen)
  const setCommunityAdminSettingsOpen = useStore((state) => state.community.setCommunityAdminSettingsOpen)
  const membershipSetting = membership?.membership_setting
  const membershipStatus = useStore((state) => state.community.userMembership?.status)
  const [settingsModalOpen, setSettingsModalOpen] = useState(false)

  const openCommunitySettings = (e: React.MouseEvent) => {
    e.preventDefault()
    setCommunityAdminSettingsOpen(true)
  }

  const externalLinks = (membershipSetting.external_links as unknown as ILink[]) || []

  if (!membershipSetting) {
    return
  }

  return (
    <StickyContainer
      className={cn(
        'community-details mb-4 hidden w-full shrink-0 overflow-hidden md:mb-0 md:block md:w-72 xl:w-[19rem]',
        className,
      )}
    >
      <div className="rounded-container all-wrapper overflow-hidden">
        <div className="image-wrapper relative flex flex-col items-center">
          <AspectRatio ratio={16 / 9} className="image-container overflow-hidden">
            {membershipSetting.cover_image ? (
              <CroppedImage
                className="overflow-hidden"
                src={membershipSetting.cover_image}
                cropArea={membershipSetting.cover_image_crop_area as TAppCropArea}
                width={800}
                height={450}
                alt={`${membership.name} cover photo`}
              />
            ) : (
              <AspectRatio ratio={16 / 9} className="overflow-hidden">
                <Image
                  className="h-full w-full object-cover"
                  src="/img/cover-photo-placeholder.jpg"
                  width={800}
                  height={450}
                  alt="Profile cover"
                />
              </AspectRatio>
            )}
          </AspectRatio>
          <div className="absolute bottom-5 left-5 flex h-6 items-center rounded-xl bg-grey-700/60 px-2 text-[0.625rem] font-semibold">
            {membershipSetting.visibility === VISIBILITY_ENUM.public ? (
              <>
                <PublicCommunity12Icon className="mr-1" />
                PUBLIC
              </>
            ) : (
              <>
                <Lock16Icon className="-ml-0.5 mr-1 h-3 w-3" />
                PRIVATE
              </>
            )}
          </div>
        </div>
        <div className="p-5">
          <h1 className="text-lg font-semibold leading-5">{membership.name}</h1>
          {membershipSetting.description ||
            (isCurrentUserAdmin && (
              <p className="mt-2 text-sbase text-black-600 dark:text-white-200">
                {membershipSetting.description || 'Add your community description by clicking the “Settings” button'}
              </p>
            ))}
          {externalLinks.length > 0 && (
            <ul className="mt-5 space-y-2">
              {externalLinks.map((link, index) => (
                <li key={index}>
                  <a className="text-sbase text-black-200 dark:text-black-100" href={link.url}>
                    <Link12Icon className="mr-2 inline align-middle" />
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          )}
        </div>
        <Separator />
        <CommunityStats />
        <div className="mt-5 px-5 pb-5">
          <JoinCommunityButton membership={membership} membershipSetting={membershipSetting} />
        </div>
      </div>
      {children}
      <PoweredBy className="hidden md:mt-6 md:flex" />
    </StickyContainer>
  )
}

export const CommunityDetails = memo(CommunityDetailsComponent)
