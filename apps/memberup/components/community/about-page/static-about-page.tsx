'use client'

import { useEffect, useRef, useState } from 'react'

import { StaticGallery } from '../static-gallery/static-gallery'
import { CommunityDetails } from '@/components/community/community-details'
import { PublicCommunity12Icon } from '@/components/icons/12px/PublicCommunity12Icon'
import { Lock16Icon } from '@/components/icons/16px/Lock16Icon'
import { Tag20Icon } from '@/components/icons/20px/tag-20-icon'
import { Group24Icon } from '@/components/icons/24px/group-24-icon'
import { ProfilePicture } from '@/components/images/profile-picture'
import { useMountedRef } from '@/hooks/useMountedRef'
import { useStore } from '@/hooks/useStore'
import { formatThousands } from '@/lib/formatting'
import { getCommunityPricing, isPublicCommunity } from '@/shared-libs/membership-settings'
import { getMemberFullName } from '@/shared-libs/profile'
import { getMembershipStatsApi } from '@/shared-services/apis/membership.api'
import { getMuxAssetApi, getMuxUploadApi } from '@/shared-services/apis/mux.api'

type MuxAssetStatus = 'in-progress' | 'done' | 'queued'

interface MuxAssetPollingEntry {
  interval: ReturnType<typeof setInterval>
  status: MuxAssetStatus
}

interface MuxAssetPollingMap {
  [assetId: string]: MuxAssetPollingEntry
}

export default function StaticCommunityAboutPage() {
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = useStore((state) => state.community.membership.membership_setting)
  const owner = useStore((state) => state.community.membership.owner)
  const community = useStore((state) => state.community)
  const [membersCount, setMembersCount] = useState(0)

  const galleryMedia = membershipSetting?.about_gallery as any[]
  const [tempGalleryVideoData, setTempGalleryVideoData] = useState({})

  const muxUploadPolling = useRef<MuxAssetPollingMap>({})
  const mountedRef = useMountedRef()

  useEffect(() => {
    if (!membership) {
      return
    }
    async function initialize() {
      const stats = await getMembershipStatsApi(membership.id)
      setMembersCount(stats.data.totalMembers)
    }
    initialize()
  }, [membership])

  const startUploadPolling = async (uploadId: any) => {
    if (!(muxUploadPolling.current[uploadId] === undefined || muxUploadPolling.current[uploadId] === null)) return

    muxUploadPolling.current[uploadId] = {
      interval: null,
      status: 'queued',
    }

    const pollMuxUpload = async () => {
      if (['in-progress', 'done'].includes(muxUploadPolling.current[uploadId]?.status)) return

      muxUploadPolling.current[uploadId].status = 'in-progress'

      const res = await getMuxUploadApi(uploadId)
      if (!mountedRef.current) return

      const assetId = res?.data?.data?.asset_id
      if (!assetId) muxUploadPolling.current[uploadId].status = 'queued'

      const {
        data: { data: assetData },
      } = await getMuxAssetApi(assetId)
      if (!mountedRef.current) return

      if (assetData?.playback_ids?.[0]?.id) {
        muxUploadPolling.current[uploadId].status = 'done'

        clearInterval(muxUploadPolling.current[uploadId].interval)
        setTempGalleryVideoData((tempData) => ({
          ...tempData,
          [uploadId]: assetData,
        }))
      }
    }

    muxUploadPolling.current[uploadId].interval = setInterval(async () => {
      pollMuxUpload()
    }, 5000)

    pollMuxUpload()
  }

  useEffect(() => {
    galleryMedia?.forEach((item) => {
      if (item?.mux_upload_id && !item.mux_asset?.playback_ids?.[0]?.id) {
        startUploadPolling(item.mux_upload_id)
      }
    })
  }, [galleryMedia])

  const getAssetData = (item: any) => {
    if (!item?.mux_upload_id) return

    if (item?.mux_asset?.playback_ids?.[0]?.id) {
      return item.mux_asset
    }
    if (tempGalleryVideoData[item.mux_upload_id]) {
      return tempGalleryVideoData[item?.mux_upload_id]
    }
  }

  return (
    <div className="space-y:6 page-inner-pb flex flex-col-reverse items-start md:flex-row md:space-x-6 md:space-y-0">
      <div className="relative w-full rounded-base bg-white-500 dark:bg-black-500">
        <div className="p-5">
          {galleryMedia && galleryMedia.length > 0 && (
            <StaticGallery getAssetData={getAssetData} media={galleryMedia} />
          )}
        </div>
        <div className="inline-flex w-full items-center justify-evenly border-b border-t border-grey-200 p-4 text-xs font-semibold text-black-700 dark:border-grey-900 dark:text-white-500">
          <div className="flex flex-grow items-center justify-center gap-2">
            {isPublicCommunity(membershipSetting) ? (
              <PublicCommunity12Icon className="mr-2 inline h-5 w-5 shrink-0 align-middle" />
            ) : (
              <Lock16Icon className="mr-2 inline h-5 w-5 shrink-0 align-middle" />
            )}
            {isPublicCommunity(membershipSetting) ? 'Public' : 'Private'}
          </div>
          <div className="flex flex-grow items-center justify-center gap-2">
            <div className="relative h-5 w-[1.375rem] overflow-hidden">
              <Group24Icon className="absolute -top-0.5 h-[1.375rem] w-[1.375rem]" />
            </div>
            {membersCount && <div>{formatThousands(membersCount)} Members</div>}
          </div>
          <div className="flex flex-grow items-center justify-center gap-2">
            <div className="h-5 w-5">
              <Tag20Icon />
            </div>
            <div>{Boolean(getCommunityPricing(membershipSetting)) ? 'Paid' : 'Free'}</div>
          </div>
          {owner && (
            <div className="flex flex-grow items-center justify-center gap-2">
              <div>
                <ProfilePicture
                  className="h-6 w-6"
                  src={owner.profile.image}
                  cropArea={owner.profile.image_crop_area}
                  alt={getMemberFullName(owner)}
                  height={24}
                  width={24}
                />
              </div>
              <div>By {getMemberFullName(owner)}</div>
            </div>
          )}
        </div>
        <div
          className="rendered-editor-text p-5 text-sm text-black-600 dark:text-white-200"
          dangerouslySetInnerHTML={{
            __html: membershipSetting.about_text,
          }}
        />
      </div>
      <CommunityDetails />
    </div>
  )
}
