import { useKnockFeed } from '@knocklabs/react'
import { useEffect, useRef, useState } from 'react'
import { useInView } from 'react-intersection-observer'

import { NotificationsMenuItem } from './notifications-menu-item'
import { LoaderCircle24Icon } from '@/components/icons'
import { MoreHorizontal24Icon } from '@/components/icons/24px/more-horizontal-24-icon'
import { Button } from '@/components/ui'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useStore } from '@/hooks/useStore'

export default function NotificationsMenu({ onItemClick }: { onItemClick?: () => void }) {
  const { feedClient, useFeedStore } = useKnockFeed()
  const { items, metadata, pageInfo } = useFeedStore()
  const userMemberships = useStore((state) => state.auth.user?.user_memberships)
  const { ref, inView } = useInView()
  const [markingAllAsRead, setMarkingAllAsRead] = useState(false)
  const after = pageInfo?.after
  const firstPageRef = useRef(true)
  const communitySlugs = new Set(
    items.map((item) => typeof item.data?.community_slug === 'string' && item.data.community_slug.slice(1)),
  )
  const communityFavicons = {}

  userMemberships?.forEach((userMembership) => {
    if (communitySlugs.has(userMembership.membership.slug)) {
      communityFavicons[userMembership.membership.slug] = {
        favicon: userMembership.membership.membership_setting.favicon,
        faviconCropArea: userMembership.membership.membership_setting.favicon_crop_area,
      }
    }
  })

  const markAllAsRead = async (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation()
    event.preventDefault()

    if (markingAllAsRead) return

    setMarkingAllAsRead(true)
    await feedClient.markAllAsRead()
    setMarkingAllAsRead(false)
  }

  useEffect(() => {
    if (firstPageRef.current) {
      firstPageRef.current = false
      return
    }

    if (inView && after) {
      feedClient.fetchNextPage()
    }
  }, [after, inView, feedClient, pageInfo])

  return (
    <div>
      <div className="flex h-[3.75rem] items-center border-b border-b-grey-200 p-4 dark:border-b-grey-900 md:h-auto">
        <h3 className="grow text-sm font-medium">Notifications</h3>
        {metadata?.unread_count > 0 && (
          <>
            <Button
              className="hidden font-normal text-primary-100 transition-colors hover:text-primary-200 md:flex"
              variant="inline"
              onClick={markAllAsRead}
            >
              Mark all as read
            </Button>
            <Popover>
              <PopoverTrigger className="ml-2 md:hidden" asChild>
                <Button
                  className="font-normal text-black-200 transition-colors hover:text-primary-200 dark:text-black-100"
                  variant="inline"
                >
                  <MoreHorizontal24Icon />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto" side="bottom">
                <Button
                  className="p-2.5 font-normal text-black-200 transition-colors hover:text-primary-200 dark:text-black-100"
                  variant="inline"
                  onClick={markAllAsRead}
                >
                  Mark all as read
                </Button>
              </PopoverContent>
            </Popover>
          </>
        )}
      </div>
      {items.length > 0 ? (
        <ScrollArea
          className="absolute left-0 top-28 h-[calc(100%-7rem)] w-full max-w-full flex-1 overflow-hidden md:relative md:top-0 md:h-auto md:flex-none md:overflow-auto"
          viewportClassName="md:max-h-[calc(50vh)] max-w-full [&>div]:max-w-full"
        >
          {items.map((item) => (
            <NotificationsMenuItem
              faviconData={
                typeof item.data?.community_slug === 'string' &&
                communityFavicons?.[item.data?.community_slug?.slice(1)]
              }
              item={item}
              key={item.id}
              onItemClick={onItemClick}
            />
          ))}
          {after && (
            <div className="flex justify-center py-2" ref={ref}>
              <LoaderCircle24Icon className="mr-2 h-[1.125rem] w-[1.125rem] animate-spin text-black-200 dark:text-black-100" />
            </div>
          )}
        </ScrollArea>
      ) : (
        <div className="py-24 text-center text-sm text-black-200 dark:text-black-100">No notifications yet</div>
      )}
    </div>
  )
}
