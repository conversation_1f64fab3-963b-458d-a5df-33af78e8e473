import { useKnockFeed } from '@knocklabs/react'

import { Bell24Icon } from '@/components/icons/24px/bell-24-icon'
import { NotificationsIndicator } from '@/components/ui/notifications-indicator'
import { cn } from '@/lib/utils'

export function NotificationsIcon({ small }: { small?: boolean }) {
  const { useFeedStore } = useKnockFeed()
  const { metadata } = useFeedStore()

  return (
    <NotificationsIndicator count={metadata?.unread_count || 0} small={small}>
      <Bell24Icon className={cn(small ? 'h-[1.125rem] w-[1.125rem]' : '')} />
    </NotificationsIndicator>
  )
}
