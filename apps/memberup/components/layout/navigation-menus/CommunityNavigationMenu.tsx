import { useParams } from 'next/navigation'

import { NavigationMenu } from './NavigationMenu'
import { NavigationMenuItem } from './NavigationMenuItem'
import { CalendarSelection24Icon, Content24Icon, Home24Icon, Info24Icon, Users24Icon } from '@/components/icons'
import { useStore } from '@/hooks/useStore'
import { isUserActiveAndAcceptedInCommunity } from '@/shared-libs/profile'
import { VISIBILITY_ENUM } from '@/shared-types/enum'

export function CommunityNavigationMenu() {
  const params = useParams()
  const membership = useStore((state) => state.community.membership)
  const user = useStore((state) => state.auth.user)

  // Private communities for non-logged in users or non-approved members should show just about page.
  const userBelongsToCommunity = isUserActiveAndAcceptedInCommunity(user, membership)
  const userCanViewCommunity =
    membership?.membership_setting?.visibility === VISIBILITY_ENUM.public || userBelongsToCommunity

  const communityURL = (path: string) => `/${params.slug}${path}`

  return (
    <NavigationMenu>
      <NavigationMenuItem href={communityURL('')} icon={<Home24Icon />} disabled={!userCanViewCommunity}>
        Community
      </NavigationMenuItem>
      <NavigationMenuItem href={communityURL('/content')} icon={<Content24Icon />} disabled={!userBelongsToCommunity}>
        Content
      </NavigationMenuItem>
      <NavigationMenuItem
        href={communityURL('/events')}
        icon={<CalendarSelection24Icon />}
        disabled={!userBelongsToCommunity}
      >
        Events
      </NavigationMenuItem>
      <NavigationMenuItem href={communityURL('/members')} icon={<Users24Icon />} disabled={!userBelongsToCommunity}>
        Members
      </NavigationMenuItem>
      <NavigationMenuItem href={communityURL('/about')} icon={<Info24Icon />}>
        About
      </NavigationMenuItem>
    </NavigationMenu>
  )
}
