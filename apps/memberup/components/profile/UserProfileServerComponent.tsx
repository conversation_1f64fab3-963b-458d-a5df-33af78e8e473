import { notFound } from 'next/navigation'

import { UserProfileClientComponent } from './UserProfileClientComponent'
import { getUserPublicDataByUsername } from '@/lib/server/users'

export const UserProfileServerComponent = async ({ params }) => {
  const username = params.slug.split('%40')[1]
  const userData = await getUserPublicDataByUsername(username)

  if (!userData) {
    notFound()
  }

  return <UserProfileClientComponent userData={userData} />
}
