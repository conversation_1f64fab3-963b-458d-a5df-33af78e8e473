'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'

import { CommunityListingCard } from '../community/community-listing-card/community-listing-card'
import { Favicon } from '../community/favicon'
import { UserDetails } from './UserDetails'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { CommunityTypeAndPricingScheme } from '@/components/community/formatting/community-type-and-pricing-scheme'
import { Button } from '@/components/ui'
import { RadioDropdown } from '@/components/ui/radio-dropdown'
import { SkeletonBox } from '@/components/ui/skeleton'
import { ActivityCalendar } from '@/components/visualization'
import { useStore } from '@/hooks/useStore'
import { useUserProfileData } from '@/hooks/useUserProfileData'
import FeedCardCondensed from '@/memberup/components/feed/feed-card-condensed'
import { IUser } from '@/shared-types/interfaces'

export function UserProfileClientComponent({ userData }: { userData: IUser }) {
  const router = useRouter()
  const viewingUser = useStore((state) => state.auth.user)
  const userIsViewingUser = viewingUser?.id === userData.id

  const [ownerCommunities, setOwnerCommunities] = useState([])
  const [memberCommunities, setMembersCommunities] = useState([])
  const [communitiesInCommon, setCommunitiesInCommon] = useState([])
  const [communityOptions, setCommunityOptions] = useState([])
  const membership = useStore((state) => state.community.membership)
  // If the user navigated from a community to this user profile then we select the community as the default.
  const [selectedMembership, setSelectedMembership] = useState(membership || userData.user_memberships[0].membership)

  const viewingUserCommunitiesSlug =
    viewingUser?.user_memberships.map((userMembership) => userMembership.membership.slug) || []
  const selectedCommunitySlug = selectedMembership?.slug

  useEffect(() => {
    if (!userData || !viewingUser) {
      return
    }

    const computedOwnerCommunities = []
    const computedMemberCommunities = []
    const computedCommunitiesInCommon = []
    const computedCommunityOptions = []
    userData.user_memberships.forEach(({ user_role, membership }) => {
      if (user_role === USER_ROLE_ENUM.owner) {
        computedOwnerCommunities.push(membership)
      } else {
        computedMemberCommunities.push(membership)
      }

      if (userIsViewingUser || viewingUserCommunitiesSlug?.includes(membership.slug)) {
        computedCommunityOptions.push({
          label: membership.name,
          value: membership.slug,
        })

        if (user_role !== USER_ROLE_ENUM.owner) {
          computedCommunitiesInCommon.push(membership)
        }
      }
    })

    setOwnerCommunities(computedOwnerCommunities)
    setMembersCommunities(computedMemberCommunities)
    setCommunitiesInCommon(computedCommunitiesInCommon)
    setCommunityOptions(computedCommunityOptions)
  }, [userData, viewingUser])

  const { activityCalendarData, fetchMessages, hasMore, loadingPosts, posts, postComments } = useUserProfileData(
    userData,
    selectedMembership,
  )

  const initialized = useRef(false)

  useEffect(() => {
    if (!initialized.current) {
      const el = document.getElementById('page-content')
      if (el) {
        el.scrollTo(0, 0)
      }
      initialized.current = true
    }
  }, [])

  const communityDropdownLabel = selectedCommunitySlug
    ? communityOptions.find((c) => c.value === selectedCommunitySlug)?.label
    : 'Select a community'

  const handleOnCommunityChange = (communitySlug) => {
    setSelectedMembership(userData.user_memberships.find((um) => um.membership.slug === communitySlug)?.membership)
  }

  return (
    <div className="user-profile space-y:6 page-inner-pb flex flex-col-reverse items-start md:flex-row md:space-x-6">
      <div className="w-full max-w-full shrink grow overflow-x-hidden md:w-auto">
        {ownerCommunities.length > 0 && (
          <div className="mb-6 sm:mb-8">
            <h2 className="mb-3 text-base font-semibold sm:text-lg">Created by {userData.first_name}</h2>
            <div className="space-y-5 lg:grid lg:auto-cols-fr lg:grid-cols-2 lg:place-content-start lg:gap-5 lg:space-y-0">
              {ownerCommunities.map((community) => (
                <CommunityListingCard key={community.id} community={community} />
              ))}
            </div>
          </div>
        )}
        {memberCommunities.length > 0 && (
          <div className="mb-6 sm:mb-8">
            <h2 className="mb-3 text-base font-semibold sm:text-lg">
              Communities
              {communitiesInCommon.length > 0 && !userIsViewingUser && (
                <span className="ml-1 text-sm font-normal text-black-100 dark:text-black-200">
                  ({communitiesInCommon.length} in common)
                </span>
              )}
            </h2>
            <div className="rounded-container space-y-6 p-5 sm:grid sm:grid-cols-2 sm:gap-6 sm:space-y-0 lg:grid-cols-3">
              {memberCommunities.map((community) => (
                <div key={community.id}>
                  <Link
                    className="group flex overflow-hidden last:mb-0"
                    href={`/${community.slug}`}
                    title={community.name}
                  >
                    <Favicon
                      className="mr-4 h-12 w-12 flex-shrink-0"
                      communityName={community.name}
                      src={community.membership_setting.favicon}
                      cropArea={community.membership_setting.favicon_crop_area}
                      width={48}
                      height={48}
                      variant="inverted"
                    />
                    <div className="flex min-w-0 flex-1 flex-col justify-between py-1">
                      <div className="truncate text-xs font-semibold text-black-700 group-hover:text-black-500 dark:text-white-500 group-hover:dark:text-white-300">
                        <span>{community.name}</span>
                      </div>
                      <div className="text-xs font-normal text-gray-500 dark:text-black-100">
                        <CommunityTypeAndPricingScheme community={community} />
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        )}
        <h2 className="mb-3 text-base font-semibold sm:text-lg">Activity</h2>
        <div className="mb-6 sm:mb-8">
          {!(activityCalendarData?.data?.length > 0) ? (
            <SkeletonBox />
          ) : (
            <div className="rounded-container max-w-full shrink grow overflow-x-hidden px-[var(--container-padding)] pb-2 pt-[var(--container-padding)]">
              <ActivityCalendar data={activityCalendarData} user={userData} />
            </div>
          )}
        </div>
        {communityOptions?.length > 0 && (
          <>
            <div className="mb-5 flex w-full flex-row items-center justify-between">
              <h2 className="flex-shrink-0 text-base font-semibold sm:mb-0 sm:text-lg">Contributions</h2>
              <div className="flex w-px min-w-0 flex-1 justify-end overflow-hidden pl-4">
                <RadioDropdown
                  className="max-w-full overflow-hidden sm:w-auto"
                  label={communityDropdownLabel}
                  items={communityOptions}
                  onValueChange={handleOnCommunityChange}
                  value={selectedCommunitySlug}
                />
              </div>
            </div>
            {selectedMembership && (
              <div className="flex flex-col space-y-4">
                {loadingPosts && posts.length === 0 ? (
                  <SkeletonBox />
                ) : !loadingPosts && posts.length === 0 ? (
                  <div className="rounded-container flex h-52 select-none items-center justify-center bg-white-500 px-5 text-center text-sbase text-black-100 dark:bg-dark-background-3">
                    <span>
                      {userIsViewingUser
                        ? 'You’ve made '
                        : `${getFullName(userData.first_name, userData.last_name)} has made `}
                      0 contributions to {selectedMembership?.name}
                    </span>
                  </div>
                ) : (
                  <InfiniteScroll
                    dataLength={posts.length}
                    next={fetchMessages}
                    hasMore={hasMore}
                    loader={<SkeletonBox />}
                    scrollableTarget="page-content"
                    initialScrollY={0}
                  >
                    {posts.map((post) => (
                      <div className="mb-4 last:mb-0" key={post.id}>
                        <FeedCardCondensed
                          membership={selectedMembership}
                          comments={postComments[post.id]}
                          feed={post}
                          showPinnedMessageIndicator={false}
                        />
                      </div>
                    ))}
                  </InfiniteScroll>
                )}
              </div>
            )}
          </>
        )}
      </div>
      <UserDetails user={userData}>
        {userIsViewingUser && (
          <Button className="w-full" variant="outline" onClick={() => router.push('/settings/account/profile')}>
            Edit profile
          </Button>
        )}
      </UserDetails>
    </div>
  )
}
