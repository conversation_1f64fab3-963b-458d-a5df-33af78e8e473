'use client'

import { useRef } from 'react'
import { Provider } from 'react-redux'

import { AppStore, makeStore } from '@/memberup/store/store'
import { InitialStoreState } from '@/store'

export function ReduxStoreProvider({
  children,
  initialData,
}: {
  children: React.ReactNode
  initialData: Partial<InitialStoreState>
}) {
  const storeRef = useRef<AppStore>()

  if (!storeRef.current) {
    storeRef.current = makeStore(initialData)
  }

  return <Provider store={storeRef.current}>{children}</Provider>
}
