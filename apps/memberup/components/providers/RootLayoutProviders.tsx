'use client'

import { SessionProvider } from 'next-auth/react'
import { SWRConfig } from 'swr'
import { z } from 'zod'

import { StoreProvider } from './StoreProvider'
import { Body } from '@/components/layout/body'
import { MUThemeProvider, ReduxStoreProvider } from '@/components/providers'
import { PostHogUserDataProvider } from '@/components/providers/posthog-user-data-provider'
import { customErrorMap } from '@/lib/validation/zod'
import { InitialStoreState } from '@/store'

z.setErrorMap(customErrorMap)

export const RootLayoutProviders = ({
  children,
  initialData,
}: {
  children: React.ReactNode
  initialData: Partial<InitialStoreState>
}) => {
  return (
    <SessionProvider refetchOnWindowFocus={false}>
      <ReduxStoreProvider initialData={initialData}>
        <StoreProvider initialData={initialData}>
          <PostHogUserDataProvider>
            <MUThemeProvider>
              <SWRConfig value={{ provider: () => new Map() }}>
                <Body>{children}</Body>
              </SWRConfig>
            </MUThemeProvider>
          </PostHogUserDataProvider>
        </StoreProvider>
      </ReduxStoreProvider>
    </SessionProvider>
  )
}
