'use client'

import { useEffect, useRef } from 'react'
import { StreamChat } from 'stream-chat'
import { Chat } from 'stream-chat-react'

import { GET_STREAM_APP_KEY } from '@memberup/shared/src/config/envs'
import { CHANNEL_TYPE_ENUM, USER_STATUS_ENUM, VISIBILITY_ENUM } from '@memberup/shared/src/types/enum'
import { useStore } from '@/hooks/useStore'
import { setChannels as setInboxChannels } from '@/memberup/store/features/inboxSlice'
import { getMembers } from '@/memberup/store/features/memberSlice'
import { getChannels } from '@/memberup/store/features/spaceSlice'
import { useAppDispatch } from '@/memberup/store/store'

enum CONNECTED_CREDENTIALS_ENUM {
  anonymous = 'anonymous',
  authenticated = 'authenticated',
}

export function StreamChatProvider({ children }: { children: React.ReactNode }): React.ReactNode {
  const dispatch = useAppDispatch()
  const streamChatClientRef = useRef(
    StreamChat.getInstance(GET_STREAM_APP_KEY, {
      enableInsights: true,
      enableWSFallback: true,
      timeout: 10000,
    }),
  )
  const streamChatClient = useStore((state) => state.streamChat.streamChatClient)
  const setStreamChatClient = useStore((state) => state.streamChat.setStreamChatClient)
  const connected = useStore((state) => state.streamChat.connected)
  const setConnected = useStore((state) => state.streamChat.setConnected)
  const setConnecting = useStore((state) => state.streamChat.setConnecting)
  const streamChatUserToken = useStore((state) => state.auth.streamChatUserToken)
  const connectedAsRef = useRef<CONNECTED_CREDENTIALS_ENUM | null>(null)
  const user = useStore((state) => state.auth.user)
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = useStore((state) => state.community.membership?.membership_setting)
  const userMembership = useStore((state) =>
    membership ? state.auth.user?.user_memberships.find((um) => um.membership.id === membership.id) : null,
  )
  const gettingForMembershipRef = useRef(null)
  const gettingForUserRef = useRef(null)

  useEffect(() => {
    if (!streamChatClient) {
      setStreamChatClient(streamChatClientRef.current)
    }
  }, [streamChatClient, setStreamChatClient])

  const connectAnonymousUser = async () => {
    connectedAsRef.current = CONNECTED_CREDENTIALS_ENUM.anonymous
    await streamChatClientRef.current.connectAnonymousUser()
    setConnected(true)
    setConnecting(false)
  }

  const connectAuthenticatedUser = async () => {
    connectedAsRef.current = CONNECTED_CREDENTIALS_ENUM.authenticated
    await streamChatClientRef.current.connectUser({ id: user.id }, streamChatUserToken)
    setConnected(true)
    setConnecting(false)
  }

  useEffect(() => {
    const connectOrReconnect = async () => {
      let connect = false

      if (!user && (!connectedAsRef.current || connectedAsRef.current !== CONNECTED_CREDENTIALS_ENUM.anonymous)) {
        connect = true
        setConnected(false)
        setConnecting(true)

        if (connectedAsRef.current === CONNECTED_CREDENTIALS_ENUM.authenticated) {
          await streamChatClientRef.current.disconnectUser()
        }
      } else if (
        user &&
        streamChatUserToken &&
        (!connectedAsRef.current || connectedAsRef.current !== CONNECTED_CREDENTIALS_ENUM.authenticated)
      ) {
        connect = true
        setConnected(false)
        setConnecting(true)

        if (connectedAsRef.current === CONNECTED_CREDENTIALS_ENUM.anonymous) {
          await streamChatClientRef.current.disconnectUser()
        }
      }

      if (connect) {
        if (!user) {
          connectAnonymousUser()
        } else {
          connectAuthenticatedUser()
        }
      }
    }

    connectOrReconnect()
  }, [setConnecting, setConnected, streamChatClient, streamChatUserToken, user])

  useEffect(() => {
    if (!membership) {
      return
    }

    if (
      (!gettingForMembershipRef.current ||
        (membership && gettingForMembershipRef.current !== membership.id) ||
        gettingForUserRef.current !== user?.id) &&
      (membershipSetting?.visibility === VISIBILITY_ENUM.public ||
        (userMembership && userMembership.status === 'accepted'))
    ) {
      dispatch(getChannels({ init: true, membershipId: membership.id }))
      dispatch(
        getMembers({
          membershipId: membership.id,
          where: JSON.stringify({
            status: USER_STATUS_ENUM.active,
          }),
          take: 10000,
          skip: 0,
        }),
      )
      gettingForMembershipRef.current = membership.id
      gettingForUserRef.current = user?.id
    } else if ((gettingForMembershipRef.current && !membership) || (!user && gettingForUserRef.current)) {
      gettingForMembershipRef.current = null
      gettingForUserRef.current = null
    }
  }, [user, membership])

  useEffect(() => {
    // NOTE: We need to fetch the initial state for the inbox channels when the StreamChat client is ready (authenticated)
    if (!streamChatClient || !connected || !user) {
      return
    }

    const fetchInboxChannels = async () => {
      const filters = {
        members: { $in: [streamChatClient.userID] },
        type: CHANNEL_TYPE_ENUM.messaging,
      }
      const channels = await streamChatClient.queryChannels(filters)
      dispatch(setInboxChannels(channels))
    }

    const handleNewInboxMessage = async (event: any) => {
      if (event.channel_type !== 'messaging') return
      await fetchInboxChannels()
    }

    fetchInboxChannels()

    streamChatClient.on('message.new', handleNewInboxMessage)

    return () => {
      if (!streamChatClient) return
      streamChatClient.off('message.new', handleNewInboxMessage)
    }
  }, [streamChatClient, connected, dispatch, user])

  return (
    <div>
      <Chat
        client={streamChatClientRef.current}
        theme="messaging"
        customClasses={{
          chat: 'custom-chat-class',
          channel: 'custom-channel-class',
        }}
      >
        {children}
      </Chat>
    </div>
  )
}
