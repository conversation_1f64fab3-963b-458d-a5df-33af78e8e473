'use client'

import { createContext, useRef } from 'react'

import { createStore, InitialStoreState } from '@/store'

export const StoreContext = createContext<CreateStoreApi | undefined>(undefined)

export type CreateStoreApi = ReturnType<typeof createStore>

export const StoreProvider = ({
  initialData,
  children,
}: {
  initialData: Partial<InitialStoreState>
  children: React.ReactNode
}) => {
  const storeRef = useRef<CreateStoreApi>()

  if (!storeRef.current) {
    storeRef.current = createStore(initialData)
  }

  return <StoreContext.Provider value={storeRef.current}>{children}</StoreContext.Provider>
}
