'use client'

import { usePostHog } from 'posthog-js/react'
import { useEffect } from 'react'

import { useStore } from '@/hooks/useStore'
import { getFullName } from '@/lib/formatting'

export function PostHogUserDataProvider({ children }: { children: React.ReactNode }) {
  const posthog = usePostHog()
  const user = useStore((state) => state.auth.user)
  const userProfile = useStore((state) => state.auth.profile)

  useEffect(() => {
    if (user) {
      posthog.identify(user.id, {
        $set: {
          email: user.email,
          username: user.username,
          status: user.status,
          first_name: user.first_name,
          last_name: user.last_name,
          name: getFull<PERSON><PERSON>(user),
          updated_at:
            new Date(userProfile.updatedAt) > new Date(user.updatedAt) ? userProfile.updatedAt : user.updatedAt,
        },
        $set_once: {
          created_at: user.createdAt,
        },
      })
    }
  }, [posthog, user])

  return children
}
