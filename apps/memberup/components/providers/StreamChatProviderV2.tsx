'use client'

import { useEffect, useRef } from 'react'
import { StreamChat } from 'stream-chat'
import { Chat } from 'stream-chat-react'

import { GET_STREAM_APP_KEY } from '@memberup/shared/src/config/envs'
import { useStore } from '@/hooks/useStore'
import { getMembers } from '@/memberup/store/features/memberSlice'
import { useAppDispatch } from '@/memberup/store/store'
import { USER_STATUS_ENUM, VISIBILITY_ENUM } from '@/shared-types/enum'

export function StreamChatProviderV2({ children }: { children: React.ReactNode }): React.ReactNode {
  const dispatch = useAppDispatch()

  const user = useStore((state) => state.auth.user)
  const streamChatUserToken = useStore((state) => state.auth.streamChatUserToken)
  const setConnected = useStore((state) => state.streamChat.setConnected)
  const setConnecting = useStore((state) => state.streamChat.setConnecting)

  const membership = useStore((state) => state.community.membership)
  const membershipSetting = useStore((state) => state.community.membership?.membership_setting)
  const userMembership = useStore((state) =>
    membership ? state.auth.user?.user_memberships.find((um) => um.membership.id === membership.id) : null,
  )
  const gettingForMembershipRef = useRef(null)
  const gettingForUserRef = useRef(null)

  const getStreamChatClient = () => {
    let client = null

    try {
      client = StreamChat.getInstance(GET_STREAM_APP_KEY, {
        enableInsights: true,
        enableWSFallback: true,
        timeout: 10000,
      })

      return client
    } catch (error) {
      console.error('Error connecting anonymous user:', error)
    }

    return client
  }

  // TODO: remove useRef
  const chatClientRef = useRef<StreamChat | null>(getStreamChatClient())

  useEffect(() => {
    const connectAnonymousUser = async () => {
      await chatClientRef.current.connectAnonymousUser()
      setConnected(true)
      setConnecting(false)
    }

    const connectAuthenticatedUser = async () => {
      await chatClientRef.current.connectUser({ id: user.id }, streamChatUserToken)
      setConnected(true)
      setConnecting(false)
    }

    const connectOrReconnect = async () => {
      if (!chatClientRef.current) return

      let connect = false
      const initialized = Boolean(chatClientRef.current.user?.role)
      const isAnonymous = chatClientRef.current.user?.role === 'anonymous'

      if (!user && (!initialized || !isAnonymous)) {
        connect = true
        setConnected(false)
        setConnecting(true)

        if (initialized) {
          await chatClientRef.current.disconnectUser()
        }
      } else if (user && streamChatUserToken && (!initialized || (initialized && isAnonymous))) {
        connect = true
        setConnected(false)
        setConnecting(true)

        if (initialized) {
          await chatClientRef.current.disconnectUser()
        }
      }

      if (connect) {
        if (!user) {
          connectAnonymousUser()
        } else {
          connectAuthenticatedUser()
        }
      }
    }

    connectOrReconnect()
  }, [setConnecting, setConnected, streamChatUserToken, user])

  useEffect(() => {
    if (!membership) {
      return
    }

    if (
      (!gettingForMembershipRef.current ||
        (membership && gettingForMembershipRef.current !== membership.id) ||
        gettingForUserRef.current !== user?.id) &&
      (membershipSetting?.visibility === VISIBILITY_ENUM.public ||
        (userMembership && userMembership.status === 'accepted'))
    ) {
      dispatch(
        getMembers({
          membershipId: membership.id,
          where: JSON.stringify({
            status: USER_STATUS_ENUM.active,
          }),
          take: 10000,
          skip: 0,
        }),
      )
      gettingForMembershipRef.current = membership.id
      gettingForUserRef.current = user?.id
    } else if ((gettingForMembershipRef.current && !membership) || (!user && gettingForUserRef.current)) {
      gettingForMembershipRef.current = null
      gettingForUserRef.current = null
    }
  }, [user, membership])
  /*

  useEffect(() => {
    // NOTE: We need to fetch the initial state for the inbox channels when the StreamChat client is ready (authenticated)
    if (!streamChatClient || !connected || !user) {
      return
    }

    const fetchInboxChannels = async () => {
      const filters = {
        members: { $in: [streamChatClient.userID] },
        type: CHANNEL_TYPE_ENUM.messaging,
      }
      const channels = await streamChatClient.queryChannels(filters)
      dispatch(setInboxChannels(channels))
    }

    const handleNewInboxMessage = async (event: any) => {
      if (event.channel_type !== 'messaging') return
      await fetchInboxChannels()
    }

    fetchInboxChannels()

    streamChatClient.on('message.new', handleNewInboxMessage)

    return () => {
      if (!streamChatClient) return
      streamChatClient.off('message.new', handleNewInboxMessage)
    }
  }, [streamChatClient, connected, dispatch, user])
  */

  return (
    <div>
      <Chat
        client={chatClientRef.current}
        theme="messaging"
        customClasses={{
          chat: 'custom-chat-class',
          channel: 'custom-channel-class',
        }}
      >
        {children}
      </Chat>
    </div>
  )
}
