export function Spark20Icon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M 3.8505578,10.784498 12.996951,0.9759528 c 0.215852,-0.2314771 0.578459,-0.2441471 0.80993,-0.028295 0.160313,0.1494847 0.22135,0.3773196 0.157188,0.5869054 l -2.085737,6.8123367 h 3.851976 c 0.3165,0 0.573086,0.2565859 0.573086,0.5730849 0,0.1466299 -0.05623,0.2876994 -0.157126,0.3941572 L 6.9414101,19.027424 C 6.7236839,19.257145 6.3610143,19.266891 6.1312305,19.049163 5.9726684,18.898914 5.913317,18.671567 5.9782284,18.462962 l 2.089174,-6.714531 H 4.269692 c -0.316506,0 -0.5730858,-0.256586 -0.5730858,-0.573086 0,-0.145068 0.055015,-0.284763 0.1539516,-0.390847 z"
        fill="url(#paint0_linear_9960_7906)"
        id="path1"
        style={{
          fill: 'url(#paint0_linear_9960_7906)',
          strokeWidth: '0.624753',
        }}
      />
      <defs id="defs2">
        <linearGradient
          id="paint0_linear_9960_7906"
          x1="22.4158"
          y1="5.1526299"
          x2="13.881"
          y2="32.8433"
          gradientUnits="userSpaceOnUse"
          gradientTransform="matrix(0.62475282,0,0,0.62475282,-1.3610554,-1.7962128)"
        >
          <stop stopColor="#51C5E2" id="stop1" />
          <stop offset="1" stopColor="#D053FF" id="stop2" />
        </linearGradient>
      </defs>
    </svg>
  )
}
