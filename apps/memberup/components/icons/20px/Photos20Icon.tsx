export function Photos20Icon(props: any) {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" version="1.1" {...props}>
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1304, -26)" fill="currentColor">
          <g transform="translate(1292, 16)">
            <g transform="translate(12, 10)">
              <g transform="translate(2, 2)">
                <path d="M10,3 C11.6568542,3 13,4.34314575 13,6 L13,13 C13,14.6568542 11.6568542,16 10,16 L3,16 C1.34314575,16 0,14.6568542 0,13 L0,6 C0,4.34314575 1.34314575,3 3,3 L10,3 Z M10,5 L3,5 C2.44771525,5 2,5.44771525 2,6 L2,12.697 L4.18626653,9.41876181 C4.50017051,8.97929623 5.09949173,8.87371288 5.54124102,9.158983 L5.6401844,9.23177872 L8.78525685,13.5193391 L11,11 L11,6 C11,5.44771525 10.5522847,5 10,5 Z M13,0 C14.5976809,0 15.9036609,1.24891996 15.9949073,2.82372721 L16,3 L16,10 C16,10.5522847 15.5522847,11 15,11 C14.4871642,11 14.0644928,10.6139598 14.0067277,10.1166211 L14,10 L14,3 C14,2.48716416 13.6139598,2.06449284 13.1166211,2.00672773 L13,2 L6,2 C5.44771525,2 5,1.55228475 5,1 C5,0.487164161 5.38604019,0.0644928393 5.88337887,0.00672773133 L6,0 L13,0 Z"></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}
