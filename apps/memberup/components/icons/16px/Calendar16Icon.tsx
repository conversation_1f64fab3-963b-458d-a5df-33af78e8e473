export function Calendar16Icon(props: any) {
  return (
    <svg width="16" height="16" fill="none" viewBox="0 0 16 16" {...props}>
      <g transform="translate(-0.98503,-0.27342)">
        <path
          d="m 5.31836,13.2734 c -1.10457,0 -2,-0.8954 -2,-2 V 6.60677 c 0,-1.10457 0.89543,-2 2,-2 h 7.33334 c 1.1046,0 2,0.89543 2,2 v 4.66663 c 0,1.1046 -0.8954,2 -2,2 z"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinejoin="round"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M 3.65234,8.27344 V 6.9401 H 14.319 v 1.33334 z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="m 5.65202,4.27344 c -0.36819,0 -0.66667,-0.29848 -0.66667,-0.66667 V 2.9401 c 0,-0.36819 0.29848,-0.66666 0.66667,-0.66666 0.36819,0 0.66666,0.29847 0.66666,0.66666 v 0.66667 c 0,0.36819 -0.29847,0.66667 -0.66666,0.66667 z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="m 12.319,4.27344 c -0.3682,0 -0.6667,-0.29848 -0.6667,-0.66667 V 2.9401 c 0,-0.36819 0.2985,-0.66666 0.6667,-0.66666 0.3682,0 0.6667,0.29847 0.6667,0.66666 v 0.66667 c 0,0.36819 -0.2985,0.66667 -0.6667,0.66667 z"
          fill="currentColor"
        />
      </g>
    </svg>
  )
}
