import { CheckedState } from '@radix-ui/react-checkbox'
import * as VisuallyHidden from '@radix-ui/react-visually-hidden'
import { Elements, PaymentElement, useElements, useStripe } from '@stripe/react-stripe-js'
import { loadStripe, PaymentMethod } from '@stripe/stripe-js'
import { useEffect, useMemo, useState } from 'react'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { STRIPE_PUBLISH_KEY } from '@memberup/shared/src/config/envs'
import {
  createStripeSetupIntentApi,
  setStripeDefaultPaymentMethodApi,
} from '@memberup/shared/src/services/apis/stripe.api'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { Checkbox, SkeletonBox } from '@/components/ui'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogInner,
  DialogTitle,
} from '@/components/ui/dialog'
import { toast } from '@/components/ui/sonner'
import { cn } from '@/lib/utils'
import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { mergeUserProfile } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

function PaymentMethodForm({
  paymentMethods,
  onSuccess,
  onCancel,
  saving,
  setSaving,
  setStripeLoading,
  updateDefault,
  setUpdateDefault,
}: {
  paymentMethods: any[]
  onSuccess: (paymentMethod: PaymentMethod) => Promise<void>
  onCancel: () => void
  saving: boolean
  setSaving: (value: boolean) => void
  setStripeLoading: (value: boolean) => void
  updateDefault: boolean
  setUpdateDefault: (value: boolean) => void
}) {
  const elements = useElements()
  const stripe = useStripe()
  const mountedRef = useMounted(true)
  const [stripeReady, setStripeReady] = useState(false)

  const onSubmit = async (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault()
    setSaving(true)

    try {
      const { setupIntent, error } = await stripe.confirmSetup({
        elements,
        redirect: 'if_required',
        confirmParams: {
          expand: ['payment_method'],
          return_url: window.location.href,
        },
      })

      if (error) {
        toast.error(error.message || 'An unexpected error occurred.')
        setSaving(false)
      } else {
        if (mountedRef.current) {
          const paymentMethod = setupIntent?.payment_method as PaymentMethod

          if (paymentMethod) {
            await onSuccess(paymentMethod)
            setSaving(false)
          }
        }
      }
    } catch (err: any) {
      if (err?.message) {
        toast.error(err.message)
      }
      setSaving(false)
    }
  }

  const onPaymentElementLoaderStart = () => {
    setStripeLoading(false)
  }

  return (
    <>
      <DialogInner>
        <div className="relative z-10">
          <PaymentElement
            options={{ readOnly: saving }}
            onLoaderStart={onPaymentElementLoaderStart}
            onReady={() => setStripeReady(true)}
          />
        </div>
      </DialogInner>
      <div className="px-8">
        {stripeReady && (
          <>
            <div className={cn('flex items-center space-x-2')}>
              {paymentMethods && paymentMethods.length > 0 && (
                <div className="flex pb-8">
                  <Checkbox
                    id="set_as_default"
                    checked={updateDefault}
                    onCheckedChange={(checked: CheckedState) => setUpdateDefault(checked === true)}
                  />
                  <label
                    htmlFor="set_as_default"
                    className="cursor-pointer pl-1 text-sm font-medium leading-5 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Set as default
                  </label>
                </div>
              )}
            </div>
          </>
        )}
      </div>
      <DialogFooter>
        <Button
          className="w-full sm:w-32"
          disabled={saving}
          onClick={(e) => {
            e.preventDefault()
            onCancel()
          }}
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          className="w-full sm:w-32"
          disabled={saving}
          loading={saving}
          type="submit"
          variant="default"
          onClick={onSubmit}
        >
          Save
        </Button>
      </DialogFooter>
    </>
  )
}

export function AddPaymentMethodDialog({
  onAddPaymentMethod,
  open,
  setOpen,
  paymentMethods,
}: {
  onAddPaymentMethod: (paymentMethod: PaymentMethod) => void
  open: boolean
  setOpen: (value: boolean) => void
  paymentMethods: any[]
}) {
  const dispatch = useAppDispatch()
  const membershipSetting = useAppSelector(selectMembershipSetting)
  const mountedRef = useMounted(true)
  const [clientSecret, setClientSecret] = useState(null)
  const [saving, setSaving] = useState(false)
  const [stripeLoading, setStripeLoading] = useState(true)
  const [updateDefault, setUpdateDefault] = useState(true)

  useEffect(() => {
    setStripeLoading(true)
    const fetchPaymentIntent = async () => {
      const response = await createStripeSetupIntentApi(false, {
        mode: 'setup',
        payment_method_types: ['card'],
      })

      if (mountedRef.current) setClientSecret(response.data.data?.['client_secret'])
    }

    if (open) {
      setClientSecret(null)
      fetchPaymentIntent()
    }
  }, [open])

  const stripePromise = useMemo(() => {
    return loadStripe(STRIPE_PUBLISH_KEY)
  }, [membershipSetting?.stripe_connect_account?.stripe_publishable_key])

  const onSuccess = async (paymentMethod: PaymentMethod) => {
    if (paymentMethod) {
      try {
        if (updateDefault || !paymentMethods || paymentMethods.length === 0) {
          await setStripeDefaultPaymentMethodApi(true, paymentMethod.id)
          dispatch(mergeUserProfile({ stripe_payment_method_id: paymentMethod.id }))
        }

        onAddPaymentMethod(paymentMethod)

        setOpen(false)
        toast.success('Payment method added successfully.')
      } catch (err) {
        toast.error(err.response?.message || 'An unexpected error occurred.')
      }
    }
  }

  return (
    <Dialog open={open} onOpenChange={(value) => value !== open && setOpen(value)}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Payment Method</DialogTitle>
          <VisuallyHidden.Root>
            <DialogDescription>Add a payment method to your account</DialogDescription>
          </VisuallyHidden.Root>
        </DialogHeader>
        {stripeLoading && (
          <DialogInner>
            <SkeletonBox />
          </DialogInner>
        )}
        {clientSecret && stripePromise && (
          <Elements
            stripe={stripePromise}
            options={{
              clientSecret: clientSecret,
              appearance: {
                theme: membershipSetting?.theme_mode === THEME_MODE_ENUM.dark ? 'night' : 'stripe',
              },
              loader: 'always',
            }}
          >
            <PaymentMethodForm
              paymentMethods={paymentMethods}
              onSuccess={onSuccess}
              onCancel={() => setOpen(false)}
              saving={saving}
              setSaving={setSaving}
              setStripeLoading={setStripeLoading}
              updateDefault={updateDefault}
              setUpdateDefault={setUpdateDefault}
            />
          </Elements>
        )}
      </DialogContent>
    </Dialog>
  )
}
