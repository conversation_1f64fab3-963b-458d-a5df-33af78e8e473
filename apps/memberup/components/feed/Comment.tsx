import { useMemo, useState } from 'react'

import { getDateTimeFromNow } from '@memberup/shared/src/libs/date-utils'
import { ProfilePicture } from '@/components/images/profile-picture'
import { Tooltip, TooltipContent, TooltipPortal, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { removeHtmlAndAtMentions } from '@/lib/parsing'
import { formatDateLong } from '@/shared-libs/date-utils'

export default function Comment({ comment }: { comment: any }) {
  const commentText = useMemo(() => {
    return removeHtmlAndAtMentions(comment.text)
  }, [comment])
  const [dateTooltipOpen, setDateTooltipOpen] = useState(false)

  const commentDate = useMemo(() => getDateTimeFromNow(new Date(comment.created_at).getTime()), [comment])

  const commentDateLong = useMemo(() => formatDateLong(new Date(comment.created_at)), [comment])

  return (
    <div className="flex">
      <div className="mr-3">
        <div className="flex h-10 w-10 items-center justify-center overflow-hidden rounded-full">
          <ProfilePicture
            src={comment.user.image}
            cropArea={comment.user.image_crop_area}
            alt={comment.user.name}
            height={40}
            width={40}
          />
        </div>
      </div>
      <div className="grow rounded-lg bg-grey-100 p-4 dark:bg-grey-800">
        <div className="dark:font-white-500 mb-1 text-nowrap text-sm font-semibold">
          <span>{comment.user.name}</span>
          <span className="ml-3 inline-block text-xs font-normal text-black-100">
            {comment.reaction_counts.like &&
              `${comment.reaction_counts.like} like${comment.reaction_counts.like > 1 ? 's' : ''} • `}
            <TooltipProvider delayDuration={700}>
              <Tooltip open={dateTooltipOpen} onOpenChange={setDateTooltipOpen}>
                <TooltipTrigger
                  asChild
                  // Workaround from: https://github.com/radix-ui/primitives/issues/955#issuecomment-**********
                  onClick={(e) => {
                    e.stopPropagation()
                    setDateTooltipOpen((prevOpen) => !prevOpen)
                  }}
                  // Timeout runs setOpen after onOpenChange to prevent bug on mobile
                  onFocus={() => setTimeout(() => setDateTooltipOpen(true), 0)}
                  onBlur={() => setDateTooltipOpen(false)}
                >
                  <span className="text-xs text-black-200 dark:text-black-100">{commentDate}</span>
                </TooltipTrigger>
                <TooltipPortal>
                  <TooltipContent side="bottom">{commentDateLong}</TooltipContent>
                </TooltipPortal>
              </Tooltip>
            </TooltipProvider>
          </span>
        </div>
        <div className="dark:font-white-200 text-ssm text-black-600 dark:text-white-200">{commentText}</div>
      </div>
    </div>
  )
}
