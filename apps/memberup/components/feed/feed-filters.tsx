'use client'

import { useRouter } from 'next/navigation'

import { RadioDropdown } from '../ui/radio-dropdown'
import { SkeletonBox } from '@/components/ui/skeleton'
import { useStore } from '@/hooks/useStore'
import { getCommunityBaseURL } from '@/src/libs/utils'

const viewOptions = [
  { label: 'View All', value: 'all' },
  { label: 'Unread', value: 'unread' },
]

const orderByOptions = [
  { label: 'Activity', value: 'activity' },
  { label: 'Newest', value: 'newest' },
]

export function FeedFilters({ onSortByChange, onSpaceChange, sortBy, spaceConfig, disabled }) {
  const membership = useStore((state) => state.community.membership)
  if (!membership || !spaceConfig)
    return (
      <div className="my-4 flex">
        <SkeletonBox className="mr-2 h-10 w-20" />
        <SkeletonBox className="h-10 w-20" />
      </div>
    )

  const spaces = membership?.channels
  const currentSpace = spaces.find((s) => s.id === spaceConfig)
  const sortLabel = `Sort: ${orderByOptions.find((opt) => opt.value === sortBy)?.label}`
  const spaceLabel = `Space: ${spaceConfig === 'community' ? 'All spaces' : currentSpace?.name}`

  // const onSpaceChange = (spaceId:string) => {
  //   const selectedSpace = membership.channels.find(c => c.id === spaceId) || {id: 'community', name: 'Community', slug: 'community'}
  //   //console.log('onSpaceChange', selectedSpace)
  //   //router.replace(getCommunityBaseURL(membership) + `?space=${selectedSpace.slug}`)
  // }

  const spacesListing = [
    {
      value: 'community',
      label: 'All spaces',
    },
    ...(spaces?.map((space) => ({
      label: space.name,
      value: space.id,
    })) || []),
  ]

  return (
    <div className="my-4 flex flex-col gap-4 sm:flex-row">
      {/*<RadioDropdown className="mr-4" label={filterLabel} items={viewOptions} onChange={() => {}} value={null} />*/}

      {!sortBy && <SkeletonBox className="mr-2 h-10 w-20" />}
      {sortBy && (
        <RadioDropdown
          label={sortLabel}
          items={orderByOptions}
          onValueChange={onSortByChange}
          value={sortBy}
          disabled={disabled}
        />
      )}
      <RadioDropdown
        label={spaceLabel}
        items={spacesListing}
        onValueChange={onSpaceChange}
        value={spaceConfig}
        disabled={disabled}
      />
    </div>
  )
}
