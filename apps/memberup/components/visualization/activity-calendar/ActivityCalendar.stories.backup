import type { Meta, StoryObj } from '@storybook/react'

import { ActivityCalendarData } from './types'
import { ActivityCalendar } from './ActivityCalendar'
import demoData from './activity_calendar_demo_data.json'
import { IUser } from '@/shared-types/interfaces'

const meta: Meta<typeof ActivityCalendar> = {
  title: 'Visualization/ActivityCalendar',
  component: ActivityCalendar,
  parameters: {
    layout: 'centered',
  },
}

export default meta

type Story = StoryObj<typeof meta>

const ActivityCalendarTemplate: Story = {
  render: () => {
    const newDate = new Date()
    newDate.setMonth(newDate.getMonth() - 9)

    return <ActivityCalendar
      data={demoData as unknown as ActivityCalendarData}
      user={{ createdAt: newDate.toISOString() } as unknown as IUser}
    />
  },
}

export const Default: Story = {
  ...ActivityCalendarTemplate,
}
