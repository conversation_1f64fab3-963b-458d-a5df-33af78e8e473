import { Meta, StoryObj } from '@storybook/react'

import { Badge } from './Badge'

const meta: Meta<typeof Badge> = {
  title: 'ui/Badge',
  component: Badge,
  parameters: {
    layout: 'centered',
  },
}
export default meta

type Story = StoryObj<typeof Badge>

export const Default: Story = {
  render: () => <Badge>Badge text</Badge>,
}

export const Secondary: Story = {
  render: () => <Badge variant="secondary">Badge text</Badge>,
}
