import { cn } from '@/lib/utils'

export function Pill({ children, className, ...props }: { children: React.ReactNode; className?: string }) {
  return (
    <div className={cn('rounded-full bg-gradient-to-r from-primary-200 to-primary-400 p-px', className)} {...props}>
      <div className="flex select-none items-center rounded-full bg-black-600 px-4 px-6 py-2 text-sm font-medium text-black-100">
        {children}
      </div>
    </div>
  )
}
