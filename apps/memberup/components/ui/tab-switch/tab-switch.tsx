import { cn } from '@/lib/utils'

export function TabSwitch({
  className,
  onChange,
  options,
  value,
}: {
  className?: string
  onChange: (value: string) => void
  options: {
    label: string
    value: string
  }[]
  value: string
}) {
  return (
    <div
      className={cn(
        'flex space-x-[0.1875rem] rounded-base border border-black-700 bg-black-100/[0.08] p-[0.1875rem]',
        className,
      )}
    >
      {options.map((option) => (
        <div
          key={option.value}
          className={cn(
            'flex h-[2.125rem] w-full cursor-pointer select-none items-center justify-center rounded-lg text-xs font-medium transition-colors',
            value === option.value ? 'bg-black-700 text-white-500' : 'text-black-100 hover:bg-black-700/80',
          )}
          onClick={() => onChange(option.value)}
        >
          <span>{option.label}</span>
        </div>
      ))}
    </div>
  )
}
