import { useRef, useState } from 'react'

import { ConfirmModal } from '@/components/ui/confirm-modal'
import { TiptapEditorField } from '@/components/ui/tiptap/editor-field'
import { cn } from '@/lib/utils'

export default function InlineEditorField({
  characterLimit,
  className,
  editing,
  label,
  placeholder,
  setEditing,
  setValue,
  value,
}: {
  characterLimit?: number
  className?: string
  editing: boolean
  label?: string
  placeholder?: string
  setEditing: (editing: boolean) => void
  setValue: (value: string) => void
  value: string
}) {
  const preEditingValueRef = useRef(value)
  const [discardChangesWarningOpen, setDiscardChangesWarningOpen] = useState(false)

  if (!editing) {
    return (
      <div
        className={cn('cursor-pencil', className)}
        onClick={() => {
          preEditingValueRef.current = value
          setEditing(true)
        }}
      >
        {value ? (
          <div
            className="rendered-editor-text"
            dangerouslySetInnerHTML={{
              __html: value,
            }}
          />
        ) : (
          <div className="rendered-editor-text">{placeholder}</div>
        )}
      </div>
    )
  }

  return (
    <div className={cn(className, 'mt-[0.625rem]')}>
      <TiptapEditorField
        characterLimit={characterLimit}
        initialValue={value}
        onUpdate={({ editor }) => {
          setValue(editor.getHTML())
        }}
        placeholder={label}
      />
      <ConfirmModal
        title="Are you sure you want to discard changes?"
        onConfirm={() => {
          setValue(preEditingValueRef.current)
          setDiscardChangesWarningOpen(false)
          setEditing(false)
        }}
        open={discardChangesWarningOpen}
        onClose={() => setDiscardChangesWarningOpen(false)}
      />
    </div>
  )
}
