import Image from 'next/image'

import { AspectRatio } from '@/components/ui'
import { Carousel, CarouselContent, CarouselItem } from '@/components/ui/carousel'

export function FixedMediaGallery({ media, thumbnails }: { media: string[]; thumbnails: any[] }) {
  return (
    <div className="fixed-media-gallery">
      {/*
      <div className="p-5">
        <Carousel>
          <CarouselContent>
            {media.map((media, index) => (
              <CarouselItem key={index}>
                {media.id}
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
      */}
      <div>
        {thumbnails.map((thumbnail, index) => (
          <div key={index}>{thumbnail}</div>
        ))}
      </div>
    </div>
  )
}
