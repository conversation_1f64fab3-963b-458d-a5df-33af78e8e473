import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'

export const mockAuthentication = (authenticationMiddleware, memberMock) => {
  authenticationMiddleware.mockImplementation((req, res, next) => {
    req['user'] = memberMock
    next()
  })
}

export const memberMock = {
  current_membership_id: 1,
  role: USER_ROLE_ENUM.member,
}

export const adminMock = {
  current_membership_id: 1,
  role: USER_ROLE_ENUM.admin,
}
