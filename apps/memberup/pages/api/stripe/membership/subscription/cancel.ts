import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUserById } from '@memberup/shared/src/libs/prisma/user'
import { stripeCancelSubscription } from '@memberup/shared/src/libs/stripe'
import { IUser } from '@memberup/shared/src/types/interfaces'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

// import { errorHandler } from '@/lib/db/db-error-handler'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  // Delete the subscription
  try {
    const user = req['user']
    const dbUser: IUser = await findUserById({
      where: { id: user.id },
      include: {
        membership: {
          include: {
            membership_setting: true,
          },
        },
      },
    })
    const membership = dbUser.membership
    const membershipSetting = membership.membership_setting
    const stripeToken = membershipSetting?.stripe_connect_account?.access_token
    if (!stripeToken) {
      return res.status(400).json({
        message: 'Community is not configured properly. Please contact the owner.',
      })
    }

    const connectedStripeAccountInfo = membershipSetting?.stripe_connect_account
    if (!connectedStripeAccountInfo) {
      return res.status(400).json({
        message: `Community didn't setup stripe. Please contact the owner.`,
      })
    }

    const result = await stripeCancelSubscription(connectedStripeAccountInfo, req.body.subscriptionId, false)

    return res.status(200).send({ success: true, data: result })
  } catch (err: any) {
    res.status(400).json({ message: err.message })
  }
})

export default handler
