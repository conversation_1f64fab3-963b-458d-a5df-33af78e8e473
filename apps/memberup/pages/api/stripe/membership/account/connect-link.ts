import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { updateMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status500 } from '@/shared-libs/api-utils'
import prisma from '@/shared-libs/prisma/prisma'
import { stripeCreateAccount, stripeCreateAccountLink, stripeGetAccount } from '@/shared-libs/stripe'
import checkCreatorRole from '@/src/middlewares/check-creator-role'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRole)
  .get(async (req, res) => {
    const user = req['user']
    const membershipId = req.query.membership_id

    const membership = await prisma.membership.findUnique({
      where: {
        id: membershipId as string,
      },
      include: {
        membership_setting: true,
      },
    })

    if (!membership) {
      return status400(res, 'Membership not found')
    }

    const membershipSetting = membership.membership_setting

    const userMembership = await prisma.userMembership.findUnique({
      where: {
        user_id_membership_id: {
          user_id: user.id,
          membership_id: membershipId as string,
        },
      },
    })

    if (!userMembership) {
      return status400(res, 'User is not a member of this community')
    }

    if (userMembership.user_role !== USER_ROLE_ENUM.admin && userMembership.user_role !== USER_ROLE_ENUM.owner) {
      return status400(res, 'You are not allowed to access this page.')
    }

    try {
      const host = req.headers.host
      let protocol = host.includes('localhost:3000') ? 'http://' : 'https://'
      const refreshUrl = `${protocol}${host}/${membership.slug}?settings=pricing&message_key=EXPIRED`
      const redirectUrl = `${protocol}${host}/${membership.slug}?settings=pricing&message_key=CONNECTED`

      let stripeAccountId = membershipSetting.stripe_connect_account?.stripe_user_id

      if (!stripeAccountId) {
        const account = await stripeCreateAccount()
        stripeAccountId = account.id

        // NOTE: We just create the account but we don't enable yet until the user completes all the verification steps.
        await updateMembershipSetting({
          where: { id: membershipSetting.id },
          data: {
            stripe_connect_account: {
              stripe_user_id: stripeAccountId,
            },
          },
        })
      }

      const account_link = await stripeCreateAccountLink(stripeAccountId, refreshUrl, redirectUrl)
      return status200(res, { account_link })
    } catch (err) {
      Sentry.captureException(err)
      console.error(err)
      return status500(res, err.message)
    }
  })

export default handler
