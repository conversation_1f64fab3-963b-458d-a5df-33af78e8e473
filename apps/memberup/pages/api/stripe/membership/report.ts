import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeReportRuns } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const user = req['user']
    const { report_type, interval_start, interval_end } = req.query
    const stripeConnectedAccount = user.membership_setting?.stripe_connect_account

    if (!stripeConnectedAccount) {
      return res.status(400).json({
        message: 'Community is not configured properly. Please contact the owner.',
      })
    }
    const result = await stripeReportRuns(
      stripeConnectedAccount,
      report_type as string,
      parseInt(interval_start as string),
      parseInt(interval_end as string),
      //   accountId ? { stripeAccount: accountId as string } : undefined
    )
    res.status(200).send({ success: true, data: result })
  } catch (err: any) {
    res.status(400).json({ message: err.message })
  }
})

export default handler
