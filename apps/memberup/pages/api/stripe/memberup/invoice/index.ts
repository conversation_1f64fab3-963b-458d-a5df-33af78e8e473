import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import Stripe from 'stripe'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status500 } from '@/shared-libs/api-utils'
import { stripeGetInvoices } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { created_gte, created_lt, limit, starting_after } = req.body
    const stripeCustomerId = user.profile.stripe_customer_id

    if (!stripeCustomerId) {
      return status500(res, 'Stripe customer not found. Please contact support.')
    }

    const params: Stripe.InvoiceListParams = {
      customer: stripeCustomerId,
      limit: limit ? parseInt(limit as string) : 100,
      starting_after: starting_after as string,
      expand: ['data.charge'],
    }

    if (created_gte || created_lt) {
      params['created'] = {
        gte: created_gte,
        lt: created_lt,
      }
    }
    const result = await stripeGetInvoices(params)
    return status200(res, result)
  } catch (err: any) {
    res.status(400).json({ message: err.message })
  }
})

export default handler
