import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { IUser } from '@memberup/shared/src/types/interfaces'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { updateUserProfile } from '@/shared-libs/prisma/user-profile'
import { stripeGetCustomers, stripeUpdateCustomer, stripeUpdateMembershipSubscription } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']

    const stripeConnectAccount = user.membership_setting?.stripe_connect_account

    if (!stripeConnectAccount) {
      return res.status(200).send({ success: true, data: [] })
    }

    const dbUser: IUser = await findUser({ where: { id: user.id }, include: { profile: true } })
    const userProfile = dbUser.profile

    if (!userProfile?.stripe_customer_id) {
      return res.status(200).send({ success: true, data: [] })
    }

    const paymentMethodId = req.query.paymentMethodId as string

    await stripeUpdateCustomer(stripeConnectAccount, userProfile.stripe_customer_id, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    })

    if (userProfile.stripe_subscription_id) {
      await stripeUpdateMembershipSubscription(stripeConnectAccount, userProfile.stripe_subscription_id, {
        default_payment_method: paymentMethodId,
      })
    }

    await updateUserProfile({
      where: {
        id: userProfile.id,
      },
      data: {
        stripe_payment_method_id: paymentMethodId,
      },
    })

    res.status(200).send({ success: true })
  } catch (err: any) {
    console.error(err)
    res.status(400).json({ message: err.message })
  }
})

export default handler
