import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import Stripe from 'stripe'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status500 } from '@/shared-libs/api-utils'
import { findUserProfile } from '@/shared-libs/prisma/user-profile'
import { stripeGetCouponMain, stripeGetInvoices } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { id } = req.query
    // TODO: We will need to use promotional codes or coupons.
    const result = await stripeGetCouponMain(STRIPE_SECRET_KEY, id as string)
    if (!result) {
      return status400(res, 'Coupon not found.')
    }

    const user = req['user']
    const userProfile = await findUserProfile({
      where: { user_id: user.id },
    })

    const stripeCustomerId = userProfile.stripe_customer_id
    if (!stripeCustomerId) {
      return status400(res, 'Stripe user does not exists')
    }

    // Check if the coupon was already used by the user
    const params: Stripe.InvoiceListParams = {
      customer: stripeCustomerId,
      limit: 100,
    }
    const invoicesResponse = await stripeGetInvoices(params)
    const invoices = invoicesResponse.data
    const hasBeenUsed = invoices.some((i) => i.discount?.coupon?.id === id)
    if (hasBeenUsed) {
      return status400(res, 'Coupon already used.')
    }
    return status200(res, result)
  } catch (err: any) {
    console.error(err)
    Sentry.captureException(err)
    return status500(res, err.message)
  }
})

export default handler
