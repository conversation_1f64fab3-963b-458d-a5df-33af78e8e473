import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import {
  STRIPE_INTERNAL_DISCOUNT_ID,
  STRIPE_PRODUCT_IDS_MAP,
  STRIPE_SECRET_KEY,
} from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import {
  stripeConfirmPaymentIntent,
  stripeCreatePaymentIntentMain,
  stripeGetInvoices,
  stripeGetPricesMain,
  stripeRetrieveSubscriptionMain,
  stripeUpdateSubscription,
} from '@/shared-libs/stripe'
import { MEMBERUP_PLANS } from '@/shared-settings/plans'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  // Upgrade the subscription
  try {
    const user = req['user']
    const { plan, interval, proration_date, plan_price, promotion_code } = req.body
    const stripeCustomerId = user.membership_setting?.stripe_customer_id
    const stripeSubscriptionId = user.membership_setting?.stripe_subscription_id
    const stripePaymentMethodId = user.membership_setting?.stripe_payment_method_id

    if (!stripeSubscriptionId) {
      return res.status(400).json({
        message: `Membership doesn't have subscribed any paid plan.`,
      })
    }

    if (!stripeCustomerId) {
      return res.status(400).json({
        message: `Membership doesn't have stripe setup. Please contact the support`,
      })
    }

    const currentSubscription = await stripeRetrieveSubscriptionMain(STRIPE_SECRET_KEY, stripeSubscriptionId)

    const productId = STRIPE_PRODUCT_IDS_MAP[plan]

    const existingStripePrices = await stripeGetPricesMain(STRIPE_SECRET_KEY, {
      active: true,
      product: productId,
      recurring: { interval },
    })

    const stripePrice = existingStripePrices?.data?.find((p) => p.unit_amount === plan_price * 100)
    if (!stripePrice?.id)
      return res.status(400).json({ message: `There is no active stripe price. Please ask admin about this.` })

    // See what the next invoice would look like with a price switch
    // and proration set:
    const items = [
      {
        id: currentSubscription.items.data[0].id,
        price: stripePrice.id, // Switch to new price
      },
    ]

    const memberUpPlan = MEMBERUP_PLANS.find((p) => p.name === plan && !p.promotional && p.annualPrice === plan_price)

    //let amount = memberUpPlan.annualPrice * 100
    // if (memberUpPlan.prorated) {
    //   // Calculate the proration manually
    //   const oldPrice = currentSubscription.items.data[0].price.unit_amount
    //   const newPrice = stripePrice.unit_amount
    //
    //   console.log('old', oldPrice, 'new', newPrice)
    //
    //   const creditForUnusedTime =
    //     ((currentSubscription.current_period_end - proration_date) /
    //       (currentSubscription.current_period_end - currentSubscription.current_period_start)) *
    //     oldPrice
    //   const costForNewPlan =
    //     ((currentSubscription.current_period_end - proration_date) /
    //       (currentSubscription.current_period_end - currentSubscription.current_period_start)) *
    //     newPrice
    //   amount = costForNewPlan - creditForUnusedTime
    // }
    //
    // console.log('amount', amount)

    // const paymentIntent = await stripeCreatePaymentIntentMain(STRIPE_SECRET_KEY, {
    //   amount: Math.floor(amount),
    //   customer: stripeCustomerId,
    //   currency: 'usd',
    //   metadata: {
    //     subscription: stripeSubscriptionId,
    //     discount: memberUpPlan.prorated
    //       ? user.membership_setting?.stripe_subscription_discount_id
    //       : undefined,
    //   },
    //   automatic_payment_methods: {
    //     enabled: true,
    //     allow_redirects: 'never',
    //   },
    // })
    //
    // await stripeConfirmPaymentIntent(STRIPE_SECRET_KEY, paymentIntent.id, {
    //   payment_method: stripePaymentMethodId,
    // })

    // if (user.membership_setting?.stripe_subscription_discount_id) {
    //   // Calculate the proration manually
    //   const oldPrice = currentSubscription.items.data[0].price.unit_amount
    //   const newPrice = stripePrice.unit_amount
    //   const creditForUnusedTime =
    //     ((currentSubscription.current_period_end - proration_date) /
    //       (currentSubscription.current_period_end - currentSubscription.current_period_start)) *
    //     oldPrice
    //   const costForNewPlan =
    //     ((currentSubscription.current_period_end - proration_date) /
    //       (currentSubscription.current_period_end - currentSubscription.current_period_start)) *
    //     newPrice
    //   const proratedAmount = costForNewPlan - creditForUnusedTime
    //
    //   const paymentIntent = await stripeCreatePaymentIntent(STRIPE_SECRET_KEY, {
    //     amount: Math.floor(proratedAmount),
    //     customer: stripeCustomerId,
    //     currency: 'usd',
    //     metadata: {
    //       subscription: stripeSubscriptionId,
    //       discount: user.membership_setting?.stripe_subscription_discount_id,
    //     },
    //     automatic_payment_methods: {
    //       enabled: true,
    //       allow_redirects: 'never',
    //     },
    //   })
    //
    //   await stripeConfirmPaymentIntent(STRIPE_SECRET_KEY, paymentIntent.id, {
    //     payment_method: stripePaymentMethodId,
    //   })
    // }

    const updatedSubscription = await stripeUpdateSubscription(STRIPE_SECRET_KEY, stripeSubscriptionId, {
      cancel_at_period_end: false,
      proration_behavior: memberUpPlan.prorated ? 'always_invoice' : 'none',
      items,
      default_payment_method: stripePaymentMethodId,
      proration_date,
      promotion_code: promotion_code,
      // coupon: memberUpPlan.prorated
      //   ? user.membership_setting?.stripe_subscription_discount_id
      //     ? STRIPE_INTERNAL_DISCOUNT_ID
      //     : undefined
      //   : undefined,
    })

    res.status(200).send({ success: true, data: updatedSubscription })
  } catch (err: any) {
    console.log(err)
    res.status(500).json({ message: err.message })
  }
})

export default handler
