import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import { findUserById } from '@memberup/shared/src/libs/prisma/user'
import { updateUserProfile } from '@memberup/shared/src/libs/prisma/user-profile'
import { IUser } from '@memberup/shared/src/types/interfaces'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { getFullName } from '@/shared-libs/profile'
import { stripeCreateSetupIntentMain } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']

    // NOTE 3023: The stripe_customer_id on user_profile is the one for the MemberUp account.
    // TODO 3023: Allow to handle more than one community. When managing subscriptions we must take care of passing the right one.
    // TODO 3023: findUserById should not require the where clause. It should be able to find the user by the id in the token.
    const dbUser: IUser = await findUserById({
      where: { id: user.id },
      include: {
        profile: true,
      },
    })
    const userProfile = dbUser.profile
    let stripeCustomerId = userProfile.stripe_customer_id
    if (!stripeCustomerId) {
      // const payload = {
      //   email: user.email,
      //   description: '',
      //   name: getFullName(user.first_name, user.last_name, ''),
      //   metadata: {
      //     user_id: user.id,
      //   },
      // }
      // // NOTE: We create the Stripe account here for backward compatibility. In the future we could remove this code.
      // const stripeCustomer = await stripeCreateCustomer(null, payload)
      //
      // const stripe = getStripe(secretKey)
      // const result = await stripe.customers.create(payload)
      // return result
      //
      // stripeCustomerId = stripeCustomer.id
      // await updateUserProfile({
      //   where: { user_id: user.id },
      //   data: { stripe_customer_id: stripeCustomer.id },
      // })
    }

    const { payment_method_types } = req.body
    const setupIntent = await stripeCreateSetupIntentMain(STRIPE_SECRET_KEY, {
      customer: stripeCustomerId,
      payment_method_types,
      usage: 'on_session',
    })

    res.status(200).send({ success: true, data: setupIntent })
  } catch (err: any) {
    res.status(500)
  }
})

export default handler
