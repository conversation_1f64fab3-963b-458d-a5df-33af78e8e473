import { captureException as sentryCaptureException } from '@sentry/nextjs'
import _orderBy from 'lodash/orderBy'
import _uniqBy from 'lodash/uniqBy'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import {
  activeCampaignCreateContactCustomFieldValue,
  activeCampaignGetContactFieldValues,
  activeCampaignGetContacts,
  activeCampaignUpdateContactCustomFieldValue,
} from '@memberup/shared/src/libs/active-campaign'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findInviteLinks, updateInviteLink } from '@memberup/shared/src/libs/prisma/invite-link'
import { findMembershipSetting, updateMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { addDomain, deleteDomain, getDomain } from '@memberup/shared/src/libs/vercel'
import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleAndSetCurrentMembershipMiddleware from '@/memberup/middlewares/check-creator-role'
import { stripeCreatePrice, stripeGetPrices, stripeUpdatePrice } from '@/shared-libs/stripe'

const DEFAULT_DOMAIN = process.env.NEXT_PUBLIC_DEFAULT_DOMAIN
const ACTIVE_CAMPAIGN_API_URL = process.env.ACTIVE_CAMPAIGN_API_URL
const ACTIVE_CAMPAIGN_API_KEY = process.env.ACTIVE_CAMPAIGN_API_KEY

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRoleAndSetCurrentMembershipMiddleware)
  .put(async (req, res) => {
    try {
      const {
        body: { id, stripe_prices, plan, stripe_payment_method_id, stripe_enable_annual, ...rest },
      } = req
      const user = req['user']

      const membershipSetting = user.current_user_membership.membership.membership_setting

      const data = { ...rest }

      if (typeof stripe_enable_annual !== 'undefined') {
        data['stripe_enable_annual'] = stripe_enable_annual
      }

      if (stripe_payment_method_id) {
        data['stripe_payment_method_id'] = stripe_payment_method_id
      }

      if (stripe_prices?.length) {
        const hasMonthly = stripe_prices.some((price: any) => price.recurring?.interval === 'month' && price.active)
        const hasOneTime = stripe_prices.some((price: any) => price.metadata?.planType === 'one_time' && price.active)

        if (hasMonthly && hasOneTime) {
          return res.status(400).json({
            error: 'Cannot activate both monthly subscriptions and one-time payments simultaneously.',
          })
        }

        const stripeConnectAccountInfo = membershipSetting?.stripe_connect_account
        const stripeProductId = membershipSetting?.stripe_product_id
        if (
          !stripeConnectAccountInfo ||
          (!stripeConnectAccountInfo.access_token && !stripeConnectAccountInfo.enabled) ||
          !stripeProductId
        ) {
          return res.status(400).json({
            message: 'Membership is not configured properly. Please contact the owner.',
          })
        }

        const dbStripePrices = membershipSetting.stripe_prices || []
        let stripePrices = []

        if (dbStripePrices.length) {
          const existingStripePrices = await stripeGetPrices(stripeConnectAccountInfo, {
            product: stripeProductId,
            limit: 100,
          })
          stripePrices = existingStripePrices.data
            .filter((p) => dbStripePrices.some((rsp) => rsp['id'] === p.id))
            .map((p) => ({
              id: p.id,
              active: p.active,
              created: p.created,
              currency: p.currency,
              livemode: p.livemode,
              unit_amount: p.unit_amount,
              recurring: p.recurring,
              metadata: p.metadata,
              type: p.type,
            }))
        }

        for (const stripe_price of stripe_prices) {
          const sIdex = stripePrices.findIndex((p) =>
            stripe_price.id
              ? stripe_price.id === p.id
              : p.unit_amount == stripe_price.unit_amount &&
                (p.recurring?.interval || null) === (stripe_price.recurring?.interval || null),
          )

          if (sIdex >= 0) {
            const data = {}
            if (stripe_price.active !== undefined) {
              data['active'] = stripe_price.active
            }
            if (stripe_price.metadata) {
              data['metadata'] = stripe_price.metadata
            }
            const temp = await stripeUpdatePrice(stripeConnectAccountInfo, stripePrices[sIdex].id, data)

            if (temp) {
              const dbPriceIndex = dbStripePrices.findIndex((p) => p.id === temp.id)
              if (dbPriceIndex >= 0) {
                dbStripePrices[dbPriceIndex] = {
                  id: temp.id,
                  active: stripe_price.active,
                  created: temp.created,
                  currency: temp.currency,
                  livemode: temp.livemode,
                  unit_amount: temp.unit_amount,
                  recurring: temp.recurring,
                  metadata: temp.metadata,
                }
              }
            }
          } else {
            const temp = await stripeCreatePrice(stripeConnectAccountInfo, {
              unit_amount: stripe_price.unit_amount,
              currency: 'usd',
              recurring:
                stripe_price.metadata?.planType === 'one_time'
                  ? undefined
                  : {
                      interval: stripe_price?.recurring?.interval || 'month',
                    },
              product: stripeProductId,
              metadata: {
                name: stripe_price.metadata?.name,
                title: stripe_price.metadata?.title,
                planType: stripe_price.metadata?.planType,
                description: stripe_price.metadata?.description,
              },
            })
            if (temp) {
              dbStripePrices.push({
                id: temp.id,
                active: temp.active,
                created: temp.created,
                currency: temp.currency,
                livemode: temp.livemode,
                unit_amount: temp.unit_amount || null,
                recurring: temp.recurring,
                metadata: temp.metadata,
              })
            }
          }
        }

        data['stripe_prices'] = _uniqBy(
          _orderBy(dbStripePrices, ['active', 'created'], ['desc', 'desc']),
          'recurring.interval',
        )
      }

      const updatedMembershipSetting = await updateMembershipSetting({
        where: { id: membershipSetting.id },
        data,
        include: { active_spark_category: true },
      })

      // if (oldCustomHost && oldCustomHost !== membershipSetting.custom_host) {
      //   const inviteLinks = await findInviteLinks({
      //     where: {
      //       membership_id: user.current_membership_id,
      //       url: {
      //         contains: `://${oldCustomHost}`,
      //       },
      //     },
      //   })
      //
      //   for (const inviteLink of inviteLinks.docs) {
      //     await updateInviteLink({
      //       where: { id: inviteLink.id },
      //       data: {
      //         url: inviteLink.url.replace(
      //           `://${oldCustomHost}`,
      //           `://${membershipSetting.custom_host || `${membershipSlug}.${DEFAULT_DOMAIN}`}`
      //         ),
      //       },
      //     })
      //   }
      // }

      if (typeof rest.community_idea === 'boolean') {
        let owner = user.role === USER_ROLE_ENUM.owner ? user : undefined
        if (!owner) {
          owner = await findUser({
            where: {
              membership_id: user.membership_id,
              role: USER_ROLE_ENUM.owner,
            },
          })
        }

        if (owner) {
          const temp = await activeCampaignGetContacts(ACTIVE_CAMPAIGN_API_URL, ACTIVE_CAMPAIGN_API_KEY, owner.email)
          const contactId = temp?.contacts?.[0]?.id
          if (contactId) {
            const customFieldValues = await activeCampaignGetContactFieldValues(
              ACTIVE_CAMPAIGN_API_URL,
              ACTIVE_CAMPAIGN_API_KEY,
              temp.contacts[0].id,
            )

            const customFieldValue = customFieldValues?.fieldValues?.find((item) => item.field === '10')

            if (customFieldValue) {
              activeCampaignUpdateContactCustomFieldValue(
                ACTIVE_CAMPAIGN_API_URL,
                ACTIVE_CAMPAIGN_API_KEY,
                contactId,
                customFieldValue.field,
                customFieldValue.id,
                membershipSetting.community_idea === true ? 'Yes' : 'No',
              )
            } else {
              activeCampaignCreateContactCustomFieldValue(
                ACTIVE_CAMPAIGN_API_URL,
                ACTIVE_CAMPAIGN_API_KEY,
                contactId,
                '10',
                membershipSetting.community_idea === true ? 'Yes' : 'No',
              )
            }
          }
        }
      }

      return res.json({ success: true, data: updatedMembershipSetting || {} })
    } catch (err: any) {
      console.log('err ======', err)
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'MembershipSetting'))
    }
  })

export default handler
