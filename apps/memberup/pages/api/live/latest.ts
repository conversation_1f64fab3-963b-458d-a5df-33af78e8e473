import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findLives } from '@memberup/shared/src/libs/prisma/live'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const user = req['user']
    const result = await findLives({
      where: { user: user.id },
      take: 1,
      orderBy: { createdAt: 'desc' },
    })

    return res.json({
      success: true,
      data: result.docs[0],
    })
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'Live'))
  }
})

export default handler
