import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import {
  activeCampaignCreateContactCustomFieldValue,
  activeCampaignGetContactFieldValues,
  activeCampaignGetContacts,
  activeCampaignUpdateContactCustomFieldValue,
} from '@memberup/shared/src/libs/active-campaign'
import { createChannel, findChannels } from '@memberup/shared/src/libs/prisma/channel'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { updateMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { CHANNEL_TYPE_ENUM, MEMBERUP_PLAN_ENUM, SPACE_TYPE_ENUM, USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'

const ACTIVE_CAMPAIGN_ENABLED = process.env.ACTIVE_CAMPAIGN_ENABLED === 'true'
const ACTIVE_CAMPAIGN_API_URL = process.env.ACTIVE_CAMPAIGN_API_URL
const ACTIVE_CAMPAIGN_API_KEY = process.env.ACTIVE_CAMPAIGN_API_KEY

// TODO 3023: Cleanup. This is obsolete. Check if there is something useful about the updateActiveCampaignContact function.
export const updateActiveCampaignContact = async (email: string, plan: MEMBERUP_PLAN_ENUM) => {
  if (ACTIVE_CAMPAIGN_ENABLED && ACTIVE_CAMPAIGN_API_URL && ACTIVE_CAMPAIGN_API_KEY) {
    try {
      const temp = await activeCampaignGetContacts(ACTIVE_CAMPAIGN_API_URL, ACTIVE_CAMPAIGN_API_KEY, email)
      let contactId = temp?.contacts?.[0]?.id

      if (contactId) {
        const customFieldValues = await activeCampaignGetContactFieldValues(
          ACTIVE_CAMPAIGN_API_URL,
          ACTIVE_CAMPAIGN_API_KEY,
          contactId,
        )

        let customFieldValue = customFieldValues?.fieldValues?.find((item) => item.field === '11')

        const membershipFieldValueOptionValue =
          plan === MEMBERUP_PLAN_ENUM.enterprise
            ? 'Infinite'
            : plan === MEMBERUP_PLAN_ENUM.pro
              ? 'In-Motion'
              : 'Inspired'
        if (customFieldValue) {
          activeCampaignUpdateContactCustomFieldValue(
            ACTIVE_CAMPAIGN_API_URL,
            ACTIVE_CAMPAIGN_API_KEY,
            contactId,
            customFieldValue.field,
            customFieldValue.id,
            membershipFieldValueOptionValue,
          )
        } else {
          activeCampaignCreateContactCustomFieldValue(
            ACTIVE_CAMPAIGN_API_URL,
            ACTIVE_CAMPAIGN_API_KEY,
            contactId,
            '11',
            membershipFieldValueOptionValue,
          )
        }

        customFieldValue = customFieldValues?.fieldValues?.find((item) => item.field === '12')
        if (customFieldValue) {
          activeCampaignUpdateContactCustomFieldValue(
            ACTIVE_CAMPAIGN_API_URL,
            ACTIVE_CAMPAIGN_API_KEY,
            contactId,
            customFieldValue.field,
            customFieldValue.id,
            'Completed',
          )
        } else {
          activeCampaignCreateContactCustomFieldValue(
            ACTIVE_CAMPAIGN_API_URL,
            ACTIVE_CAMPAIGN_API_KEY,
            contactId,
            '12',
            'Completed',
          )
        }
      }
    } catch (err) {
      console.log('err ====', err)
    }
  }
}

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Error occurred. Please try again later.')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('API is not responding. Please try again later.')
  },
})

handler.post(async (req, res) => {
  try {
    const { id: membershipId } = req.body
    if (!membershipId) {
      return res.status(400).json({ error: `id is required.` })
    }

    const membershipSetting = await updateMembershipSetting({
      data: {
        completed_membership: 1,
      },
      where: {
        membership_id: membershipId,
      },
      include: {
        membership: {
          include: {
            users: {
              where: {
                role: USER_ROLE_ENUM.owner,
              },
            },
            user_memberships: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    })

    const membership = membershipSetting?.['membership']
    const owner = membership.user_memberships.find((item) => item.user_role === USER_ROLE_ENUM.owner)?.user
    const ownerId = owner?.id

    const channelsData = [
      {
        name: 'Announcements',
        title: 'Announcements',
        description: 'Share announcements!',
        slug: 'announcements',
        is_private: true,
      },
      {
        name: 'Introductions',
        title: 'Introductions',
        description: 'Say hello!',
        slug: 'introductions',
        is_private: false,
      },
      {
        name: 'Member Wins',
        title: 'Member Wins',
        description: 'Celebrate your wins!',
        slug: 'member-wins',
        is_private: false,
      },
    ]

    if (!membershipSetting?.id || !membership.id || !owner?.id) {
      return res.status(400).json({ error: `Couldn't complete the community. Please try again.` })
    }

    const channels = await findChannels({
      where: {
        membership_id: membershipId,
        name: 'Home',
      },
    })
    if (!channels.docs.length) {
      for (const channelData of channelsData) {
        await createChannel({
          data: {
            ...channelData,
            type: CHANNEL_TYPE_ENUM.livestream,
            space_type: SPACE_TYPE_ENUM.space,
            created_by: {
              connect: {
                id: ownerId,
              },
            },
            membership: {
              connect: {
                id: membershipId,
              },
            },
          },
        }).catch((err) => {
          console.log('err =====', err)
        })
      }
    }

    updateActiveCampaignContact(owner.email, membershipSetting.plan)

    return res.json({
      success: true,
      data: {
        membership: {
          id: membership.id,
          active: membership.active,
          brand: membership.brand,
          domain: membership.domain,
          host: membership.host,
          name: membership.name,
          slug: membership.slug,
          createdAt: membership.createdAt,
          updatedAt: membership.updatedAt,
          membership_setting: {
            completed_membership: membershipSetting?.completed_membership,
            plan: membershipSetting?.plan,
            stripe_enable_annual: membershipSetting?.stripe_enable_annual,
            stripe_payment_method_id: membershipSetting?.stripe_payment_method_id,
          },
        },
      },
    })
  } catch (err: any) {
    console.error('community err ==== ', err?.response?.data)
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'Community'))
  }
})

export default handler
