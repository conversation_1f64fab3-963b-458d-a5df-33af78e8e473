import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY, STRIPE_WEBHOOK_MEMBERUP_SECRET } from '@memberup/shared/src/config/envs'
import { StripeEventEnum, stripeWebhook } from '@memberup/shared/src/libs/stripe'
import { inngest } from '@memberup/shared/src/services/inngest'

export const config = {
  api: {
    bodyParser: false,
  },
}

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end(err.message)
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.post(async (req, res) => {
  try {
    const event = await stripeWebhook(STRIPE_SECRET_KEY, STRIPE_WEBHOOK_MEMBERUP_SECRET, req)

    // Extract the object from the event.
    const dataObject = event?.data?.object

    // Handle the event
    // Review important events for Billing webhooks
    // https://stripe.com/docs/billing/webhooks
    // Remove comment to see the various objects sent for this sample

    switch (event?.type) {
      case StripeEventEnum.CUSTOMER_DELETED:
        await inngest.send({ name: 'stripe/customer.deleted', data: { ...dataObject, isMembershipStripe: false } })
        break
      case StripeEventEnum.CUSTOMER_SUBSCRIPTION_TRIAL_WILL_END:
        break
      case StripeEventEnum.CUSTOMER_SUBSCRIPTION_CREATED:
      case StripeEventEnum.CUSTOMER_SUBSCRIPTION_UPDATED:
        if (dataObject['status'] !== 'incomplete') {
          const data = {
            eventType: event.type === StripeEventEnum.CUSTOMER_SUBSCRIPTION_CREATED ? 'created' : 'updated',
            ...dataObject,
            isMembershipStripe: false,
          }
          await inngest.send({ name: 'stripe/customer.subscription.updated', data: data })
        }
        break
      case StripeEventEnum.CUSTOMER_SUBSCRIPTION_DELETED:
        await inngest.send({
          name: 'stripe/customer.subscription.deleted',
          data: { ...dataObject, isMembershipStripe: false },
        })
        break
      case StripeEventEnum.INVOICE_PAID:
        // Used to provision services after the trial has ended.
        // The status of the invoice will show up as paid. Store the status in your
        // database to reference when a user accesses your service to avoid hitting rate limits.
        await inngest.send({ name: 'stripe/invoice.paid', data: { ...dataObject, isMembershipStripe: false } })
        break
      case StripeEventEnum.INVOICE_PAYMENT_FAILED:
        // If the payment fails or the customer does not have a valid payment method,
        //  an invoice.payment_failed event is sent, the subscription becomes past_due.
        // Use this webhook to notify your user that their payment has
        // failed and to retrieve new card details.
        break
      case StripeEventEnum.PAYMENT_METHOD_ATTACHED:
      case StripeEventEnum.PAYMENT_METHOD_UPDATED:
      case StripeEventEnum.PAYMENT_METHOD_AUTOMATICALLY_UPDATED:
        await inngest.send({
          name: 'stripe/payment_method.attached',
          data: { ...dataObject, isMembershipStripe: false },
        })
        break
      case StripeEventEnum.PAYMENT_INTENT_SUCCEEDED:
        await inngest.send({
          name: 'stripe/payment_intent.succeeded',
          data: { ...dataObject, isMembershipStripe: false },
        })
      default:
      // Unexpected event type
    }
    res.status(200).json({ success: true })
  } catch (err) {
    console.error(err)
    sentryCaptureException(err)
    res.status(200).json({ success: false, error: err.message })
  }
})

export default handler
