import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { parseQuery } from '@memberup/shared/src/libs/api-utils'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status201, status500 } from '@/shared-libs/api-utils'
import { prisma } from '@/shared-libs/prisma/prisma'
import {
  createSparkMembershipQuestionInstanceResponse,
  findSparkResponses,
  getSparkCurrentStreak,
} from '@/shared-libs/prisma/spark-response'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { where, take, select, skip, orderBy } = parseQuery(req.query)
      const result = await findSparkResponses({
        where: { membership_id: user.current_membership_id, ...(where || {}) },
        take,
        select,
        skip,
        orderBy: [{ createdAt: 'desc' }],
      })
      return res.json({ success: true, data: result })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'SparkResponse'))
    }
  })
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { spark_membership_question_instance_id, content } = req.body

      let streakCount = await getSparkCurrentStreak(user.id, user.current_membership_id)
      streakCount += 1

      const membership = user.current_user_membership.membership

      const channelId = membership.membership_setting.spark_current_membership_category_id
      const result = await createSparkMembershipQuestionInstanceResponse(
        {
          data: {
            content,
            membership_id: membership.id,
            spark_membership_question_instance: {
              connect: {
                id: spark_membership_question_instance_id,
              },
            },
            streak_count: streakCount,
            user: {
              connect: {
                id: user.id,
              },
            },
          },
        },
        channelId,
      )

      const updatedQuestion = await prisma.sparkMembershipQuestionInstance.findFirst({
        where: {
          id: spark_membership_question_instance_id,
        },
        include: {
          spark_membership_question_instance_responses: true,
        },
      })

      return status201(res, { response: result, streak_count: streakCount, updated_question: updatedQuestion })
    } catch (err: any) {
      sentryCaptureException(err)
      return status500(res, err.message)
    }
  })

export default handler
