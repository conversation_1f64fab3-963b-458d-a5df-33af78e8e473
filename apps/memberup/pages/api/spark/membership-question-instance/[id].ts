import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import prisma from '@/shared-libs/prisma/prisma'
import { deleteSparkMembershipQuestionById, findSparkMembershipQuestion } from '@/shared-libs/prisma/spark-m-question'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRoleMiddleware)
  // .get(async (req, res) => {
  //   try {
  //     const user = req['user']
  //     const {
  //       query: { id },
  //     } = req
  //     const result = await findSparkMembershipQuestion({
  //       where: { id: id as string, membership_id: user.current_membership_id },
  //       include: {
  //         question: true,
  //       },
  //     })
  //     return res.json({ success: true, data: result })
  //   } catch (err: any) {
  //     sentryCaptureException(err)
  //     res.status(400).json(errorHandler(err, 'SparkQuestion'))
  //   }
  // })
  .delete(async (req, res) => {
    try {
      const { id, membership_id } = req.query
      const result = await prisma.sparkMembershipQuestion.delete({
        where: {
          id: id as string,
          membership_id: membership_id as string,
        },
      })
      if (result?.id) {
        return res.status(200).send({ success: true, data: result })
      }
      return res.status(400).json(errorHandler(result, 'SparkQuestion'))
    } catch (err: any) {
      console.error(err)
      sentryCaptureException(err)
      return res.status(400).json(errorHandler(err, 'SparkQuestion'))
    }
  })

export default handler
