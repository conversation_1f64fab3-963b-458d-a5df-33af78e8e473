import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status500 } from '@/shared-libs/api-utils'
import { prisma } from '@/shared-libs/prisma/prisma'
import { getSparkCurrentStreak } from '@/shared-libs/prisma/spark-response'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const user = req['user']
    const membershipId = req.query.membership_id as string
    if (!membershipId) {
      return status400(res, `membership_id was not provided.`)
    }

    const membership = await prisma.membership.findUnique({
      where: {
        id: membershipId as string,
      },
      include: {
        membership_setting: true,
      },
    })
    if (!membership) {
      return status400(res, `membership_id was not provided was not found.`)
    }

    const result = await prisma.sparkMembershipQuestionInstance.findFirst({
      where: {
        id: membership.membership_setting.spark_current_membership_question_instance_id,
      },
      include: {
        spark_membership_question_instance_responses: true,
      },
    })

    const streak = await getSparkCurrentStreak(user.id, membershipId)

    return status200(res, {
      question: result,
      spark_streak: streak,
    })
  } catch (err: any) {
    console.error(err)
    sentryCaptureException(err)
    return status500(res, err.message)
  }
})

export default handler
