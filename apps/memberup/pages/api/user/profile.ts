import * as Sentry from '@sentry/node'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { knockSetUserPreferences } from '@memberup/shared/src/libs/knock'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findUser, updateUser } from '@memberup/shared/src/libs/prisma/user'
import { updateUserProfile } from '@memberup/shared/src/libs/prisma/user-profile'
import { NOTIFICATION_SETTINGS } from '@memberup/shared/src/settings/notifications'
import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { internalServerError } from '@/lib/error-messages'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { setupUserOnExternalServices } from '@/shared-libs/user'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).put(async (req, res) => {
  try {
    const user = req['user']
    const {
      query: { id },
      body: {
        first_name,
        last_name,
        username,
        user_id,
        stripe_subscription_canceled_at,
        stripe_customer_id,
        stripe_payment_method_id,
        stripe_subscription_id,
        stripe_subscription_status,
        ...rest
      },
    } = req

    let targetUser: IUser = await findUser({
      where: { id: id as string },
      include: {
        profile: true,
        membership: {
          include: {
            membership_setting: true,
          },
        },
      },
    })

    if (
      [USER_ROLE_ENUM.admin, USER_ROLE_ENUM.creator, USER_ROLE_ENUM.owner].includes(
        user.role || (USER_ROLE_ENUM.member as any),
      )
    ) {
      if (targetUser.membership_id !== user.membership_id) {
        return res.status(400).end(`You don't have permission to update this user's information.`)
      }
    } else {
      if (user.id !== id) {
        return res.status(400).end(`You don't have permission to update other user's information.`)
      }
      if (rest.active || rest.user_id || rest.membership_id) {
        return res
          .status(400)
          .end(
            `You are trying to update data that you don't have pemission. Please ask admin or creator to update those information.`,
          )
      }
    }

    if (targetUser?.profile?.id) {
      const membershipData = targetUser.membership
      let updatedProfile = targetUser.profile
      let fullNameChanged = false
      let usernameChanged = false

      const userUpdate = {} as any
      const profileUpdate = {} as any

      if (first_name && last_name && (first_name !== targetUser.first_name || last_name !== targetUser.last_name)) {
        if (user.name_updated_at) {
          res.status(400).end('First and last names can be updated only once.')
        }

        fullNameChanged = true
        Object.assign(userUpdate, { first_name, last_name, name_updated_at: new Date() })
      }

      if (username && username !== targetUser.username) {
        if (user.username_updated_at) {
          res.status(400).end('Username can be updated only once.')
        }
        usernameChanged = true
        Object.assign(userUpdate, { username, username_updated_at: new Date() })
      }

      if (fullNameChanged || usernameChanged) {
        targetUser = await updateUser({
          where: { id: targetUser.id },
          data: userUpdate,
        })
      }

      if (Object.keys(rest).length) {
        updatedProfile = await updateUserProfile({
          where: { id: updatedProfile.id },
          data: { ...rest, ...profileUpdate },
        })

        if (rest.enable_notifications && updatedProfile.enable_notifications) {
          const enableNotifications = updatedProfile.enable_notifications
          const preferenceSet = {
            workflows: {},
          }
          for (const notificationSetting of NOTIFICATION_SETTINGS) {
            preferenceSet.workflows[notificationSetting.name] = {
              channel_types: {
                email: enableNotifications[notificationSetting.name]?.email,
                in_app_feed: enableNotifications[notificationSetting.name]?.in_app_feed,
              },
            }
          }

          await knockSetUserPreferences(id as string, preferenceSet, {
            preferenceSet: user.current_membership_id,
          })
        }
      }

      if (fullNameChanged || usernameChanged || rest.image || rest.image_crop_area) {
        await setupUserOnExternalServices(
          {
            ...targetUser,
            profile: updatedProfile,
          },
          membershipData,
          null,
          false,
          false,
        )
      }

      const { password, profile, membership, ...userData } = targetUser

      return res.status(200).send({
        success: true,
        data: {
          ...updatedProfile,
          user: userData,
        },
      })
    } else {
      return res.status(400).json(errorHandler(null, 'UserProfile'))
    }
  } catch (error) {
    Sentry.captureException(error)
    console.error(error)
    return res.status(500).json({ message: internalServerError })
  }
})

export default handler
