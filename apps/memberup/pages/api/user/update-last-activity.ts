import * as Sentry from '@sentry/node'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { updateUserLastActivity } from '@/lib/server/users'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']

    await updateUserLastActivity(user.id)

    return res.status(200).send({})
  } catch (error) {
    console.error(error)
    Sentry.captureException(error)
    return res.status(500).send({})
  }
})

export default handler
