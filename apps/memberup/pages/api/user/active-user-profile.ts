import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUserById } from '@memberup/shared/src/libs/prisma/user'
import { stripeGetSubscription } from '@memberup/shared/src/libs/stripe'
import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { IUser } from '@memberup/shared/src/types/interfaces'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  let sentryTransaction
  try {
    const user = req['user']
    const result: IUser = await findUserById({
      where: { id: user.id },
      include: {
        profile: true,
        membership: true,
      },
    })

    if (!result?.id) {
      return res.status(400).end('There is no user.')
    }

    const userId = result.id
    const profile = result.profile
    const stripeAccessToken = user.membership_setting?.stripe_connect_account?.access_token
    const stripeUserId = user.membership_setting?.stripe_connect_account?.stripe_user_id

    // const channels = await findChannels({
    //   where: { membership_id: user.membership_id },
    // })
    // const channelIds = channels.docs?.map((c) => c.id)
    // if (channelIds?.length) {
    //   const streamChannelsOfUser = await getStreamChannels({
    //     id: { $in: channelIds },
    //     members: { $in: [userId] },
    //   })
    //   const streamChannelIdsOfUser = streamChannelsOfUser.map((c) => c.id)
    //   const streamChannels = await getStreamChannels({
    //     id: { $in: channelIds },
    //   })
    //   const newChannels = streamChannels.filter((c) => streamChannelIdsOfUser.indexOf(c.id) < 0)
    //   newChannels.forEach(async (c) => {
    //     await c.addMembers ([{ user_id: userId }])
    //   })
    // }

    let stripeSubscription
    if (profile.stripe_subscription_id && stripeAccessToken) {
      if (![USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner].includes(result.role as any)) {
        stripeSubscription = await stripeGetSubscription(
          stripeAccessToken,
          profile.stripe_subscription_id,
          stripeUserId,
        )
      }
    }

    return res.status(200).send({
      success: true,
      data: {
        profile: {
          ...profile,
          stripe_subscription: stripeSubscription || null,
        },
      },
    })
  } catch (err: any) {
    console.log('test err =====', err)
    // sentryCaptureException(err)
    return res.status(400).end('Failed to create the Stream User.')
  } finally {
    if (sentryTransaction) {
      sentryTransaction.finish()
    }
  }
})

export default handler
