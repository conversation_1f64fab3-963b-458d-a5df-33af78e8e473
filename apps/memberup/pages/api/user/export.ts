import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findUsers } from '@memberup/shared/src/libs/prisma/user'
import { USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import prisma from '@/shared-libs/prisma/prisma'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRoleMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']

      const select = {
        id: true,
        first_name: true,
        last_name: true,
        membership_id: true,
        role: true,
        status: true,
        createdAt: true,
        email: true,
        profile: {
          select: {
            login_count: true,
            last_activity_at: true,
            last_login_at: true,
          },
        },
      }

      const filter = {}
      filter['status'] = USER_STATUS_ENUM.active
      filter['membership_id'] = user.current_membership_id

      const result = await findUsers({
        where: filter,
        select,
        orderBy: [{ last_name: 'asc' }, { first_name: 'asc' }],
      })

      // TODO: This could be precomputed in the database
      /* for each user, get their courses, and progress for each course and build a response object */
      // Parallelize fetching courses with progress for each user
      const coursesPromises = result.docs.map((user) =>
        prisma.contentLibraryCourse.findMany({
          where: {
            visibility: 'published',
            membership_id: user.membership_id,
          },
          select: {
            id: true,
            title: true,
            description: true,
            ContentLibraryCourseUserProgress: {
              where: {
                user_id: user.id,
              },
              select: {
                progress_percentage: true,
                // Add other fields from ContentLibraryCourseUserProgress as needed
              },
            },
          },
        }),
      )

      // Await all promises simultaneously
      const coursesResults = await Promise.all(coursesPromises)

      // Assign the fetched courses with their progress to the corresponding user in the result.docs array
      for (let i = 0; i < result.docs.length; i++) {
        ;(result.docs[i] as any).courses = coursesResults[i]
      }

      res.json({ success: true, data: result.docs })
    } catch (err: any) {
      console.dir('err ============', err)
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'User'))
    }
  })

export default handler
