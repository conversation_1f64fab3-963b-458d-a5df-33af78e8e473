import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUser } from '@memberup/shared/src/libs/prisma/user'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200 } from '@/shared-libs/api-utils'
import prisma from '@/shared-libs/prisma/prisma'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Internal server error')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { username, membership_id: membershipId } = req.query
    const user = await findUser({
      select: {
        id: true,
      },
      where: {
        username: username as string,
      },
    })

    const courses = await prisma.contentLibraryCourse.findMany({
      where: {
        visibility: 'published',
        membership_id: membershipId as string,
      },
      select: {
        id: true,
        title: true,
        description: true,
        ContentLibraryCourseUserProgress: {
          where: {
            user_id: user.id,
          },
          select: {
            progress_percentage: true,
          },
        },
      },
    })
    return status200(res, { user: user, courses: courses })
  } catch (err: any) {
    sentryCaptureException(err)
    return res.status(500).end("Failed to fetch the user's courses progress.")
  }
})

export default handler
