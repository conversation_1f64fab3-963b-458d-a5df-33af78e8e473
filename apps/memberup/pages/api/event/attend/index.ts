import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { createEventAttendee } from '@memberup/shared/src/libs/prisma/event-attendee'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const { event_id } = req.body
    const user = req['user']
    const result = await createEventAttendee({
      data: {
        event: {
          connect: {
            id: event_id,
          },
        },
        user: {
          connect: {
            id: user.id,
          },
        },
      },
      include: {
        user: {
          include: {
            profile: true,
          },
        },
      },
    })
    return res.json({ success: true, data: result })
  } catch (err: any) {
    sentryCaptureException(err)
    return res.status(400).json(errorHandler(err, 'Event'))
  }
})

export default handler
