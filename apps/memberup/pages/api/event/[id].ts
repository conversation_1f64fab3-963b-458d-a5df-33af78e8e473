import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { knockCancelWorkflow } from '@memberup/shared/src/libs/knock'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { deleteEventById, findEventById, updateEvent } from '@memberup/shared/src/libs/prisma/event'
import { EVENT_LOCATION_TYPE_ENUM, EVENT_STATUS_ENUM, KNOCK_WORKFLOW_ENUM } from '@memberup/shared/src/types/enum'
import { knockEvent } from '@/memberup/libs/knock'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { knockTriggerWorkflow } from '@/shared-libs/knock'
import { prisma } from '@/shared-libs/prisma/prisma'
import { findUsers } from '@/shared-libs/prisma/user'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const { id } = req.query
      const result = await findEventById({
        where: { id: id as string },
        include: {
          attendees: {
            include: {
              user: {
                include: {
                  profile: {
                    select: {
                      image: true,
                    },
                  },
                },
              },
            },
          },
        },
      })
      return res.json({ success: true, data: result })
    } catch (err: any) {
      sentryCaptureException(err)
      return res.status(400).json(errorHandler(err, 'Event'))
    }
  })
  .use(checkCreatorRoleMiddleware)
  .put(async (req, res) => {
    try {
      const user = req['user']
      const { id } = req.query
      const { membership_id, data } = req.body
      const membershipId = membership_id as string

      const prevData = await findEventById({
        where: { id: id as string, membership_id: membershipId },
      })

      const result = await updateEvent({
        where: { id: id as string },
        data: data,
      })

      const startTimeStamp = prevData.start_time
      const currentTime = Math.floor(Date.now() / 1000)
      const hasEventPassed = startTimeStamp < currentTime
      const time24hsBeforeStartTimeStamp = startTimeStamp - 24 * 60 * 60 // 24 hours in seconds

      if (!hasEventPassed) {
        // Try to cancel any notification in the future.
        if (prevData?.notify_members && prevData.status === EVENT_STATUS_ENUM.published) {
          const users = await findUsers({
            where: {
              membership_id: prevData.membership_id,
            },
          })
          await knockCancelWorkflow(
            KNOCK_WORKFLOW_ENUM.event,
            `cancel_${id}_${prevData.updatedAt.toISOString()}`,
            users.docs.map((item) => item.id),
          )
        }

        let notifyAsUpdated = time24hsBeforeStartTimeStamp < currentTime
        const eventId = id as string
        if (result?.notify_members && result.status === EVENT_STATUS_ENUM.published) {
          const cancelationKey = `cancel_${id}_${result.updatedAt.toISOString()}`
          knockEvent({
            id: `${eventId}`,
            location:
              result.location_type === EVENT_LOCATION_TYPE_ENUM.tbd
                ? 'TBD'
                : result.location_type === EVENT_LOCATION_TYPE_ENUM.content_release
                  ? 'Content Release'
                  : result.location_address,
            start_time: result.start_time,
            title: result.title,
            time_zone: result.time_zone,
            membership_id: user.current_membership_id,
            user_id: user.id,
            cancelationKey: cancelationKey,
            updated: notifyAsUpdated,
          })
        }
      }

      if (result?.id) {
        return res.json({ success: true, data: result })
      }

      res.status(500).json(errorHandler(result, 'Event'))
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(500).json(errorHandler(err, 'Event'))
    }
  })
  .delete(async (req, res) => {
    try {
      const { id } = req.query
      const user = req['user']
      const { membership_id } = req.body
      const membershipId = membership_id as string

      const event = await findEventById({
        where: {
          id: id as string,
          membership_id: membershipId,
        },
      })

      const result = await deleteEventById(id as string)
      const membership = await prisma.membership.findUnique({
        where: {
          id: result.membership_id,
        },
        select: {
          name: true,
        },
      })

      if (!result?.id) {
        return res.status(404).json(errorHandler(result, 'Event'))
      }
      if (result?.notify_members && result.status === EVENT_STATUS_ENUM.published) {
        const users = await findUsers({
          where: {
            membership_id: result.membership_id,
          },
        })

        // This will cancel the schedule email notification
        await knockCancelWorkflow(
          KNOCK_WORKFLOW_ENUM.event,
          `cancel_${id}_${result.updatedAt.toISOString()}`,
          users.docs.map((item) => item.id),
        )

        await knockTriggerWorkflow(
          user.membership_id,
          KNOCK_WORKFLOW_ENUM.cancel_event,
          user.id,
          users.docs.map((item) => item.id),
          {
            community_name: membership.name,
            event_name: event.title,
          },
        )
      }
      return res.status(200).send({ success: true, data: result })
    } catch (err: any) {
      return res.status(500).json(errorHandler(err, 'Event'))
    }
  })

export default handler
