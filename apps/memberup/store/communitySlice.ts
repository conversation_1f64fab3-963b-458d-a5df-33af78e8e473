import chroma from 'chroma-js'
import { StateCreator } from 'zustand'
import { compute } from 'zustand-computed-state'

import { type StoreState } from './store'
import { getHSLString } from '@/lib/colors'
import { defaultCommunityColors } from '@/lib/constants'
import { IChannel, IMembership, IMembershipSetting, ISpace, IUser, IUserMembership } from '@/shared-types/interfaces'

export interface CommunitySliceState {
  loadingMessages: boolean
  membership?: IMembership | null
  newlyCreatedId?: string | null
  owner?: IUser | null
  overrideThemeMainColor: string | null
  communityAdminSettingsOpen: boolean
  communityUserSettingsOpen: boolean
  spaces: ISpace[] | null
}

export interface CommunitySliceActions {
  reset: () => void
  setMembership: (value: IMembership | null) => void
  setMembershipSettings: (value: IMembershipSetting) => void
  setNewlyCreatedId: (value: string | null) => void
  setLoadingMessages: (value: boolean) => void
  setOverrideThemeMainColor: (value: string | null) => void
  setCommunityAdminSettingsOpen: (value: boolean) => void
  setCommunityUserSettingsOpen: (value: boolean) => void
  setSpaces: (value: ISpace[] | null) => void
}

export interface CommunitySliceComputed {
  colors: {
    primary: string
    secondary: string
    primaryHSL: string
    secondaryHSL: string
  }
  isPaid: boolean
  linearGradient: string
  linearGradient10: string
  userMembership: IUserMembership | null
}

export type CommunitySlice = {
  community: CommunitySliceState & CommunitySliceActions & CommunitySliceComputed
}

const defaultInitialCommunitySliceState: CommunitySliceState = {
  loadingMessages: true,
  membership: null,
  owner: null,
  newlyCreatedId: null,
  overrideThemeMainColor: null,
  communityAdminSettingsOpen: false,
  communityUserSettingsOpen: false,
  spaces: null,
}

export const createCommunitySlice: (
  initialData?: Partial<CommunitySliceState>,
) => StateCreator<StoreState, [], [['zustand/devtools', never]], CommunitySlice> =
  (initialData = defaultInitialCommunitySliceState) =>
  (
    set: (
      partial: StoreState | Partial<StoreState> | ((state: StoreState) => StoreState | Partial<StoreState>),
      replace?: boolean,
    ) => void,
    get: () => StoreState,
  ) => ({
    community: {
      reset: () => {
        set((state: StoreState) => ({ community: { ...state.community, ...defaultInitialCommunitySliceState } }))
      },
      setLoadingMessages: (value: boolean) => {
        set((state: StoreState) => ({ community: { ...state.community, loadingMessages: value } }))
      },
      setMembership: (value: IMembership | null) => {
        set((state: StoreState) => ({ community: { ...state.community, membership: value } }))
      },
      setSpaces: (value: ISpace[] | null) => {
        set((state: StoreState) => ({
          community: {
            ...state.community,
            membership: { ...state.community.membership, channels: value as unknown as IChannel[] | undefined },
          },
        }))
      },
      setMembershipSettings: (value: IMembershipSetting | null) => {
        set((state: StoreState) => ({
          community: { ...state.community, membership: { ...state.community.membership, membership_setting: value } },
        }))
      },
      setCommunityAdminSettingsOpen: (value: boolean) => {
        set((state: StoreState) => ({ community: { ...state.community, communityAdminSettingsOpen: value } }))
      },
      setNewlyCreatedId: (value: string | null) => {
        set((state: StoreState) => ({ community: { ...state.community, newlyCreatedId: value } }))
      },
      setOverrideThemeMainColor: (value: string | null) => {
        set((state: StoreState) => ({ community: { ...state.community, overrideThemeMainColor: value } }))
      },
      ...{ ...defaultInitialCommunitySliceState, ...initialData },
    },
    ...compute('communitySlice', get, (state) => {
      const { community, auth } = state as StoreState

      const baseColor =
        community.overrideThemeMainColor ??
        community.membership?.membership_setting?.theme_main_color ??
        defaultCommunityColors[0]

      const baseHue = chroma(baseColor).get('hsl.h')
      const analogousColors = [
        chroma(baseColor)
          .set('hsl.h', baseHue - 30)
          .hex(),
        baseColor,
      ]

      const userMembership =
        (auth.user?.user_memberships.find(
          (userMembership) => userMembership.membership.id === community.membership?.id,
        ) as IUserMembership) || null

      const isPaid = Boolean(community?.membership?.membership_setting.is_pricing_enabled)

      return {
        community: {
          ...community,
          colors: {
            primary: analogousColors[1],
            secondary: analogousColors[0],
            primaryHSL: getHSLString(analogousColors[1]),
            secondaryHSL: getHSLString(analogousColors[0]),
          },
          isPaid,
          linearGradient: `linear-gradient(90deg, ${analogousColors[0]} 0%, ${analogousColors[1]} 100%)`,
          linearGradient10: `linear-gradient(90deg, ${analogousColors[0]}19 0%, ${analogousColors[1]}19 100%)`,
          userMembership,
        },
      }
    }),
  })
