import { StateCreator } from 'zustand'
import { compute } from 'zustand-computed-state'

import { type StoreState } from './store'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'

export interface UISliceState {
  navbarOffset: number
  searchOpen: boolean
  stickyItemsOffset: number
}

export interface UISliceActions {
  setNavbarOffset: (value: number) => void
  setSearchOpen: (value: boolean) => void
  setStickyItemsOffset: (value: number | ((value: number) => number)) => void
}

export interface UISliceComputed {
  isDarkTheme: boolean
  themeMode: THEME_MODE_ENUM
}

export type UISlice = {
  ui: UISliceState & UISliceActions & UISliceComputed
}

const defaultInitialUISliceState: UISliceState = {
  navbarOffset: 0,
  searchOpen: false,
  stickyItemsOffset: 0,
}

export const createUISlice: () => StateCreator<StoreState, [], [['zustand/devtools', never]], UISlice> =
  () => (set: any, get: any, _: any) => {
    return {
      ui: {
        setSearchOpen: (searchOpen: boolean) => {
          set((state: StoreState) => ({ ui: { ...state.ui, searchOpen } }))
        },
        setStickyItemsOffset: (stickyItemsOffset: number | ((value: number) => number)) => {
          const newOffset =
            typeof stickyItemsOffset === 'function' ? stickyItemsOffset(get().ui.stickyItemsOffset) : stickyItemsOffset
          set((state: StoreState) => ({ ui: { ...state.ui, stickyItemsOffset: newOffset } }))
        },
        setNavbarOffset: (navbarOffset: number) => {
          set((state: StoreState) => ({ ui: { ...state.ui, navbarOffset } }))
        },
        ...defaultInitialUISliceState,
      },
      ...compute('uiSlice', get, (state) => {
        const {
          auth: { profile },
          ui,
        } = state as StoreState

        return {
          ui: {
            ...ui,
            isDarkTheme: !profile?.theme_mode || profile?.theme_mode === THEME_MODE_ENUM.dark,
            themeMode: profile?.theme_mode || THEME_MODE_ENUM.dark,
          },
        }
      }),
    }
  }
