import { StateCreator } from 'zustand'

import { type StoreState } from './store'

export interface StreamChatSliceState {
  connected: boolean
  connecting: boolean
}

export interface StreamChatSliceActions {
  setConnected: (value: boolean) => void
  setConnecting: (value: boolean) => void
}

export type StreamChatSlice = {
  streamChat: StreamChatSliceState & StreamChatSliceActions
}

const defaultInitialStreamChatSliceState: StreamChatSliceState = {
  connected: false,
  connecting: true,
}

export const createStreamChatSlice: (
  initialData?: StreamChatSliceState,
) => StateCreator<StoreState, [], [['zustand/devtools', never]], StreamChatSlice> =
  (initialData = defaultInitialStreamChatSliceState) =>
  (
    set: (
      partial: StoreState | Partial<StoreState> | ((state: StoreState) => StoreState | Partial<StoreState>),
      replace?: boolean,
    ) => void,
  ) => {
    return {
      streamChat: {
        setConnected: (value: boolean) =>
          set((state: StoreState) => ({ streamChat: { ...state.streamChat, connected: value } })),
        setConnecting: (value: boolean) =>
          set((state: StoreState) => ({ streamChat: { ...state.streamChat, connecting: value } })),
        ...initialData,
      },
    }
  }
