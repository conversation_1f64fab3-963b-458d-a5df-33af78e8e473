import _ from 'lodash'
import { StateCreator } from 'zustand'

import { type StoreState } from './store'

export interface FeedSliceState {
  selectedSpaceId: string | null
  pinnedMessages: any[]
  initialized: boolean
  membershipSlug: string | null
  spaces: {
    [key: string]: {
      id: string
      name: string
      slug: string
      description: string
      orderBy: 'activity' | 'newest'
      messages: any[]
      next: string | undefined
    }
  }
}

export interface FeedSliceComputed {
  currentSortBy: 'activity' | 'newest'
}

export interface FeedSliceActions {
  setSelectedSpaceId: (spaceId: string) => void
  addMessages: (spaceId: string, messages: any[], next: string | null) => void
  setMessages: (spaceId: string, messages: any[]) => void
  initializeSpaces: (spaces: any[], membershipSlug: string, initialSpaceId?: string) => void
  getSelectedSpaceOrderBy: (spaceId?: string) => 'activity' | 'newest'
  setSelectedSpaceOrderBy: (orderBy: 'activity' | 'newest') => void
  getSelectedSpaceMessages: () => any[]
  getSelectedSpaceNext: () => string | undefined
  setPinnedMessages: (pinnedMessages: any[]) => void
  getPinnedMessages: () => any[]
  pinMessage: (message: any) => void
  unpinMessage: (message: any) => void
  addMessageToFront: (spaceId: string, message: any) => void
  updateMessage: (spaceId: string, updatedMessage: any) => void
  deleteMessage: (messageId: string) => void
}

export type FeedSlice = {
  feed: FeedSliceState & FeedSliceActions & FeedSliceComputed
}

export type InitialFeedSliceState = {
  membershipSlug: string
  spaces: any[]
  selectedSpaceId: string
  initialized: boolean
  pinnedMessages: any[]
}

const defaultInitialFeedSliceState: Partial<FeedSliceState> = {
  membershipSlug: null,
  spaces: {},
  selectedSpaceId: null,
  initialized: false,
  pinnedMessages: [],
}

export const createFeedSlice: (
  initialData?: InitialFeedSliceState,
) => StateCreator<StoreState, [], [['zustand/devtools', never]], FeedSlice> =
  (initialData = defaultInitialFeedSliceState) =>
  (set: any, get: any) => {
    return {
      feed: {
        selectedSpaceId: null,
        initialized: true,
        spaces: {},
        pinnedMessages: [],
        setSelectedSpaceId: (newSelectedSpaceId: string) => {
          set((state: StoreState) => {
            state.feed.selectedSpaceId = newSelectedSpaceId
            return {
              feed: { ...state.feed },
            }
          })
        },
        pinMessage: (message: any) => {
          set((state: StoreState) => {
            if (!state.feed.pinnedMessages.find((m) => m.id === message.idd)) {
              state.feed.pinnedMessages.push(message)
            }
            // TODO: Remove the message from other spaces
            return {
              feed: { ...state.feed },
            }
          })
        },
        unpinMessage: (message: any) => {
          set((state: StoreState) => {
            const i = state.feed.pinnedMessages.findIndex((m) => m.id === message.id)
            if (i !== -1) {
              state.feed.pinnedMessages.splice(i, 1)
              state.feed.pinnedMessages = [...state.feed.pinnedMessages]
            }
            return {
              feed: { ...state.feed },
            }
          })
        },
        addMessageToFront: (spaceId: string, message: any) => {
          set((state: StoreState) => {
            if (state.feed.spaces[spaceId]) {
              state.feed.spaces[spaceId].messages = [message, ...state.feed.spaces[spaceId].messages]
              state.feed.spaces['community'].messages = [message, ...state.feed.spaces['community'].messages]
            }
            return {
              feed: { ...state.feed },
            }
          })
        },
        deleteMessage: (spaceId: string, messageId: any) => {
          set((state: StoreState) => {
            if (state.feed.spaces[spaceId]) {
              const i = state.feed.spaces[spaceId].messages.findIndex((message) => message.id === messageId)
              if (i !== -1) {
                state.feed.spaces[spaceId].messages.splice(i, 1)
                state.feed.spaces[spaceId].messages = [...state.feed.spaces[spaceId].messages]
              }
              const j = state.feed.spaces['community'].messages.findIndex((message) => message.id === messageId)
              if (j !== -1) {
                state.feed.spaces['community'].messages.splice(j, 1)
                state.feed.spaces['community'].messages = [...state.feed.spaces['community'].messages]
              }
            }
            return {
              feed: { ...state.feed },
            }
          })
        },
        updateMessage: (spaceId: string, updatedMessage: any) => {
          set((state: StoreState) => {
            if (state.feed.spaces[spaceId]) {
              const i = state.feed.spaces[spaceId].messages.findIndex((message) => message.id === updatedMessage.id)
              if (i !== -1) {
                state.feed.spaces[spaceId].messages[i] = updatedMessage
                state.feed.spaces[spaceId].messages = [...state.feed.spaces[spaceId].messages]
              }

              const j = state.feed.spaces['community'].messages.findIndex((message) => message.id === updatedMessage.id)
              if (j !== -1) {
                state.feed.spaces['community'].messages[j] = updatedMessage
                state.feed.spaces['community'].messages = [...state.feed.spaces['community'].messages]
              }
            }
            return {
              feed: { ...state.feed },
            }
          })
        },
        initializeSpaces: (spaceList: any[], membershipSlug: string, initialSpaceId?: string) => {
          set((state: StoreState) => {
            const currentSpaceIds = Object.keys(state.feed.spaces)
            const newSpaceIds = spaceList.map((s) => s.id)
            if (!_.isEqual(_.sortBy(currentSpaceIds), _.sortBy(newSpaceIds))) {
              const newSpaces =
                spaceList.reduce((output, space) => {
                  const id = space.id
                  output[id] = {
                    id: space.id,
                    name: space.name,
                    slug: space.slug,
                    description: space.description,

                    orderBy: 'activity',

                    pinnedMessages: [],
                    messages: [],
                    next: undefined,
                  }
                  return output
                }, {}) || {}

              newSpaces['community'] = {
                id: 'community',
                name: 'Community',
                slug: 'community',
                description: '',
                orderBy: 'activity',
                pinnedMessages: [],
                messages: [],
                next: undefined,
              }
              state.feed.membershipSlug = membershipSlug
              state.feed.spaces = { ...newSpaces }
              state.feed.selectedSpaceId = initialSpaceId
              return {
                feed: { ...state.feed },
              }
            } else {
              return state
            }
          })
        },
        addMessages: (spaceId: string, messages: any[], next?: string) => {
          set((state: StoreState) => {
            if (state.feed.spaces[spaceId]) {
              state.feed.spaces[spaceId].next = next
              state.feed.spaces[spaceId].messages = [...state.feed.spaces[spaceId].messages, ...messages]
            }
            return {
              feed: { ...state.feed },
            }
          })
        },

        setSelectedSpaceOrderBy: (newOrderBy: 'activity' | 'newest') => {
          set((state: StoreState) => {
            const selectedSpaceId = state.feed.selectedSpaceId
            if (selectedSpaceId && state.feed.spaces[selectedSpaceId]) {
              state.feed.spaces[selectedSpaceId].orderBy = newOrderBy
            }
            return {
              feed: { ...state.feed },
            }
          })
        },

        getSelectedSpaceOrderBy: (spaceId?: string) => {
          const state = get()
          if (spaceId) {
            return state.feed.spaces[spaceId]?.orderBy
          }
          return state.feed.spaces[state.feed.selectedSpaceId]?.orderBy
        },
        getSelectedSpaceMessages: () => {
          const state = get()
          const selectedSpaceId = state.feed.selectedSpaceId
          if (selectedSpaceId && state.feed.spaces[selectedSpaceId]) {
            return state.feed.spaces[selectedSpaceId].messages
          }
          return []
        },
        getPinnedMessages: () => {
          const state = get()
          if (state.feed.spaces['community']) {
            return state.feed.spaces['community'].pinnedMessages
          }
          return []
        },
        setPinnedMessages: (pinnedMessages: any[]) => {
          set((state: StoreState) => {
            state.feed.pinnedMessages = [...pinnedMessages]
            return {
              feed: { ...state.feed },
            }
          })
        },
        setMessages: (spaceId: string, messages: any[], next: string) => {
          set((state: StoreState) => {
            if (state.feed.spaces[spaceId]) {
              state.feed.spaces[spaceId].messages = [...messages]
              state.feed.spaces[spaceId].next = next
            }
            return {
              feed: { ...state.feed },
            }
          })
        },
        getSelectedSpaceNext: () => {
          const state = get()
          const selectedSpaceId = state.feed.selectedSpaceId
          if (selectedSpaceId && state.feed.spaces[selectedSpaceId]) {
            return state.feed.spaces[selectedSpaceId].next
          }
          return undefined
        },
      },
    }
  }
