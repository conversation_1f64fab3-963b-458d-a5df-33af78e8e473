import type { Config } from 'tailwindcss'

const config = {
  darkMode: ['class'],
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './lib/**/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    '../../packages/shared/src/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
    './stories/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  corePlugins: {
    preflight: false,
  },
  important: true,
  plugins: [require('tailwindcss-animate')],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1504px',
      },
    },
    extend: {
      zIndex: {
        '100': '100',
        '200': '200',
        '300': '300',
        '1000': '1000',
        '2000': '2000',
      },
      colors: {
        black: {
          100: 'hsl(var(--black-100))', // #8D94A3
          200: 'hsl(var(--black-200))', // #797F8C
          300: 'hsl(var(--black-300))', // #202124
          400: 'hsl(var(--black-400))', // #202327
          500: 'hsl(var(--black-500))', // #16181C
          600: 'hsl(var(--black-600))', // #17171A
          700: 'hsl(var(--black-700))', // #080808
        },
        'body-copy': 'hsl(var(--body-copy))',
        'button-focus': 'hsl(var(--button-focus))',
        'dark-background': {
          1: 'hsl(var(--dark-background-1))',
          2: 'hsl(var(--dark-background-2))',
          3: 'hsl(var(--dark-background-3))',
        },
        'font-light-ui-black': 'hsl(var(--font-light-ui-black))',
        'font-light-ui-gray': 'hsl(var(--font-light-ui-gray))',
        'light-background': {
          200: 'hsl(var(--light-background-200))',
        },
        'pure-black': 'hsl(var(--pure-black))',
        'pure-white': 'hsl(var(--pure-white))',
        'spark-gray': 'hsl(var(--spark-gray))',
        'surface-1': 'hsl(var(--surface-1))',
        'surface-1-rgb': 'hsl(var(--surface-1-rgb))',
        'ui-dark': {
          100: 'hsl(var(--ui-dark-100))',
          300: 'hsl(var(--ui-dark-300))',
          400: 'hsl(var(--ui-dark-400))',
          700: 'hsl(var(--ui-dark-700))',
          800: 'hsl(var(--ui-dark-800))',
          900: 'hsl(var(--ui-dark-900))',
          1000: 'hsl(var(--ui-dark-1000))',
        },
        'ui-light': {
          300: 'hsl(var(--ui-light-300))',
          800: 'hsl(var(--ui-light-800))',
          1000: 'hsl(var(--ui-light-1000))',
        },
        'tertiary-grey': 'hsl(var(--tertiary-grey))',
        'gray-1': 'hsl(var(--gray-1))',
        'gray-2': 'hsl(var(--gray-2))',
        'gray-3': 'hsl(var(--gray-3))',
        'gray-4': 'hsl(var(--gray-4))',
        'gray-5': 'hsl(var(--gray-5))',
        'gray-7': 'hsl(var(--gray-7))',
        white: {
          100: 'hsl(var(--white-100))', // #F1F2F5
          200: 'hsl(var(--white-200))', // #E0E1E1
          300: 'hsl(var(--white-300))', // #EBEBEB
          400: 'hsl(var(--white-400))', // #F3F5F5
          500: 'hsl(var(--white-500))', // #FFFFFF
        },
        grey: {
          100: 'hsl(var(--grey-100))', // #EBEBEB
          200: 'hsl(var(--grey-200))', // #E5E5E7
          300: 'hsl(var(--grey-300))', // #E0E0E0
          400: 'hsl(var(--grey-400))', // #C7C7C7
          500: 'hsl(var(--grey-500))', // #9E9E9E
          600: 'hsl(var(--grey-600))', // #999999
          700: 'hsl(var(--grey-700))', // #666666
          800: 'hsl(var(--grey-800))', // #2E2F34
          900: 'hsl(var(--grey-900))', // #212121
        },
        green: {
          100: 'hsl(var(--green-100))', // #AEE78B
          200: 'hsl(var(--green-200))', // #48B705
        },
        red: {
          100: 'hsl(var(--red-100))', // #C43939
          200: 'hsl(var(--red-200))', // #F34646
        },
        orange: {
          100: 'hsl(var(--orange-100))', // #FFB153
        },
        primary: {
          DEFAULT: 'hsl(var(--primary-200))', // #7B00FF
          100: 'hsl(var(--primary-100))', // #7D52FF
          200: 'hsl(var(--primary-200))', // #7B00FF
          300: 'hsl(var(--primary-300))', // #6402CE
          400: 'hsl(var(--primary-400))', // #D452FF
        },
        // Fallback to primary-200
        'community-primary': 'hsl(var(--community-primary, 269 100% 50%) / <alpha-value>)',
        // Fallback to primary-100
        'community-secondary': 'hsl(var(--community-secondary, 255 100% 66%) / <alpha-value)',

        /* Legacy */
        'legacy-gray-1': 'hsl(var(--legacy-gray-1))',
        'legacy-gray-2': 'hsl(var(--legacy-gray-2))',
        'legacy-white-transparent-80': 'hsl(var(--legacy-white-transparent-80))',

        /* Component-specific */
        'divider-border-color': 'hsl(var(--divider-border-color))',
        'dark-component-border-color': 'hsl(var(--dark-divider-border-color))',

        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--red-200))',
          foreground: 'hsl(var(--pure-white))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        base: '0.625rem',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        'caret-blink': {
          '0%,70%,100%': { opacity: '1' },
          '20%,50%': { opacity: '0' },
        },
        marquee: {
          from: { transform: 'translateX(0)' },
          to: { transform: 'translateX(calc(-100% - var(--gap)))' },
        },
        'marquee-vertical': {
          from: { transform: 'translateY(0)' },
          to: { transform: 'translateY(calc(-100% - var(--gap)))' },
        },
        ripple: {
          '0%, 100%': {
            transform: 'translate(-50%, -50%) scale(1)',
          },
          '50%': {
            transform: 'translate(-50%, -50%) scale(0.9)',
          },
        },
        'spin-around': {
          '0%': {
            transform: 'translateZ(0) rotate(0)',
          },
          '15%, 35%': {
            transform: 'translateZ(0) rotate(90deg)',
          },
          '65%, 85%': {
            transform: 'translateZ(0) rotate(270deg)',
          },
          '100%': {
            transform: 'translateZ(0) rotate(360deg)',
          },
        },
        slide: {
          to: {
            transform: 'translate(calc(100cqw - 100%), 0)',
          },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'caret-blink': 'caret-blink 1.25s ease-out infinite',
        marquee: 'marquee var(--duration) linear infinite',
        'marquee-vertical': 'marquee-vertical var(--duration) linear infinite',
        ripple: 'ripple var(--duration,2s) ease calc(var(--i, 0)*.2s) infinite',
        'spin-around': 'spin-around calc(var(--speed) * 2) infinite linear',
        slide: 'slide var(--speed) ease-in-out infinite alternate',
      },
      fontSize: {
        sxs: [
          '0.6875rem',
          {
            lineHeight: '1.058rem',
          },
        ],
        ssm: [
          '0.8125rem',
          {
            lineHeight: '1.25rem',
          },
        ],
        sbase: [
          '0.9375rem',
          {
            lineHeight: '1.5rem',
          },
        ],
      },
    },
    fontFamily: {
      sans: ['Graphik', 'sans-serif'],
      'inter-numbers': ['Inter Numbers', 'sans-serif'],
    },
  },
} satisfies Config

export default config
