import { ReactNode } from 'react'

import AppLayout from '@/components/layout/AppLayout'
import { BackToTop } from '@/components/ui/back-to-top'

export default function LegalLayout({ children }: { children: ReactNode }) {
  return (
    <>
      <AppLayout>
        <div className="flex min-h-screen">
          <main className="flex-1 pb-8 xl:pt-1">
            <div className="flex w-full justify-center xl:block">{children}</div>
          </main>
        </div>
        <BackToTop />
      </AppLayout>
    </>
  )
}
