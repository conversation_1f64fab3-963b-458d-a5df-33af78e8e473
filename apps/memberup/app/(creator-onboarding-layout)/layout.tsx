import Image from 'next/image'
import Link from 'next/link'

import { ScrollArea } from '@/components/ui'
import { Pill } from '@/components/ui/pill'
import memberupLogo from '@/public/assets/default/logos/memberup-logo.png'

const features = [
  'Events',
  'Rich Profiles',
  'Member Directory',
  'In-App Notifications',
  'Group Chat',
  'Custom Branding',
  'Moderation',
  'Analytics',
  'Community',
  'Multiple Courses',
  'Community Wide Search',
  'Payments',
  'Content Storage',
  'Direct Messages',
]

export default function CreatorOnboardingLayout({ children }: { children: React.ReactNode }) {
  return (
    <ScrollArea className="absolute h-full w-full bg-black-700">
      <div className="flex w-full justify-center overflow-hidden">
        <div className="flex h-full min-h-svh max-w-[1364px] flex-col justify-center px-4 lg:px-16 lg:py-4">
          <Image
            className="mb-8 mt-8 self-center md:mt-0 md:self-auto lg:mb-16"
            src={memberupLogo}
            width={170}
            height={25}
            alt="MemberUp"
          />

          <Link href="/community"></Link>

          <div className="flex w-full flex-col md:flex-row md:justify-between">
            <div className="w-full min-w-0 grow pr-10 md:w-auto">
              <div className="whitespace-normal text-center text-[3rem] font-semibold leading-[1.15] text-white-500 md:text-left lg:text-[3.625rem]">
                Where creators + brands build &#8203;
                <span className="bg-gradient-to-r from-[#DCBCFF] to-[#B673FE] bg-clip-text text-transparent">
                  extraordinary
                </span>
                &#8203;communities.
              </div>
              <div className="py-4 text-center text-base text-white-500 md:text-left">
                Make money building a community around a topic you love. Host your online course, community, events, and
                payments, all in one place, without the tech headache.
              </div>
              <div className="flex flex-wrap items-start justify-start gap-3 lg:pt-14">
                {features.map((feature) => (
                  <Pill key={feature}>{feature}</Pill>
                ))}
              </div>
            </div>
            <div className="relative w-full shrink-0 md:w-[477px]">{children}</div>
          </div>
        </div>
      </div>
    </ScrollArea>
  )
}
