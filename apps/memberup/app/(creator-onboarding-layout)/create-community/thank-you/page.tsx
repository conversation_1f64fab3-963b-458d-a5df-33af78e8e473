'use client'

import CheckIcon from '@mui/icons-material/Check'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import axios from 'axios'
import Cookie from 'js-cookie'
import type { NextPage } from 'next'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'

const DEFAULT_DOMAIN = process.env.NEXT_PUBLIC_DEFAULT_DOMAIN

const ThankYouPage: NextPage = () => {
  const router = useRouter()
  // TODO: 3023 fix this!
  const state = {
    membership: null,
    userInput: null,
  }
  const mountedRef = useMounted(true)
  const [loading, setLoading] = useState(false)
  const affiliateLinkUrl = (state.membership?.membership_setting?.affiliate as any)?.links?.[0]?.url

  useEffect(() => {
    if (mountedRef.current) {
    }
  }, [affiliateLinkUrl])

  const { slug } = router.query

  useEffect(() => {
    if (!slug) {
      return
    }
  }, [slug])

  const handleCopy = () => {
    navigator.clipboard.writeText(affiliateLinkUrl)
  }

  const handleContinue = async () => {
    try {
      setLoading(true)
      // const payload = {
      //   email: state.userInput.email,
      //   password: state.userInput.password,
      //   redirectTo: 'getting-started',
      // }
      router.push(`/${slug}/getting-started`)

      // const result = await axios.post('/api/json-encode', payload)
      // const token = result.data.token
      // const isLocalhost = location.hostname.includes('localhost')
      // const isNgrok = location.hostname.includes('ngrok.io')
      // const host = isLocalhost ? 'localhost:3000' : isNgrok ? 'ngrok.io' : DEFAULT_DOMAIN
      // const protocol = host === 'localhost:3000' ? 'http' : 'https'
      // Cookie.set(`${state.membership.slug}-auth-token`, token, {
      //   domain: `.${isLocalhost ? 'localhost.com' : host}`,
      // })
      // window.localStorage.removeItem('start-memberup-signup_old')
      // location.href = `${protocol}://${state.membership.slug}.${host}/getting-started`
    } catch (err) {
      if (!mountedRef.current) return
      setLoading(false)
    }
  }

  return (
    <Box
      sx={{
        border: '12px solid #000000',
        color: '#FFFFFF',
        '& p': {
          lineHeight: '16px',
        },
      }}
    >
      <Box
        sx={{
          maxWidth: 990,
          m: 'auto',
          pb: '64px',
          overflow: 'hidden',
        }}
      >
        <Box
          data-cy={'payment-confirmed-block'}
          sx={{
            position: 'relative',
          }}
        >
          <Box className="text-center" sx={{ position: 'relative', maxWidth: 450, m: 'auto', pt: '45px', pb: '24px' }}>
            <Box
              sx={{
                position: 'absolute',
                left: 0,
                right: 0,
                top: 0,
                borderBottomLeftRadius: '35%',
                borderBottomRightRadius: '35%',
                bottom: 0,
                backgroundImage:
                  'linear-gradient(180deg, rgba(249, 115, 0, 1) 0%, rgba(217, 0, 135, 1) 52%, rgba(49, 0, 200, 1) 100%)',
                filter: 'blur(160px)',
                opacity: 0.36,
                zIndex: 0,
              }}
            />
            <Box
              sx={{
                position: 'relative',
                maxWidth: 450,
                m: 'auto',
              }}
            >
              <Box
                className="text-center"
                sx={{
                  m: 'auto',
                  mb: '32px',
                }}
              >
                <AppImg src={`/assets/logos/memberup-logo.png`} width={135} height={20} alt="MemberUp Logo" />
              </Box>
              <Box
                className="text-center"
                sx={{
                  width: 64,
                  height: 64,
                  backgroundColor: '#49CC98',
                  borderRadius: '50%',
                  p: '14px',
                  m: 'auto',
                }}
              >
                <CheckIcon sx={{ color: '#000000', fontSize: 36 }} />
              </Box>
              <Typography align="center" color="inherit" variant="h4" gutterBottom sx={{ mt: '24px' }}>
                Payment confirmed!
              </Typography>
              <Typography align="center" color="inherit" variant="h4">
                You just leveled up 👏
              </Typography>
            </Box>
          </Box>
        </Box>

        <Box
          sx={{
            maxWidth: 640,
            m: 'auto',
            p: '24px',
            pb: '54px',
            position: 'relative',
            borderRadius: '16px',
          }}
        >
          <Box
            sx={{
              m: 'auto',
              mb: '16px',
              lineHeight: 1,
              overflow: 'hidden',
              '& img': {
                width: '100%',
                height: 'auto',
                objectFit: 'contain',
                borderRadius: '20px',
              },
            }}
          >
            <AppImg src={`/assets/images/dlgif.gif`} width={576} height={324} alt="DL Gif Image" />
          </Box>
          <Grid container spacing={{ xs: 3, sm: 0 }}>
            <Grid item xs={12} sm={3.5}>
              <Box
                sx={{
                  p: '32px',
                  position: 'relative',
                  backgroundColor: '#212124',
                  borderRadius: '16px',
                  height: '100%',
                  overflow: 'hidden',
                }}
              >
                <Box
                  sx={{
                    backgroundImage: 'url(/assets/images/glass-money-icone.png)',
                    backgroundSize: 'cover',
                    position: 'absolute',
                    right: { xs: 20, sm: -25 },
                    top: { xs: -20, sm: 'initial' },
                    bottom: { xs: 'initial', sm: -10 },
                    transform: 'rotate(-15deg)',
                    width: 110,
                    height: 110,
                  }}
                />
                <Box
                  sx={{
                    backgroundImage: 'url(/assets/images/money-glass-icon.png)',
                    backgroundSize: 'cover',
                    position: 'absolute',
                    left: { xs: 'initial', sm: 0 },
                    right: { xs: 0, sm: 'initial' },
                    top: { xs: 40, sm: 'initial' },
                    bottom: { xs: 'initial', sm: 0 },
                    transform: 'rotate(30deg)',
                    width: 64,
                    height: 64,
                  }}
                />
                <Grid container>
                  <Typography
                    className="font-family-graphik-medium"
                    variant="h4"
                    style={{
                      fontSize: 28,
                      background:
                        'linear-gradient(135deg, #7852FF 0%, #9652FF 17%, #E052FF 33%, #FF53BD 50%, #FF535D 67%, #FF9353 83%, #FFB153 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                    }}
                  >
                    <b>$5,980</b>
                  </Typography>
                </Grid>
                <Typography className="font-family-graphik-medium" variant="body1" sx={{ mt: '10px' }}>
                  Payout
                </Typography>
                <Typography
                  className="font-family-graphik-medium"
                  color="text.disabled"
                  variant="body2"
                  sx={{ mt: '20px', maxWidth: { xs: 'none', sm: '110px' }, ml: '0' }}
                >
                  Refer 10 friends and this amount could be on it&rsquo;s way to you!
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={8.5}>
              <Box
                sx={{
                  backgroundColor: '#17171A',
                  borderRadius: '16px',
                  p: '32px',
                  pb: '38px',
                }}
              >
                <Box sx={{ height: 120 }}>
                  <Typography variant="h3" sx={{ fontSize: 20, lineHeight: '24px', paddingTop: '30px' }}>
                    Get paid to use MemberUp 💰
                  </Typography>
                  <Typography className="font-family-graphik-medium" variant="body2" gutterBottom sx={{ mt: 3 }}>
                    Refer people to MemberUp and earn 20% of annual recurring revenue for life.
                  </Typography>
                </Box>
                <Divider sx={{ my: 2, width: '100%' }} />
                <Box sx={{ m: 'auto', mt: '28px' }}>
                  <Grid container alignItems="flex-end" spacing={1}>
                    <Grid item xs sx={{ overflow: 'hidden' }}>
                      <Typography className="font-family-graphik-medium" variant="body1" gutterBottom>
                        Your affiliate link:
                      </Typography>
                      <Box
                        sx={{
                          borderRadius: '12px',
                          backgroundColor: 'rgba(141,148,163, 0.16)',
                          height: 48,
                          p: '17px 14px',
                        }}
                      >
                        <Typography color="inherit" className="text-ellipsis" variant="body1" sx={{ color: '#808080' }}>
                          {affiliateLinkUrl || 'Link not found'}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item>
                      <Button
                        disabled={!affiliateLinkUrl}
                        sx={{
                          color: '#FFFFFF',
                          borderRadius: '12px',
                          backgroundColor: 'rgba(141,148,163, 0.16)',
                          height: 48,
                          minWidth: 48,
                        }}
                        onClick={handleCopy}
                      >
                        <ContentCopyIcon />
                      </Button>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </Grid>
          </Grid>
          <Box sx={{ m: 'auto', mt: '24px' }}>
            <Button fullWidth className="round-small" variant="contained" sx={{ height: 48 }} onClick={handleContinue}>
              {loading ? <CircularProgress color="inherit" size={16} /> : 'Continue to Community'}
            </Button>
          </Box>
        </Box>
      </Box>
    </Box>
  )
}

ThankYouPage.displayName = 'ThankYouPage'

export default ThankYouPage
