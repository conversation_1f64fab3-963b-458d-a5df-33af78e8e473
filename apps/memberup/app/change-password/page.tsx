'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useState } from 'react'

import { ChangePasswordForm } from '@/components/auth/change-password-form'
import { buttonVariants } from '@/components/ui'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import memberupLogo from '@/public/assets/default/logos/memberup-logo.png'

export default function ChangePasswordPage() {
  const [passwordUpdated, setPasswordUpdated] = useState(false)

  return (
    <ScrollArea className="absolute h-full w-full bg-grey-100 dark:bg-grey-700">
      <div className="flex h-full min-h-svh w-full items-center justify-center overflow-hidden">
        <div className="relative w-full max-w-[498px] rounded-base bg-white-500 p-12 dark:bg-black-500">
          <div className="flex flex-col items-center">
            <Image className="mb-5" src={memberupLogo} width={170} height={25} alt="MemberUp" />
            <div className="mb-2 text-center text-lg font-semibold text-black-700 dark:text-white-500">
              {passwordUpdated ? 'Password updated' : 'Create a new password'}
            </div>
            {passwordUpdated && (
              <p className="mb-6 text-sm font-normal text-black-200 dark:text-black-100">
                Please log in with your new password.
              </p>
            )}
          </div>
          {passwordUpdated ? (
            <Link className={cn(buttonVariants({ variant: 'default' }), 'w-full')} href="/login">
              Log in
            </Link>
          ) : (
            <ChangePasswordForm className="mt-4" onUpdatedPassword={() => setPasswordUpdated(true)} />
          )}
        </div>
      </div>
    </ScrollArea>
  )
}
