import * as Sentry from '@sentry/nextjs'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Intentionally throw an error to test <PERSON><PERSON>
    throw new Error('This is a test error for Sentry integration')
  } catch (error) {
    Sentry.captureException(error)
    return NextResponse.json({ error: 'An error occurred and was reported to Sentry' }, { status: 500 })
  }
}
