import { UpChunk } from '@mux/upchunk'
import _orderBy from 'lodash/orderBy'
import { useCallback, useState } from 'react'
import { v4 as uuidv4 } from 'uuid'

import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { getFileType } from '@memberup/shared/src/libs/file'
import { uploadFileToCloudinaryApi } from '@memberup/shared/src/services/apis/cloudinary.api'
import { TAttachment } from '@memberup/shared/src/types/types'
import { getMuxUploadDataApi } from '@/shared-services/apis/mux.api'

const useFileUpload = (prefix: string, membershipId: string) => {
  const [uploadProgress, setUploadProgress] = useState<number | null>(null)
  const [uploadedFile, setUploadedFile] = useState<TAttachment | null>(null)
  const [cancelObject, setCancelObject] = useState(null)

  const cancelUpload = useCallback(() => {
    cancelObject?.cancel('Upload cancelled by the user')
    setCancelObject(null)
  }, [cancelObject])

  const uploadFile = async (
    file: File,
    uploadTo: 'Mux' | 'Cloudinary',
    generateTranscriptions: boolean = false,
    providedPassthrough: string = null,
  ) => {
    setUploadProgress(0)
    const fileType = getFileType(file)

    const passthrough = providedPassthrough || `${prefix}:${uuidv4()}`

    if (uploadTo === 'Mux' && fileType === 'video') {
      const res = await getMuxUploadDataApi(passthrough, generateTranscriptions, membershipId, true)

      const upload = UpChunk.createUpload({
        endpoint: res.data.data.mux_upload_url,
        file,
        attempts: 3,
        chunkSize: 102400, // Uploads the file in ~100mb chunks
      })

      upload.on('offline', (temp) => {
        console.log('offline', temp)
      })

      upload.on('online', (temp) => {
        console.log('online', temp)
      })

      upload.on('error', (temp) => {
        console.log('error', temp)
      })

      upload.on('progress', (temp) => {
        setUploadProgress(parseInt(temp.detail))
      })

      upload.on('success', (temp) => {
        setUploadProgress(100)
        setUploadedFile({
          mimetype: fileType,
          filename: file.name,
          passthrough,
          mux_upload_id: res.data.data.mux_upload_id,
          uploaded_date: formatDate({
            date: new Date(),
            format: 'EEE, LLL d, yyyy K:mm aa',
          }),
          public_id: res?.data.public_id || (file as any).public_id,
        })
      })

      upload.on('error', (err) => {
        console.error('💥 🙀', err.detail)
      })
    } else {
      const res = await uploadFileToCloudinaryApi(
        file,
        fileType,
        (progress: number) => {
          setUploadProgress(Math.ceil(progress * 100))
        },
        (source: any) => {
          setCancelObject(source)
        },
      )

      if (res?.data) {
        return {
          id: res?.data.public_id || (file as any).public_id,
          filename: file.name,
          mimetype: fileType,
          passthrough,
          url: res.data.secure_url as string,
          uploaded_date: formatDate({
            date: new Date(),
            format: 'EEE, LLL d, yyyy K:mm aa',
          }),
          thumbnail: res?.data.thumbnail || (file as any).thumbnail,
          size_in_bytes: res?.data.size_in_bytes || (file as any).size_in_bytes || file.size,
          public_id: res?.data.public_id || (file as any).public_id,
        }
      } else {
        return null
      }
    }
  }

  return {
    cancelUpload,
    uploadFile,
    uploadedFile,
    uploadProgress,
  }
}

export default useFileUpload
