import { useRouter } from 'next/navigation'

import { useStore } from './useStore'
import { resetState } from '@/memberup/store/features/feedAggregationSlice'
import { getMembershipSuccess } from '@/memberup/store/features/membershipSlice'
import { useAppDispatch } from '@/memberup/store/store'
import { IMembership, IMembershipSetting, IUserMembership } from '@/shared-types/interfaces'

export default function useSwitchCommunity() {
  const setMembership = useStore((state) => state.community.setMembership)
  const user = useStore((state) => state.auth.user)
  const dispatch = useAppDispatch()
  const setLoadingMessages = useStore((state) => state?.community?.setLoadingMessages)
  const router = useRouter()

  return (
    communitySlug: string,
    membershipsFromFetch?: Array<IUserMembership>, // Replace IUserMembership with your actual type
  ) => {
    // Use the global user memberships if available; otherwise, fall back to the provided list.
    const userMemberships = user?.user_memberships || membershipsFromFetch
    if (!userMemberships) {
      console.warn('No user memberships available to switch community.')
      return
    }

    let targetMembership = userMemberships.find((um) => um.membership.slug === communitySlug)
    if (!targetMembership) {
      console.warn(`Cannot find a community with the provided slug of ${communitySlug}.`)
      return
    }

    setLoadingMessages(true)
    setMembership(targetMembership.membership as IMembership)
    // Reset the feed aggregation state

    // CHECK: This should be removed when completing the migration to state.community.
    // dispatch(
    //   getMembershipSuccess({
    //     membership: targetMembership.membership as IMembership,
    //     membership_setting: targetMembership.membership.membership_setting as IMembershipSetting,
    //     owner: null,
    //   })
    // )
    router.push(`/${communitySlug}`, { shallow: true })
  }
}
