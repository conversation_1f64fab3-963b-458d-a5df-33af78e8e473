import { PERSONALITY_TYPE_ENUM } from '@prisma/client'

import { TColorPickerContrastReferenceColor } from '@/shared-types/types'

export type TMbtiType = {
  label: string
  value: PERSONALITY_TYPE_ENUM
  description: string
}

export const mbtiTypes: TMbtiType[] = [
  { label: '', value: 'DS', description: '' },
  { label: 'ISTJ', value: 'ISTJ', description: 'Introversion, Sensing, Thinking, Judgment' },
  { label: 'ISTP', value: 'ISTP', description: 'Introversion, Sensing, Thinking, Perception' },
  { label: 'ISFJ', value: 'ISFJ', description: 'Introversion, Sensing, Feeling, Judgment' },
  { label: 'INFJ', value: 'INFJ', description: 'Introversion, Intuition, Feeling, Judgment' },
  { label: 'INFP', value: 'INFP', description: 'Introversion, Intuition, Feeling, Perception' },
  { label: 'INTJ', value: 'INTJ', description: 'Introversion, Intuition, Thinking, Judgment' },
  { label: 'INTP', value: 'INTP', description: 'Introversion, Intuition, Thinking, Perception' },
  { label: 'ESTP', value: 'ESTP', description: 'Extraversion, Sensing, Thinking, Perception' },
  { label: 'ESTJ', value: 'ESTJ', description: 'Extraversion, Sensing, Thinking, Judgment' },
  { label: 'ESFP', value: 'ESFP', description: 'Extraversion, Sensing, Feeling, Perception' },
  { label: 'ESFJ', value: 'ESFJ', description: 'Extraversion, Sensing, Feeling, Judgment' },
  { label: 'ENFP', value: 'ENFP', description: 'Extraversion, Intuition, Feeling, Perception' },
  { label: 'ENFJ', value: 'ENFJ', description: 'Extraversion, Intuition, Feeling, Judgment' },
  { label: 'ENTP', value: 'ENTP', description: 'Extraversion, Intuition, Thinking, Perception' },
  { label: 'ENTJ', value: 'ENTJ', description: 'Extraversion, Intuition, Thinking, Judgment' },
]

export const paymentTypes = [
  { label: 'Monthly (recommended)', value: 'month', description: 'Monthly' },
  { label: 'Yearly', value: 'year', description: 'Yearly' },
  { label: 'One time', value: 'one-time', description: 'One time' },
]

export type TProfileSocialLink = {
  value: string
  label: string
  type: 'url' | 'username'
  maxLength: number
}

export const coverPictureMinWidth = 800
export const coverPictureMinHeight = 450

export const maxBioLength = 150

export const profilePictureMinWidth = 500
export const profilePictureMinHeight = 500

export const profileSocialLinks: TProfileSocialLink[] = [
  { value: 'website', label: 'Website', type: 'url', maxLength: 255 },
  { value: 'facebook', label: 'Facebook', type: 'username', maxLength: 50 },
  { value: 'instagram', label: 'Instagram', type: 'username', maxLength: 30 },
  { value: 'youtube', label: 'YouTube', type: 'username', maxLength: 36 },
  { value: 'linkedin', label: 'LinkedIn', type: 'username', maxLength: 100 },
  { value: 'tiktok', label: 'TikTok', type: 'username', maxLength: 24 },
  { value: 'x', label: 'X', type: 'username', maxLength: 15 },
]

export const nonCommunityPathnames = [
  '/billing-confirmed',
  '/change-password',
  '/create-community',
  '/login',
  '/notifications',
  '/reset-password',
  '/signup',
  '/settings',
  '/chat',
  '/@',
]

export const fullscreenLayoutPathnames = ['/content/course/', '/content/course-builder/']

export const openCommunityPathnames = ['/about', '/join']

export const defaultCommunityColors = [
  '#d452ff',
  '#7b00ff',
  '#1869e9',
  '#538256',
  '#b68050',
  '#8f8c73',
  '#6e6eb6',
  '#be6ace',
]

export const initialColorPickerHex = '#d452ff'

export const colorPickerContrastReferenceColors: TColorPickerContrastReferenceColor[] = [
  { color: '#FFFFFF', minColorContrastRatio: 3.2 },
  { color: '#16181C', minColorContrastRatio: 1.5 },
]

export const saturationAreaWidth = 222

export const saturationAreaHeight = 160

export const legalLinks = [
  { href: '/privacy-policy', label: 'Privacy policy' },
  { href: '/terms-and-conditions', label: 'Terms & conditions' },
  { href: '/cookie-policy', label: 'Cookie policy' },
  { href: '/transaction-terms', label: 'Transaction terms' },
  { href: '/acceptable-use', label: 'Acceptable use' },
]
