import { USER_MEMBERSHIP_STATUS_ENUM } from '@memberup/shared/src/types/enum'

export const membershipPublicFields = {
  id: true,
  name: true,
  slug: true,
  createdAt: true,
  active: true,
}

export const ownerPublicFields = {
  id: true,
  first_name: true,
  last_name: true,
  createdAt: true,
  username: true,
}

export const ownerProfilePublicFields = {
  id: true,
  createdAt: true,
  image: true,
  image_crop_area: true,
}

export const membershipSettingsPublicFields = {
  about_gallery: true,
  about_text: true,
  completed_membership: true,
  community_idea: false,
  cover_image: true,
  cover_image_crop_area: true,
  description: true,
  external_links: true,
  favicon: true,
  favicon_crop_area: true,
  intro_html: false,
  intro_closed_html: false,
  logo: true,
  logo_crop_area: true,
  library: true,
  members_count: true,
  onboarding: true,
  spark_expire_time: true,
  time_zone: true,
  theme_main_color: true,
  theme_secondary_color: true,
  visibility: true,
  stripe_product_id: true,
  stripe_connect_account: true,
  stripe_prices: true,
  form_enabled: true,
  is_pricing_enabled: true,
  form: true,
  support_email: true,
  spark_enabled: true,
  spark_current_membership_category_id: true,
  spark_current_membership_question_instance_id: true,
}

export const ownerQueryOptions = {
  select: {
    ...ownerPublicFields,
    profile: {
      select: {
        ...ownerProfilePublicFields,
      },
    },
  },
}

export const membershipQueryOptions = {
  select: {
    ...membershipPublicFields,
    membership_setting: {
      select: {
        ...membershipSettingsPublicFields,
      },
    },
    owner: {
      ...ownerQueryOptions,
    },
    channels: {
      select: {
        id: true,
        slug: true,
        name: true,
        description: true,
        is_private: true,
        visibility: true,
      },
    },
  },
}

export const membershipMemberListingQueryOptions = {
  where: {
    status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
  },
  select: {
    user_role: true,
    createdAt: true,
    owner: ownerQueryOptions,
    user: {
      select: {
        id: true,
        email: false,
        first_name: true,
        last_name: true,
        membership_id: true,
        status: true,
        createdAt: true,
        invite_token: true,
        username: true,
        profile: {
          select: {
            image: true,
            image_crop_area: true,
            phone_number: false,
            bio: true,
            location: true,
            last_activity_at: true,
          },
        },
      },
    },
  },
}
