import { signOut } from 'next-auth/react'
import { usePathname, useRouter } from 'next/navigation'
import { usePostHog } from 'posthog-js/react'

import { canAccessCommunityPath } from './authorization'
import { useStore } from '@/hooks/useStore'
import { UserEvents } from '@/lib/posthog'
import useAppCookie from '@/memberup/components/hooks/use-app-cookie'
import { clearLikes } from '@/memberup/store/features/likesSlice'
import { resetDialogs } from '@/memberup/store/features/uiSlice'
import { useAppDispatch } from '@/memberup/store/hooks'
import { resetUser } from '@/memberup/store/store'

export const useLogout = () => {
  const dispatch = useAppDispatch()
  const { deleteAppCookie } = useAppCookie()
  const setUnautenticatedState = useStore((state) => state.auth.setUnauthenticatedState)
  const membership = useStore((state) => state.community.membership)
  const pathname = usePathname()
  const router = useRouter()
  const posthog = usePostHog()

  const logout = async () => {
    if (pathname.startsWith(`/${membership.slug}`)) {
      if (!canAccessCommunityPath(null, membership, pathname)) {
        router.push(`/${membership.slug}/about`)
      }
    }
    posthog.capture(UserEvents.LOGGED_OUT)
    await signOut({ redirect: false })
    setUnautenticatedState()
    dispatch(resetUser({}))
    dispatch(clearLikes())
    dispatch(resetDialogs())
    deleteAppCookie()
    posthog.reset()
  }

  return logout
}
