import { z } from 'zod'

export const communityGalleryDataSchema = z
  .array(
    z.object({
      id: z.string(),
      asset_id: z.string().optional(),
      cropArea: z
        .object({
          height: z.number(),
          width: z.number(),
          x: z.number(),
          y: z.number(),
        })
        .optional(),
      filename: z.string().optional(),
      format: z.string().optional(),
      height: z.number().optional(),
      width: z.number().optional(),
      url: z.string().optional(),
      mimetype: z.string().optional(),
      mux_upload_id: z.string().optional(),
      passthrough: z.string().optional(),
    }),
  )
  .max(7)

export function validateCommunityGalleryData(data: any) {
  return communityGalleryDataSchema.safeParse(data)
}
