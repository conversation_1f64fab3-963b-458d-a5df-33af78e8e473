import Stripe from 'stripe'

import { STRIPE_SECRET_KEY } from '@/shared-config/envs'
import { getFullName } from '@/shared-libs/profile'
import { stripeGetOrCreateCustomerMain } from '@/shared-libs/stripe'
import { IUser } from '@/shared-types/interfaces'

export const generateStripeCustomer = async (user: IUser) => {
  const userFullName = getFullName(user.first_name, user.last_name)

  const stripeCustomerPayload = {
    email: user.email,
    description: userFullName,
    name: userFullName,
    metadata: {
      user_id: user.id,
      user_name: user.first_name,
      user_last_name: user.last_name,
      user_email: user.email,
    },
  }

  return await stripeGetOrCreateCustomerMain(STRIPE_SECRET_KEY, stripeCustomerPayload)
}

export const formatCommunitySubscriptionPricing = (subscription: Stripe.Subscription) => {
  const interval = subscription.items.data[0]?.price?.recurring?.interval || subscription.items.data[0]?.plan?.interval

  switch (interval) {
    case 'month':
      return 'Monthly'
    case 'year':
      return 'Yearly'
    default:
      return 'One-time'
  }
}

export const formatCommunitySubscriptionAmountSuffix = (subscription: Stripe.Subscription) => {
  const interval = subscription.items.data[0]?.price?.recurring?.interval || subscription.items.data[0]?.plan?.interval

  switch (interval) {
    case 'month':
      return '/month'
    case 'year':
      return '/year'
    default:
      return '- One-time'
  }
}

export const formatPaymentMethod = (paymentMethod: Stripe.PaymentMethod) => {
  if (paymentMethod.type === 'card' && paymentMethod.card) {
    return `${paymentMethod.card.brand.toUpperCase()} ending in ${paymentMethod.card.last4}`
  }

  return paymentMethod.type
}
