export function getVideoScreenshot(
  videoFile: File,
  timeInSeconds: number = 1,
  format: string = 'image/jpeg',
  quality: number = 0.95,
): Promise<string> {
  return new Promise<string>((resolve, reject) => {
    const videoUrl: string = URL.createObjectURL(videoFile)

    const video: HTMLVideoElement = document.createElement('video')

    video.preload = 'metadata'
    video.muted = true
    video.playsInline = true

    video.onloadedmetadata = (): void => {
      const captureTime: number = Math.min(timeInSeconds, video.duration)
      video.currentTime = captureTime
    }

    video.onseeked = (): void => {
      const canvas: HTMLCanvasElement = document.createElement('canvas')
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      const ctx: CanvasRenderingContext2D | null = canvas.getContext('2d')

      if (!ctx) {
        URL.revokeObjectURL(videoUrl)
        reject(new Error('Failed to get canvas context'))
        return
      }

      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

      const dataUrl: string = canvas.toDataURL(format, quality)

      URL.revokeObjectURL(videoUrl)

      resolve(dataUrl)
    }

    video.onerror = (): void => {
      URL.revokeObjectURL(videoUrl)
      reject(new Error('Failed to load video'))
    }

    video.src = videoUrl
  })
}
