import { IAuthenticatedUser, IMembership, IUser } from '@/shared-types/interfaces'

export const canAccessFullCommunity = (user: IUser | IAuthenticatedUser, membership: IMembership) => {
  const { membership_setting: membershipSetting } = membership

  if (membershipSetting.visibility === 'public') {
    return true
  }

  if (!user) {
    return false
  }

  const userMembership = user.user_memberships.find((userMembership) => userMembership.membership.id === membership.id)

  return userMembership && userMembership.status === 'accepted'
}

export const canAccessCommunityPath = (user: IUser | IAuthenticatedUser, membership: IMembership, pathname: string) => {
  if (user && user.status === 'banned') {
    return false
  }

  if ([`/${membership.slug}/about` || `/${membership.slug}/about`].includes(pathname)) {
    return true
  }

  return canAccessFullCommunity(user, membership)
}
