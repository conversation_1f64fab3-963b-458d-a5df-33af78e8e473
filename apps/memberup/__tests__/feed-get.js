/**
 * @jest-environment node
 */

import authenticationMiddleware from '../src/middlewares/authentication'
import { adminMock, memberMock, mockAuthentication } from '../test-common'
import { findFeedByPermalink } from '@memberup/shared/src/libs/prisma/feed'
import handler from '@/memberup/pages/api/feed/index'

jest.mock('../src/middlewares/authentication')

jest.mock('@memberup/shared/src/libs/prisma/feed', () => ({
  createFeed: jest.fn(),
  findFeedByPermalink: jest.fn(),
  findFeeds: jest.fn(),
}))

jest.mock('@mux/mux-node')

describe('/api/feed', () => {
  let res
  beforeEach(() => {
    authenticationMiddleware.mockReset()
    findFeedByPermalink.mockReset()

    res = {
      status: jest.fn(() => res),
      json: jest.fn(),
    }
  })

  it('GET should return 400 when not providing the permalink', async () => {
    mockAuthentication(authenticationMiddleware, memberMock)
    const request = {
      method: 'GET',
      url: '/api/feed',
      body: {},
      query: {},
      user: {
        membership_id: 1,
      },
    }

    await handler(request, res)

    expect(res.status).toHaveBeenCalledWith(400)
    expect(res.json).toHaveBeenCalledWith({
      message: 'You must specify a permalink.',
    })
  })

  it('GET should return 404 when the post is not found', async () => {
    mockAuthentication(authenticationMiddleware, memberMock)
    // Simulate a post not found.
    findFeedByPermalink.mockImplementation(() => null)
    const request = {
      method: 'GET',
      url: '/api/feed',
      body: {},
      query: { permalink: 'some-post' },
      user: {
        membership_id: 1,
      },
    }

    await handler(request, res)

    expect(res.status).toHaveBeenCalledWith(404)
    expect(res.json).toHaveBeenCalledWith({
      message: 'Post not found.',
    })
  })
})
